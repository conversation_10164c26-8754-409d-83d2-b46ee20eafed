# Zoobbe Admin Frontend

This is the frontend application for the Zoobbe Admin Dashboard, providing a user interface for managing users, workspaces, and system statistics.

## Features

- Admin authentication and authorization
- Dashboard with system statistics
- User management
- Workspace management
- Activity log viewer

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Zoobbe Admin Backend running

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file in the root directory with the following variables:
   ```
   VITE_API_URL=http://localhost:5001/api
   ```

## Running the Application

### Development Mode
```
npm run dev
```

### Production Build
```
npm run build
```

### Preview Production Build
```
npm run preview
```

## Project Structure

```
src/
├── assets/         # Static assets
├── components/     # Reusable components
│   ├── auth/       # Authentication components
│   ├── navigation/ # Navigation components
│   └── ui/         # UI components
├── context/        # React context providers
├── layouts/        # Page layouts
├── pages/          # Page components
├── services/       # API services
├── store/          # Redux store
│   └── slices/     # Redux slices
└── utils/          # Utility functions
```

## Authentication

The application uses JWT-based authentication. The admin token is stored in localStorage and included in API requests via an Axios interceptor.

## State Management

Redux Toolkit is used for state management. The store is organized into the following slices:

- `auth` - Authentication state
- `users` - User management state
- `workspaces` - Workspace management state
- `stats` - System statistics state
- `activity` - Activity log state

## Styling

The application uses CSS variables for theming. The main styles are defined in `App.css`.

## License

ISC

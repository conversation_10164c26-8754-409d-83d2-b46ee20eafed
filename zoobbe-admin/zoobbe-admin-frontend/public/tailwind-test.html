<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tailwind CSS Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
          'mono': ['"Intel One Mono"', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
        },
        extend: {
          colors: {
            slate: {
              50: '#f8fafc',
              100: '#f1f5f9',
              200: '#e2e8f0',
              300: '#cbd5e1',
              400: '#94a3b8',
              500: '#64748b',
              600: '#475569',
              700: '#334155',
              800: '#1e293b',
              900: '#0f172a',
              950: '#020617',
            },
          }
        }
      }
    }
  </script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Intel One Mono - Regular */
    @font-face {
      font-family: 'Intel One Mono';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff2/IntelOneMono-Regular.woff2') format('woff2'),
           url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff/IntelOneMono-Regular.woff') format('woff');
    }
  </style>
</head>
<body class="bg-gray-100 p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold text-slate-800 mb-6">Tailwind CSS Test Page</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <!-- Card 1 -->
      <div class="bg-white rounded-xl shadow-soft p-6 border border-gray-200">
        <h2 class="text-xl font-semibold text-slate-700 mb-4">Regular Font</h2>
        <p class="text-slate-600">This text uses the Inter font family which is set as the default sans-serif font.</p>
        <div class="mt-4 flex space-x-2">
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">Button</button>
          <button class="bg-slate-200 hover:bg-slate-300 text-slate-700 px-4 py-2 rounded-lg transition-colors">Cancel</button>
        </div>
      </div>
      
      <!-- Card 2 -->
      <div class="bg-white rounded-xl shadow-soft p-6 border border-gray-200">
        <h2 class="text-xl font-semibold text-slate-700 mb-4">Monospace Font</h2>
        <p class="font-mono text-slate-600">This text uses the Intel One Mono font family which is set as the default monospace font.</p>
        <div class="mt-4 p-3 bg-slate-100 rounded-lg">
          <code class="font-mono text-sm text-slate-800">console.log("Hello, Tailwind!");</code>
        </div>
      </div>
    </div>
    
    <!-- Color Palette -->
    <div class="bg-white rounded-xl shadow-soft p-6 border border-gray-200 mb-8">
      <h2 class="text-xl font-semibold text-slate-700 mb-4">Color Palette</h2>
      <div class="grid grid-cols-5 gap-2">
        <div class="h-12 rounded bg-slate-50 flex items-center justify-center text-xs text-slate-800">50</div>
        <div class="h-12 rounded bg-slate-100 flex items-center justify-center text-xs text-slate-800">100</div>
        <div class="h-12 rounded bg-slate-200 flex items-center justify-center text-xs text-slate-800">200</div>
        <div class="h-12 rounded bg-slate-300 flex items-center justify-center text-xs text-slate-800">300</div>
        <div class="h-12 rounded bg-slate-400 flex items-center justify-center text-xs text-slate-800">400</div>
        <div class="h-12 rounded bg-slate-500 flex items-center justify-center text-xs text-white">500</div>
        <div class="h-12 rounded bg-slate-600 flex items-center justify-center text-xs text-white">600</div>
        <div class="h-12 rounded bg-slate-700 flex items-center justify-center text-xs text-white">700</div>
        <div class="h-12 rounded bg-slate-800 flex items-center justify-center text-xs text-white">800</div>
        <div class="h-12 rounded bg-slate-900 flex items-center justify-center text-xs text-white">900</div>
      </div>
    </div>
    
    <!-- Dark Mode Toggle -->
    <div class="bg-white rounded-xl shadow-soft p-6 border border-gray-200">
      <h2 class="text-xl font-semibold text-slate-700 mb-4">Dark Mode Toggle</h2>
      <p class="mb-4">Click the button below to toggle dark mode:</p>
      <button id="darkModeToggle" class="bg-slate-800 hover:bg-slate-700 text-white px-4 py-2 rounded-lg transition-colors">
        Toggle Dark Mode
      </button>
    </div>
  </div>
  
  <script>
    // Dark mode toggle functionality
    const darkModeToggle = document.getElementById('darkModeToggle');
    darkModeToggle.addEventListener('click', () => {
      document.documentElement.classList.toggle('dark');
      if (document.documentElement.classList.contains('dark')) {
        document.body.classList.remove('bg-gray-100');
        document.body.classList.add('bg-slate-900', 'text-white');
      } else {
        document.body.classList.add('bg-gray-100');
        document.body.classList.remove('bg-slate-900', 'text-white');
      }
    });
  </script>
</body>
</html>

.feature-flags-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
}

.filters {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
}

.flag-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.flag-description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flag-rollout {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rollout-bar {
  flex: 1;
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.rollout-progress {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
}

.text-muted {
  color: var(--text-light);
  font-style: italic;
}

/* Feature Flag Detail */
.feature-flag-detail {
  padding: 20px;
}

.flag-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.flag-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.flag-header-left h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
}

.flag-actions {
  display: flex;
  gap: 12px;
}

.flag-section {
  margin-bottom: 24px;
}

.flag-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.flag-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.info-item {
  margin-bottom: 16px;
}

.info-label {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 4px;
}

.info-value {
  font-weight: 500;
}

.rollout-slider {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.slider-label {
  font-weight: 500;
}

.slider-value {
  font-weight: 600;
  color: var(--primary-color);
}

.slider-container {
  position: relative;
  height: 16px;
  background-color: var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.slider-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 8px;
}

.slider-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background-color: var(--primary-color);
  border: 2px solid var(--card-bg);
  border-radius: 50%;
  cursor: pointer;
  z-index: 1;
}

.target-section {
  margin-top: 24px;
}

.target-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.target-tab {
  padding: 12px 16px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.target-tab.active {
  border-bottom-color: var(--primary-color);
  color: var(--primary-color);
}

.target-content {
  padding: 16px 0;
}

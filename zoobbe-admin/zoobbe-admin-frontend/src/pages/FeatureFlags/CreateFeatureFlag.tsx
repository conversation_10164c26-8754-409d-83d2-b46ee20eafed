import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { RiArrowLeftLine } from 'react-icons/ri';
import { 
  Card, 
  Button, 
  Form,
  Input,
  Textarea,
  Select,
  Checkbox,
  Alert
} from '../../components/ui';
import { createFeatureFlag } from '../../services/api';

const CreateFeatureFlag: React.FC = () => {
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    enabled: false,
    rolloutPercentage: 0,
    isExperimental: true,
    targetPlans: [] as string[]
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' 
          ? Number(value) 
          : value
    }));
  };
  
  const handlePlanChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    
    setFormData(prev => {
      if (checked) {
        return {
          ...prev,
          targetPlans: [...prev.targetPlans, value]
        };
      } else {
        return {
          ...prev,
          targetPlans: prev.targetPlans.filter(plan => plan !== value)
        };
      }
    });
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const response = await createFeatureFlag(formData);
      navigate(`/feature-flags/${response.featureFlag._id}`, { 
        state: { message: 'Feature flag created successfully' } 
      });
    } catch (err) {
      setError('Failed to create feature flag. Please try again.');
      console.error('Error creating feature flag:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleGoBack = () => {
    navigate('/feature-flags');
  };
  
  return (
    <div className="feature-flag-detail">
      <div className="flag-header">
        <div className="flag-header-left">
          <Button 
            variant="outline" 
            icon={<RiArrowLeftLine />}
            onClick={handleGoBack}
          >
            Back
          </Button>
          <h1>Create Feature Flag</h1>
        </div>
      </div>
      
      <Card>
        {error && (
          <Alert variant="error" title="Error" onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <Form onSubmit={handleSubmit}>
          <div className="flag-section">
            <h2>Flag Details</h2>
            
            <Input
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="e.g., new-dashboard-layout"
              required
              fullWidth
            />
            
            <Textarea
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Describe what this feature flag controls"
              fullWidth
            />
            
            <div className="form-row">
              <Checkbox
                label="Enable this feature flag"
                name="enabled"
                checked={formData.enabled}
                onChange={handleInputChange as any}
              />
              
              <Checkbox
                label="This is an experimental feature"
                name="isExperimental"
                checked={formData.isExperimental}
                onChange={handleInputChange as any}
              />
            </div>
          </div>
          
          <div className="flag-section">
            <h2>Rollout Configuration</h2>
            
            <div className="rollout-slider">
              <div className="slider-header">
                <div className="slider-label">Rollout Percentage</div>
                <div className="slider-value">{formData.rolloutPercentage}%</div>
              </div>
              
              <Input
                type="range"
                name="rolloutPercentage"
                min="0"
                max="100"
                value={formData.rolloutPercentage.toString()}
                onChange={handleInputChange}
                fullWidth
              />
              
              <p className="text-muted">
                This controls what percentage of users will see this feature.
              </p>
            </div>
          </div>
          
          <div className="flag-section">
            <h2>Target Plans</h2>
            
            <div className="checkbox-group">
              <Checkbox
                label="Free"
                name="targetPlans"
                value="free"
                checked={formData.targetPlans.includes('free')}
                onChange={handlePlanChange}
              />
              
              <Checkbox
                label="Standard"
                name="targetPlans"
                value="standard"
                checked={formData.targetPlans.includes('standard')}
                onChange={handlePlanChange}
              />
              
              <Checkbox
                label="Premium"
                name="targetPlans"
                value="premium"
                checked={formData.targetPlans.includes('premium')}
                onChange={handlePlanChange}
              />
              
              <Checkbox
                label="Enterprise"
                name="targetPlans"
                value="enterprise"
                checked={formData.targetPlans.includes('enterprise')}
                onChange={handlePlanChange}
              />
            </div>
            
            <p className="text-muted">
              If no plans are selected, the feature will be available to all plans based on the rollout percentage.
            </p>
          </div>
          
          <div className="form-actions">
            <Button 
              variant="outline" 
              type="button"
              onClick={handleGoBack}
            >
              Cancel
            </Button>
            <Button 
              variant="primary" 
              type="submit"
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Feature Flag'}
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default CreateFeatureFlag;

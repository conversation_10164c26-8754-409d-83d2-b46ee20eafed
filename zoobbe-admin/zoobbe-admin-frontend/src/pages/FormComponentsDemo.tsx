import React, { useState } from 'react';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import Select from '../components/ui/Select';
import Textarea from '../components/ui/Textarea';
import { RiSearchLine, RiMailLine, RiLockLine, RiEyeLine, RiEyeOffLine } from 'react-icons/ri';

const FormComponentsDemo = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    message: '',
    country: '',
    plan: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('Form submitted with data: ' + JSON.stringify(formData, null, 2));
  };

  const countryOptions = [
    { value: 'us', label: 'United States' },
    { value: 'ca', label: 'Canada' },
    { value: 'uk', label: 'United Kingdom' },
    { value: 'au', label: 'Australia' },
    { value: 'de', label: 'Germany' },
    { value: 'fr', label: 'France' },
    { value: 'jp', label: 'Japan' }
  ];

  const planOptions = [
    { value: 'free', label: 'Free Plan' },
    { value: 'basic', label: 'Basic Plan' },
    { value: 'pro', label: 'Pro Plan' },
    { value: 'enterprise', label: 'Enterprise Plan' }
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">Form Components</h1>
        <p className="text-gray-600 dark:text-gray-400">Modern form components styled with Tailwind CSS</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Input Components */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">Input Components</h2>
          
          <div className="space-y-6">
            <Input
              label="Default Input"
              placeholder="Enter text"
              fullWidth
            />
            
            <Input
              label="Input with Left Icon"
              placeholder="Search..."
              leftIcon={<RiSearchLine />}
              fullWidth
            />
            
            <Input
              label="Input with Right Icon"
              placeholder="Enter email"
              rightIcon={<RiMailLine />}
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              fullWidth
            />
            
            <Input
              label="Password Input"
              placeholder="Enter password"
              leftIcon={<RiLockLine />}
              rightIcon={
                <div className="cursor-pointer" onClick={() => setShowPassword(!showPassword)}>
                  {showPassword ? <RiEyeOffLine /> : <RiEyeLine />}
                </div>
              }
              type={showPassword ? "text" : "password"}
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              fullWidth
            />
            
            <Input
              label="Input with Error"
              placeholder="Enter text"
              error="This field is required"
              fullWidth
            />
            
            <Input
              label="Disabled Input"
              placeholder="This input is disabled"
              disabled
              fullWidth
            />
            
            <div className="grid grid-cols-3 gap-4">
              <Input
                label="Small"
                placeholder="Small"
                size="sm"
                fullWidth
              />
              
              <Input
                label="Medium"
                placeholder="Medium"
                size="md"
                fullWidth
              />
              
              <Input
                label="Large"
                placeholder="Large"
                size="lg"
                fullWidth
              />
            </div>
          </div>
        </div>
        
        {/* Select and Textarea Components */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">Select & Textarea</h2>
          
          <div className="space-y-6">
            <Select
              label="Default Select"
              options={countryOptions}
              placeholder="Select a country"
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              fullWidth
            />
            
            <Select
              label="Select with Error"
              options={planOptions}
              placeholder="Select a plan"
              error="Please select a plan"
              fullWidth
            />
            
            <Select
              label="Disabled Select"
              options={countryOptions}
              placeholder="Select a country"
              disabled
              fullWidth
            />
            
            <div className="grid grid-cols-3 gap-4">
              <Select
                label="Small"
                options={planOptions}
                size="sm"
                fullWidth
              />
              
              <Select
                label="Medium"
                options={planOptions}
                size="md"
                fullWidth
              />
              
              <Select
                label="Large"
                options={planOptions}
                size="lg"
                fullWidth
              />
            </div>
            
            <Textarea
              label="Default Textarea"
              placeholder="Enter your message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              fullWidth
            />
            
            <Textarea
              label="Textarea with Error"
              placeholder="Enter your message"
              error="This field is required"
              fullWidth
            />
            
            <Textarea
              label="Disabled Textarea"
              placeholder="This textarea is disabled"
              disabled
              fullWidth
            />
          </div>
        </div>
      </div>
      
      {/* Button Components */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mt-8">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">Button Components</h2>
        
        <div className="space-y-6">
          <div className="flex flex-wrap gap-4">
            <Button variant="primary">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="success">Success</Button>
            <Button variant="danger">Danger</Button>
            <Button variant="warning">Warning</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
          </div>
          
          <div className="flex flex-wrap gap-4">
            <Button variant="primary" size="small">Small</Button>
            <Button variant="primary" size="medium">Medium</Button>
            <Button variant="primary" size="large">Large</Button>
          </div>
          
          <div className="flex flex-wrap gap-4">
            <Button variant="primary" leftIcon={<RiMailLine />}>With Left Icon</Button>
            <Button variant="primary" rightIcon={<RiMailLine />}>With Right Icon</Button>
            <Button variant="primary" isLoading>Loading</Button>
            <Button variant="primary" disabled>Disabled</Button>
          </div>
          
          <div className="flex flex-wrap gap-4">
            <Button variant="primary" fullWidth>Full Width Button</Button>
          </div>
        </div>
      </div>
      
      {/* Form Example */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mt-8">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">Form Example</h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <Input
            label="Full Name"
            placeholder="Enter your full name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            fullWidth
            required
          />
          
          <Input
            label="Email Address"
            placeholder="Enter your email"
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            leftIcon={<RiMailLine />}
            fullWidth
            required
          />
          
          <Select
            label="Select Plan"
            options={planOptions}
            name="plan"
            value={formData.plan}
            onChange={handleInputChange}
            fullWidth
            required
          />
          
          <Textarea
            label="Message"
            placeholder="Enter your message"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            fullWidth
            required
          />
          
          <div className="flex justify-end gap-4">
            <Button variant="outline" type="reset">Reset</Button>
            <Button variant="primary" type="submit">Submit</Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FormComponentsDemo;

import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  fetchSystemStats,
  fetchUserGrowthStats,
  fetchWorkspaceGrowthStats,
  fetchSubscriptionStats,
  fetchActivityStats
} from '../store/slices/statsSlice';
import {
  RiUser3Line,
  RiTeamLine,
  RiLayoutMasonryLine,
  RiFileList3Line,
  RiVipCrownLine,
  RiHistoryLine,
  RiCheckboxCircleLine,
  RiErrorWarningLine,
  RiAlertLine
} from 'react-icons/ri';


const Dashboard = () => {
  const dispatch = useAppDispatch();
  const {
    data,
    userGrowth,
    workspaceGrowth,
    loading,
    error
  } = useAppSelector((state) => state.stats);

  const [statsPeriod, setStatsPeriod] = useState('month');

  useEffect(() => {
    dispatch(fetchSystemStats());
    dispatch(fetchUserGrowthStats(statsPeriod as any));
    dispatch(fetchWorkspaceGrowthStats(statsPeriod as any));
    dispatch(fetchSubscriptionStats(statsPeriod as any));
    dispatch(fetchActivityStats('week' as any));
  }, [dispatch, statsPeriod]);

  // Health status icon and color
  const getHealthStatusInfo = (status: string | undefined) => {
    switch (status?.toLowerCase()) {
      case 'healthy':
        return {
          icon: <RiCheckboxCircleLine className="h-6 w-6" />,
          color: 'text-green-500',
          bgColor: 'bg-green-100'
        };
      case 'warning':
        return {
          icon: <RiAlertLine className="h-6 w-6" />,
          color: 'text-amber-500',
          bgColor: 'bg-amber-100'
        };
      case 'critical':
        return {
          icon: <RiErrorWarningLine className="h-6 w-6" />,
          color: 'text-rose-500',
          bgColor: 'bg-rose-100'
        };
      default:
        return {
          icon: <RiAlertLine className="h-6 w-6" />,
          color: 'text-slate-500',
          bgColor: 'bg-slate-100'
        };
    }
  };

  const healthStatus = getHealthStatusInfo(data?.systemHealth?.status);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        <span className="ml-3 text-lg font-medium text-gray-300">Loading dashboard data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border-l-4 border-red-500 p-4 rounded-md m-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <RiErrorWarningLine className="h-5 w-5 text-red-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-300">Error loading dashboard</h3>
            <div className="mt-2 text-sm text-red-400">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div id="dashboard-container" className="space-y-8 animate-fadeIn px-6 py-8">
      <div id="dashboard-header" className="mb-6">
        <h1 className="text-3xl font-bold text-white">Dashboard Overview</h1>
        <p className="mt-2 text-gray-400">
          A summary of your platform's performance and key metrics
        </p>
      </div>

      {/* Stats Cards */}
      <div id="stats-cards-container" className="grid grid-cols-2 gap-4 sm:grid-cols-2 lg:grid-cols-3 dashboard-section stats-grid">
        {/* Users Card */}
        <div id="users-card" className="bg-slate-900 overflow-hidden rounded border border-slate-800 transition-all duration-300 hover:translate-y-[-2px] group stat-card">
          <div className="p-6 relative overflow-hidden card-header">
            <div className="absolute top-0 right-0 w-32 h-32 bg-indigo-500 opacity-10 rounded-full -mr-10 -mt-10 group-hover:scale-110 transition-transform duration-500"></div>
            <div className="flex items-center relative">
              <div className="flex-shrink-0 bg-indigo-900/30 rounded p-3 group-hover:bg-indigo-800/40 transition-colors duration-300 card-icon">
                <RiUser3Line className="h-7 w-7 text-indigo-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Total Users</dt>
                  <dd>
                    <div className="text-3xl font-bold text-white font-mono">{data?.users?.total || 0}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-slate-900 px-6 py-3 border-t border-slate-800/50 card-details">
            <div className="text-xs">
              <div className="grid grid-cols-3 gap-2">
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Active</div>
                  <div className="text-indigo-400 font-medium font-mono">{data?.users?.active || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">New (30d)</div>
                  <div className="text-indigo-400 font-medium font-mono">{data?.users?.new || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Monthly</div>
                  <div className="text-indigo-400 font-medium font-mono">{data?.users?.monthlyActive || 0}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Workspaces Card */}
        <div id="workspaces-card" className="bg-slate-900 overflow-hidden rounded border border-slate-800 transition-all duration-300 hover:translate-y-[-2px] group stat-card">
          <div className="p-6 relative overflow-hidden card-header">
            <div className="absolute top-0 right-0 w-32 h-32 bg-purple-500 opacity-10 rounded-full -mr-10 -mt-10 group-hover:scale-110 transition-transform duration-500"></div>
            <div className="flex items-center relative">
              <div className="flex-shrink-0 bg-purple-900/30 rounded p-3 group-hover:bg-purple-800/40 transition-colors duration-300 card-icon">
                <RiTeamLine className="h-7 w-7 text-purple-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Total Workspaces</dt>
                  <dd>
                    <div className="text-3xl font-bold text-white font-mono">{data?.workspaces?.total || 0}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-slate-900 px-6 py-3 border-t border-slate-800/50 card-details">
            <div className="text-xs">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-center">
                  <div className="text-gray-500 mb-1">New (30d)</div>
                  <div className="text-purple-400 font-medium font-mono">{data?.workspaces?.new || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Active</div>
                  <div className="text-purple-400 font-medium font-mono">{data?.workspaces?.total || 0}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Boards Card */}
        <div id="boards-card" className="bg-slate-900 overflow-hidden rounded border border-slate-800 transition-all duration-300 hover:translate-y-[-2px] group stat-card">
          <div className="p-6 relative overflow-hidden card-header">
            <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500 opacity-10 rounded-full -mr-10 -mt-10 group-hover:scale-110 transition-transform duration-500"></div>
            <div className="flex items-center relative">
              <div className="flex-shrink-0 bg-blue-900/30 rounded p-3 group-hover:bg-blue-800/40 transition-colors duration-300 card-icon">
                <RiLayoutMasonryLine className="h-7 w-7 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Total Boards</dt>
                  <dd>
                    <div className="text-3xl font-bold text-white font-mono">{data?.boards?.total || 0}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-slate-900 px-6 py-3 border-t border-slate-800/50 card-details">
            <div className="text-xs">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Avg/Workspace</div>
                  <div className="text-blue-400 font-medium font-mono">
                    {data?.workspaces?.total && data?.boards?.total
                      ? (data.boards.total / data.workspaces.total).toFixed(1)
                      : '0'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Active</div>
                  <div className="text-blue-400 font-medium font-mono">{data?.boards?.total || 0}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Cards Card */}
        <div id="cards-card" className="bg-slate-900 overflow-hidden rounded border border-slate-800 transition-all duration-300 hover:translate-y-[-2px] group stat-card">
          <div className="p-6 relative overflow-hidden card-header">
            <div className="absolute top-0 right-0 w-32 h-32 bg-green-500 opacity-10 rounded-full -mr-10 -mt-10 group-hover:scale-110 transition-transform duration-500"></div>
            <div className="flex items-center relative">
              <div className="flex-shrink-0 bg-green-900/30 rounded p-3 group-hover:bg-green-800/40 transition-colors duration-300 card-icon">
                <RiFileList3Line className="h-7 w-7 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Total Cards</dt>
                  <dd>
                    <div className="text-3xl font-bold text-white font-mono">{data?.cards?.total || 0}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-slate-900 px-6 py-3 border-t border-slate-800/50 card-details">
            <div className="text-xs">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Avg/Board</div>
                  <div className="text-green-400 font-medium font-mono">
                    {data?.boards?.total && data?.cards?.total
                      ? (data.cards.total / data.boards.total).toFixed(1)
                      : '0'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Created (30d)</div>
                  <div className="text-green-400 font-medium font-mono">{data?.cards?.total || 0}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Subscriptions Card */}
        <div id="subscriptions-card" className="bg-slate-900 overflow-hidden rounded border border-slate-800 transition-all duration-300 hover:translate-y-[-2px] group stat-card">
          <div className="p-6 relative overflow-hidden card-header">
            <div className="absolute top-0 right-0 w-32 h-32 bg-amber-500 opacity-10 rounded-full -mr-10 -mt-10 group-hover:scale-110 transition-transform duration-500"></div>
            <div className="flex items-center relative">
              <div className="flex-shrink-0 bg-amber-900/30 rounded p-3 group-hover:bg-amber-800/40 transition-colors duration-300 card-icon">
                <RiVipCrownLine className="h-7 w-7 text-amber-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Active Subscriptions</dt>
                  <dd>
                    <div className="text-3xl font-bold text-white font-mono">{data?.subscriptions?.active || 0}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-slate-900 px-6 py-3 border-t border-slate-800/50 card-details">
            <div className="text-xs">
              <div className="grid grid-cols-3 gap-2">
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Trial</div>
                  <div className="text-amber-400 font-medium font-mono">{data?.subscriptions?.trial || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Canceled</div>
                  <div className="text-amber-400 font-medium font-mono">{data?.subscriptions?.canceled || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">MRR</div>
                  <div className="text-amber-400 font-medium font-mono">${data?.subscriptions?.mrr?.toFixed(2) || '0.00'}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Activity Card */}
        <div id="activity-card" className="bg-slate-900 overflow-hidden rounded border border-slate-800 transition-all duration-300 hover:translate-y-[-2px] group stat-card">
          <div className="p-6 relative overflow-hidden card-header">
            <div className="absolute top-0 right-0 w-32 h-32 bg-rose-500 opacity-10 rounded-full -mr-10 -mt-10 group-hover:scale-110 transition-transform duration-500"></div>
            <div className="flex items-center relative">
              <div className="flex-shrink-0 bg-rose-900/30 rounded p-3 group-hover:bg-rose-800/40 transition-colors duration-300 card-icon">
                <RiHistoryLine className="h-7 w-7 text-rose-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Recent Activity</dt>
                  <dd>
                    <div className="text-3xl font-bold text-white font-mono">{data?.activity?.recent || 0}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-slate-900 px-6 py-3 border-t border-slate-800/50 card-details">
            <div className="text-xs">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Period</div>
                  <div className="text-rose-400 font-medium">Last 24h</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Type</div>
                  <div className="text-rose-400 font-medium">All actions</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* System Health */}
      <div id="system-health-section" className="bg-slate-900 rounded shadow-sm dashboard-section">
        <div id="system-health-header" className="px-6 py-4 border-b border-slate-800/50 flex justify-between items-center section-header">
          <div>
            <h3 className="text-lg leading-6 font-semibold flex items-center text-white">
              <div className="w-8 h-8 rounded-full bg-green-900/20 flex items-center justify-center mr-3">
                <svg className="h-4 w-4 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              System Health
            </h3>
            <p className="mt-1 ml-11 text-xs text-gray-400">Current status of the platform</p>
          </div>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
            data?.systemHealth?.status === 'healthy'
              ? 'bg-green-900/30 text-green-300'
              : data?.systemHealth?.status === 'warning'
                ? 'bg-amber-900/30 text-amber-300'
                : 'bg-rose-900/30 text-rose-300'
          }`}>
            {data?.systemHealth?.status || 'Unknown'}
          </span>
        </div>
        <div id="system-health-content" className="px-6 py-6 section-content">
          <div className="flex flex-col health-status-overview">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-green-900/20 flex items-center justify-center">
                  <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-base font-medium capitalize text-white">
                    {data?.systemHealth?.status === 'healthy'
                      ? 'All Systems Operational'
                      : data?.systemHealth?.status === 'warning'
                        ? 'Some Systems Degraded'
                        : 'System Issues Detected'}
                  </h3>
                </div>
              </div>
              <div className="text-xs text-gray-400 flex items-center bg-slate-800/50 px-3 py-1.5 rounded">
                <svg className="h-3.5 w-3.5 mr-1.5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>
                  Last checked: <span className="text-gray-300">
                    {data?.systemHealth?.lastChecked
                      ? new Date(data.systemHealth.lastChecked).toLocaleString()
                      : 'Unknown'}
                  </span>
                </span>
              </div>
            </div>
          </div>

          <div id="system-services-grid" className="mt-6 grid grid-cols-3 gap-4 services-grid">
            <div id="api-service-status" className="bg-slate-900 rounded p-3 border border-slate-800 service-status-card text-center">
              <div className="flex-shrink-0 bg-green-900/20 rounded-full p-2 mx-auto mb-2 service-icon">
                <RiCheckboxCircleLine className="h-5 w-5 text-green-400" />
              </div>
              <h4 className="text-xs font-medium text-white mb-1">API Services</h4>
              <p className="text-xs text-green-400">Operational</p>
            </div>

            <div id="database-service-status" className="bg-slate-900 rounded p-3 border border-slate-800 service-status-card text-center">
              <div className="flex-shrink-0 bg-green-900/20 rounded-full p-2 mx-auto mb-2 service-icon">
                <RiCheckboxCircleLine className="h-5 w-5 text-green-400" />
              </div>
              <h4 className="text-xs font-medium text-white mb-1">Database</h4>
              <p className="text-xs text-green-400">Operational</p>
            </div>

            <div id="storage-service-status" className="bg-slate-900 rounded p-3 border border-slate-800 service-status-card text-center">
              <div className="flex-shrink-0 bg-green-900/20 rounded-full p-2 mx-auto mb-2 service-icon">
                <RiCheckboxCircleLine className="h-5 w-5 text-green-400" />
              </div>
              <h4 className="text-xs font-medium text-white mb-1">Storage</h4>
              <p className="text-xs text-green-400">Operational</p>
            </div>
          </div>
        </div>
      </div>

      {/* Growth Statistics */}
      <div id="growth-statistics-section" className="bg-slate-900 rounded overflow-hidden border border-slate-800 dashboard-section">
        <div id="growth-statistics-header" className="px-6 py-4 border-b border-slate-800 flex justify-between items-center section-header">
          <div className="header-content">
            <h3 className="text-lg leading-6 font-semibold text-white">Growth Statistics</h3>
            <p className="mt-1 max-w-2xl text-xs text-gray-400">Platform growth over time</p>
          </div>
          <div className="flex items-center space-x-3 period-selector">
            <label htmlFor="period" className="text-xs font-medium text-gray-400">
              Period
            </label>
            <select
              id="period"
              value={statsPeriod}
              onChange={(e) => setStatsPeriod(e.target.value)}
              className="block w-32 pl-3 pr-8 py-1.5 text-xs border border-slate-800 bg-slate-900 text-white rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
            >
              <option value="week">Last 7 days</option>
              <option value="month">Last 30 days</option>
              <option value="year">Last 12 months</option>
            </select>
          </div>
        </div>
        <div id="growth-statistics-content" className="px-6 py-6 section-content">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 growth-charts-container">
            {/* User Growth */}
            <div id="user-growth-chart" className="bg-slate-900 rounded shadow-sm transition-shadow duration-300 growth-chart">
              <div className="flex items-center justify-between px-4 py-3 border-b border-slate-800/50">
                <h4 className="text-sm font-semibold text-white flex items-center chart-title">
                  <div className="w-8 h-8 rounded-full bg-indigo-900/30 flex items-center justify-center mr-3">
                    <RiUser3Line className="h-4 w-4 text-indigo-400" />
                  </div>
                  User Growth
                </h4>
                <div className="text-xs text-indigo-400 font-medium">
                  {userGrowth && userGrowth.length > 0 ?
                    userGrowth.reduce((sum, item) => sum + item.count, 0) : 0} total
                </div>
              </div>
              {userGrowth && userGrowth.length > 0 ? (
                <div className="p-4">
                  <table className="min-w-full bg-slate-900 overflow-hidden growth-table">
                    <thead>
                      <tr>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                          Date
                        </th>
                        <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                          New Users
                        </th>
                      </tr>
                    </thead>
                    <tbody className="table-body">
                      {userGrowth.slice(0, 5).map((item, index) => (
                        <tr key={index} className="transition-colors duration-150 hover:bg-indigo-900/10 group">
                          <td className="px-3 py-2 whitespace-nowrap text-xs font-medium text-gray-400 group-hover:text-gray-300">{item.date}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-xs text-indigo-400 font-semibold text-right group-hover:text-indigo-300">
                            <span className="inline-flex items-center">
                              <span className="mr-1.5">+{item.count}</span>
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7 17L17 7M17 7H8M17 7V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 bg-slate-900/30 empty-state">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <div className="w-12 h-12 rounded-full bg-indigo-900/20 flex items-center justify-center">
                      <RiUser3Line className="h-6 w-6 text-indigo-500" />
                    </div>
                    <p className="text-sm text-gray-400">No user growth data available.</p>
                  </div>
                </div>
              )}
            </div>

            {/* Workspace Growth */}
            <div id="workspace-growth-chart" className="bg-slate-900 rounded shadow-sm transition-shadow duration-300 growth-chart">
              <div className="flex items-center justify-between px-4 py-3 border-b border-slate-800/50">
                <h4 className="text-sm font-semibold text-white flex items-center chart-title">
                  <div className="w-8 h-8 rounded-full bg-purple-900/30 flex items-center justify-center mr-3">
                    <RiTeamLine className="h-4 w-4 text-purple-400" />
                  </div>
                  Workspace Growth
                </h4>
                <div className="text-xs text-purple-400 font-medium">
                  {workspaceGrowth && workspaceGrowth.length > 0 ?
                    workspaceGrowth.reduce((sum, item) => sum + item.count, 0) : 0} total
                </div>
              </div>
              {workspaceGrowth && workspaceGrowth.length > 0 ? (
                <div className="p-4">
                  <table className="min-w-full bg-slate-900 overflow-hidden growth-table">
                    <thead>
                      <tr>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                          Date
                        </th>
                        <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                          New Workspaces
                        </th>
                      </tr>
                    </thead>
                    <tbody className="table-body">
                      {workspaceGrowth.slice(0, 5).map((item, index) => (
                        <tr key={index} className="transition-colors duration-150 hover:bg-purple-900/10 group">
                          <td className="px-3 py-2 whitespace-nowrap text-xs font-medium text-gray-400 group-hover:text-gray-300">{item.date}</td>
                          <td className="px-3 py-2 whitespace-nowrap text-xs text-purple-400 font-semibold text-right group-hover:text-purple-300">
                            <span className="inline-flex items-center">
                              <span className="mr-1.5">+{item.count}</span>
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7 17L17 7M17 7H8M17 7V16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 bg-slate-900/30 empty-state">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <div className="w-12 h-12 rounded-full bg-purple-900/20 flex items-center justify-center">
                      <RiTeamLine className="h-6 w-6 text-purple-500" />
                    </div>
                    <p className="text-sm text-gray-400">No workspace growth data available.</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchSystemSettings, updateSystemSettings } from '../store/slices/settingsSlice';
import Input from '../components/ui/Input';
import Textarea from '../components/ui/Textarea';
import Button from '../components/ui/Button';

const SystemSettings = () => {
  const dispatch = useAppDispatch();
  const { settings, loading, error, updateSuccess } = useAppSelector((state) => state.settings);

  const [formData, setFormData] = useState({
    siteName: '',
    siteDescription: '',
    maintenanceMode: false,
    allowSignups: true,
    requireEmailVerification: true,
    maxWorkspacesPerUser: 5,
    maxBoardsPerWorkspace: 10,
    maxCardsPerBoard: 100,
    defaultUserQuota: {
      storage: 100, // MB
      members: 5
    },
    emailSettings: {
      fromEmail: '',
      replyToEmail: '',
      sendWelcomeEmail: true,
      sendNotificationEmails: true
    },
    integrations: {
      googleAuth: {
        enabled: false,
        clientId: '',
        clientSecret: ''
      },
      stripe: {
        enabled: false,
        publicKey: '',
        secretKey: '',
        webhookSecret: ''
      }
    }
  });

  const [activeTab, setActiveTab] = useState('general');
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    dispatch(fetchSystemSettings());
  }, [dispatch]);

  useEffect(() => {
    if (settings) {
      setFormData(settings);
    }
  }, [settings]);

  useEffect(() => {
    if (updateSuccess) {
      setSuccessMessage('Settings updated successfully!');
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [updateSuccess]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;

      // Handle nested properties
      if (name.includes('.')) {
        const [parent, child] = name.split('.');
        setFormData({
          ...formData,
          [parent]: {
            ...formData[parent as keyof typeof formData],
            [child]: target.checked
          }
        });
      } else {
        setFormData({
          ...formData,
          [name]: target.checked
        });
      }
    } else if (type === 'number') {
      // Handle nested properties
      if (name.includes('.')) {
        const [parent, child] = name.split('.');
        setFormData({
          ...formData,
          [parent]: {
            ...formData[parent as keyof typeof formData],
            [child]: Number(value)
          }
        });
      } else {
        setFormData({
          ...formData,
          [name]: Number(value)
        });
      }
    } else {
      // Handle nested properties
      if (name.includes('.')) {
        const [parent, child] = name.split('.');
        setFormData({
          ...formData,
          [parent]: {
            ...formData[parent as keyof typeof formData],
            [child]: value
          }
        });
      } else {
        setFormData({
          ...formData,
          [name]: value
        });
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(updateSystemSettings(formData));
  };

  if (loading && !settings) {
    return <div className="flex items-center justify-center h-screen text-lg font-medium text-gray-700 dark:text-white">Loading system settings...</div>;
  }

  if (error) {
    return <div className="flex items-center justify-center h-screen text-lg font-medium text-red-600 dark:text-red-400">Error loading system settings: {error}</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">System Settings</h1>

      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-300">
          {successMessage}
        </div>
      )}

      <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
        <button
          type="button"
          className={`py-3 px-4 font-medium text-sm border-b-2 ${
            activeTab === 'general'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
          }`}
          onClick={() => setActiveTab('general')}
        >
          General
        </button>
        <button
          type="button"
          className={`py-3 px-4 font-medium text-sm border-b-2 ${
            activeTab === 'limits'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
          }`}
          onClick={() => setActiveTab('limits')}
        >
          Limits & Quotas
        </button>
        <button
          type="button"
          className={`py-3 px-4 font-medium text-sm border-b-2 ${
            activeTab === 'email'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
          }`}
          onClick={() => setActiveTab('email')}
        >
          Email
        </button>
        <button
          type="button"
          className={`py-3 px-4 font-medium text-sm border-b-2 ${
            activeTab === 'integrations'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
          }`}
          onClick={() => setActiveTab('integrations')}
        >
          Integrations
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <Input
                label="Site Name"
                type="text"
                id="siteName"
                name="siteName"
                value={formData.siteName}
                onChange={handleInputChange}
                fullWidth
              />

              <Textarea
                label="Site Description"
                id="siteDescription"
                name="siteDescription"
                value={formData.siteDescription}
                onChange={handleInputChange}
                fullWidth
              />

              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="maintenanceMode"
                    name="maintenanceMode"
                    checked={formData.maintenanceMode}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                  />
                  <label htmlFor="maintenanceMode" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Maintenance Mode
                  </label>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 ml-6">When enabled, only admins can access the site.</p>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="allowSignups"
                  name="allowSignups"
                  checked={formData.allowSignups}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                />
                <label htmlFor="allowSignups" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Allow New Signups
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireEmailVerification"
                  name="requireEmailVerification"
                  checked={formData.requireEmailVerification}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                />
                <label htmlFor="requireEmailVerification" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Require Email Verification
                </label>
              </div>
            </div>
          )}

          {activeTab === 'limits' && (
            <div className="space-y-6">
              <Input
                label="Max Workspaces Per User"
                type="number"
                id="maxWorkspacesPerUser"
                name="maxWorkspacesPerUser"
                value={formData.maxWorkspacesPerUser.toString()}
                onChange={handleInputChange}
                min="1"
                fullWidth
              />

              <Input
                label="Max Boards Per Workspace"
                type="number"
                id="maxBoardsPerWorkspace"
                name="maxBoardsPerWorkspace"
                value={formData.maxBoardsPerWorkspace.toString()}
                onChange={handleInputChange}
                min="1"
                fullWidth
              />

              <Input
                label="Max Cards Per Board"
                type="number"
                id="maxCardsPerBoard"
                name="maxCardsPerBoard"
                value={formData.maxCardsPerBoard.toString()}
                onChange={handleInputChange}
                min="1"
                fullWidth
              />

              <Input
                label="Default Storage Quota (MB)"
                type="number"
                id="defaultUserQuota.storage"
                name="defaultUserQuota.storage"
                value={formData.defaultUserQuota.storage.toString()}
                onChange={handleInputChange}
                min="1"
                fullWidth
              />

              <Input
                label="Default Members Quota"
                type="number"
                id="defaultUserQuota.members"
                name="defaultUserQuota.members"
                value={formData.defaultUserQuota.members.toString()}
                onChange={handleInputChange}
                min="1"
                fullWidth
              />
            </div>
          )}

          {activeTab === 'email' && (
            <div className="space-y-6">
              <Input
                label="From Email"
                type="email"
                id="emailSettings.fromEmail"
                name="emailSettings.fromEmail"
                value={formData.emailSettings.fromEmail}
                onChange={handleInputChange}
                fullWidth
              />

              <Input
                label="Reply-To Email"
                type="email"
                id="emailSettings.replyToEmail"
                name="emailSettings.replyToEmail"
                value={formData.emailSettings.replyToEmail}
                onChange={handleInputChange}
                fullWidth
              />

              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="emailSettings.sendWelcomeEmail"
                    name="emailSettings.sendWelcomeEmail"
                    checked={formData.emailSettings.sendWelcomeEmail}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                  />
                  <label htmlFor="emailSettings.sendWelcomeEmail" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Send Welcome Email
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="emailSettings.sendNotificationEmails"
                    name="emailSettings.sendNotificationEmails"
                    checked={formData.emailSettings.sendNotificationEmails}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                  />
                  <label htmlFor="emailSettings.sendNotificationEmails" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Send Notification Emails
                  </label>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'integrations' && (
            <div className="space-y-8">
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-800 dark:text-white">Google Authentication</h3>

                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="integrations.googleAuth.enabled"
                    name="integrations.googleAuth.enabled"
                    checked={formData.integrations.googleAuth.enabled}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                  />
                  <label htmlFor="integrations.googleAuth.enabled" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Enable Google Authentication
                  </label>
                </div>

                <Input
                  label="Client ID"
                  type="text"
                  id="integrations.googleAuth.clientId"
                  name="integrations.googleAuth.clientId"
                  value={formData.integrations.googleAuth.clientId}
                  onChange={handleInputChange}
                  disabled={!formData.integrations.googleAuth.enabled}
                  fullWidth
                />

                <Input
                  label="Client Secret"
                  type="password"
                  id="integrations.googleAuth.clientSecret"
                  name="integrations.googleAuth.clientSecret"
                  value={formData.integrations.googleAuth.clientSecret}
                  onChange={handleInputChange}
                  disabled={!formData.integrations.googleAuth.enabled}
                  fullWidth
                />
              </div>

              <div className="space-y-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-800 dark:text-white">Stripe Integration</h3>

                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="integrations.stripe.enabled"
                    name="integrations.stripe.enabled"
                    checked={formData.integrations.stripe.enabled}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-700"
                  />
                  <label htmlFor="integrations.stripe.enabled" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Enable Stripe Integration
                  </label>
                </div>

                <Input
                  label="Public Key"
                  type="text"
                  id="integrations.stripe.publicKey"
                  name="integrations.stripe.publicKey"
                  value={formData.integrations.stripe.publicKey}
                  onChange={handleInputChange}
                  disabled={!formData.integrations.stripe.enabled}
                  fullWidth
                />

                <Input
                  label="Secret Key"
                  type="password"
                  id="integrations.stripe.secretKey"
                  name="integrations.stripe.secretKey"
                  value={formData.integrations.stripe.secretKey}
                  onChange={handleInputChange}
                  disabled={!formData.integrations.stripe.enabled}
                  fullWidth
                />

                <Input
                  label="Webhook Secret"
                  type="password"
                  id="integrations.stripe.webhookSecret"
                  name="integrations.stripe.webhookSecret"
                  value={formData.integrations.stripe.webhookSecret}
                  onChange={handleInputChange}
                  disabled={!formData.integrations.stripe.enabled}
                  fullWidth
                />
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end mt-6">
          <Button type="submit" variant="primary">Save Settings</Button>
        </div>
      </form>
    </div>
  );
};

export default SystemSettings;

import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchBoards, updateBoardStatus, resetBoards } from '../store/slices/boardSlice';
import InfiniteVirtualList from '../components/common/InfiniteVirtualList';

const Boards = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { data, loading, error, hasNextPage } = useAppSelector((state) => state.boards);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50); // Increased default limit to 50
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');
  const [workspaceFilter, setWorkspaceFilter] = useState('');

  // Reset boards when filters change
  useEffect(() => {
    dispatch(resetBoards());
    setPage(1);
    loadBoards(1);
  }, [status, search, workspaceFilter, limit]);

  // Load boards based on current filters and pagination
  const loadBoards = useCallback((pageToLoad: number) => {
    dispatch(fetchBoards({
      page: pageToLoad,
      limit,
      status,
      search,
      workspace: workspaceFilter
    }));
  }, [dispatch, limit, status, search, workspaceFilter]);

  // Load next page of boards for infinite scroll
  const loadNextPage = useCallback(() => {
    if (hasNextPage && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadBoards(nextPage);
    }
  }, [hasNextPage, loading, page, loadBoards]);

  const handleStatusChange = async (boardId: string, newStatus: string) => {
    if (window.confirm(`Are you sure you want to change this board's status to ${newStatus}?`)) {
      try {
        await dispatch(updateBoardStatus({ boardId, status: newStatus }));
        // Reset and refresh the board list
        dispatch(resetBoards());
        setPage(1);
        loadBoards(1);
      } catch (error) {
        console.error('Failed to update board status:', error);
      }
    }
  };



  if (loading && !data.length) {
    return (
      <div id="boards-loading" className="flex items-center justify-center h-64 w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        <span className="ml-3 text-lg font-medium text-gray-700 dark:text-gray-300">Loading boards...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div id="boards-error" className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-4 rounded-md m-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error loading boards</h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div id="boards-page" className="w-full px-6 py-8 animate-fadeIn">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-white">Boards</h1>
        <p className="mt-2 text-gray-400">Manage and monitor boards across workspaces</p>
      </div>

      <div id="boards-filters" className="mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div id="boards-search" className="flex-grow min-w-[200px] max-w-md">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                id="search-input"
                type="text"
                placeholder="Search boards..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white placeholder-gray-500"
                aria-label="Search boards"
              />
            </div>
          </div>

          <div id="boards-status-filter" className="w-48">
            <select
              id="status-select"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white"
              aria-label="Filter by status"
            >
              <option value="">All Boards</option>
              <option value="active">Active</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          <div id="boards-workspace-filter" className="w-64">
            <input
              id="workspace-input"
              type="text"
              placeholder="Filter by workspace ID..."
              value={workspaceFilter}
              onChange={(e) => setWorkspaceFilter(e.target.value)}
              className="w-full px-4 py-2.5 bg-slate-900 border border-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-white placeholder-gray-500"
              aria-label="Filter by workspace ID"
            />
          </div>
        </div>
      </div>

      <div className="border border-slate-800 rounded-md overflow-hidden">
        <div id="boards-table-header" className="md:flex bg-slate-900 border-b border-slate-800 py-3 font-medium text-gray-400 text-xs uppercase tracking-wider">
          <div className="flex-1 px-6">Title</div>
          <div className="flex-1 px-6">Workspace</div>
          <div className="w-24 px-4 text-center">Members</div>
          <div className="w-28 px-4 text-center">Status</div>
          <div className="w-32 px-4">Created At</div>
          <div className="w-48 px-4 text-center">Actions</div>
        </div>

        <div id="boards-list-container" className="h-[calc(100vh-350px)] overflow-hidden">
        <InfiniteVirtualList
          items={data}
          hasNextPage={hasNextPage}
          isLoading={loading}
          loadNextPage={loadNextPage}
          estimateSize={70} // Estimated row height
          loadingComponent={
            <div id="boards-loading-more" className="text-center py-4 text-gray-500 bg-gray-50 border-t border-gray-200 dark:text-gray-400 dark:bg-slate-800 dark:border-slate-700">
              <div className="inline-flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading more boards...
              </div>
            </div>
          }
          emptyComponent={
            <div id="boards-empty-list" className="flex flex-col items-center justify-center p-12 text-center text-gray-500 bg-white dark:bg-slate-800 dark:text-gray-400">
              <svg className="w-16 h-16 mb-4 text-gray-300 dark:text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
              </svg>
              <p className="text-xl font-medium mb-2">
                {search || status || workspaceFilter
                  ? 'No boards match your filters'
                  : 'No boards found'}
              </p>
              <p className="text-sm max-w-md">
                {search || status || workspaceFilter
                  ? 'Try adjusting your search or filter criteria to find what you\'re looking for.'
                  : 'Create your first board to get started.'}
              </p>
            </div>
          }
          renderItem={(board: any) => (
            <div className="flex items-center border-b border-slate-800 hover:bg-slate-800/70 transition-colors bg-slate-900 text-white" key={board._id} id={`board-row-${board._id}`}>
              <div className="flex-1 p-4 truncate font-medium">{board.title}</div>
              <div className="flex-1 p-4 truncate">
                <button
                  type="button"
                  className="text-indigo-600 hover:text-indigo-800 hover:underline font-medium dark:text-indigo-400 dark:hover:text-indigo-300"
                  onClick={() => navigate(`/workspaces/${board.workspace._id}`)}
                >
                  {board.workspace.name}
                </button>
              </div>
              <div className="w-24 p-4 text-center">{board.members?.length || 0}</div>
              <div className="w-28 p-4 text-center">
                <span className={`inline-flex px-2 py-1 rounded text-xs font-medium ${board.archived
                  ? 'bg-gray-800 text-gray-300'
                  : 'bg-green-900/30 text-green-300'}`}>
                  {board.archived ? 'Archived' : 'Active'}
                </span>
              </div>
              <div className="w-32 p-4 text-gray-400">{new Date(board.createdAt).toLocaleDateString()}</div>
              <div className="w-48 p-4 flex justify-center gap-2">
                <button
                  type="button"
                  onClick={() => navigate(`/boards/${board._id}`)}
                  className="px-3 py-1 rounded bg-indigo-900/30 text-indigo-300 text-xs font-medium hover:bg-indigo-900/50 transition-colors focus:outline-none"
                >
                  View
                </button>

                {board.archived ? (
                  <button
                    type="button"
                    onClick={() => handleStatusChange(board._id, 'active')}
                    className="px-3 py-1 rounded bg-green-900/30 text-green-300 text-xs font-medium hover:bg-green-900/50 transition-colors focus:outline-none"
                  >
                    Unarchive
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={() => handleStatusChange(board._id, 'archived')}
                    className="px-3 py-1 rounded bg-red-900/30 text-red-300 text-xs font-medium hover:bg-red-900/50 transition-colors focus:outline-none"
                  >
                    Archive
                  </button>
                )}
              </div>
            </div>
          )}
        />
        </div>
      </div>

      <div id="boards-pagination" className="mt-4 flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <label htmlFor="limit-select" className="text-sm text-gray-400">Items per page:</label>
          <select
            id="limit-select"
            value={limit}
            onChange={(e) => {
              setLimit(Number(e.target.value));
            }}
            aria-label="Number of items to display per page"
            className="block w-16 pl-2 pr-6 py-1 text-sm border border-slate-800 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 bg-slate-900 text-gray-300 transition-colors"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="200">200</option>
          </select>
        </div>

        <div className="ml-4 text-sm text-gray-400">
          {data.length > 0 && (
            <span>
              Showing <span className="font-medium">{data.length}</span> boards
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default Boards;

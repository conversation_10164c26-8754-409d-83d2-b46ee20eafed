/* Cards page specific styles */

.cards-container {
  background-color: var(--bg-surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.cards-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.cards-header h1 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.cards-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background-color: var(--bg-subtle);
  border-bottom: 1px solid var(--border-color);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-select {
  min-width: 150px;
}

.cards-table-container {
  overflow-x: auto;
}

.cards-table {
  width: 100%;
  border-collapse: collapse;
}

.cards-table th {
  background-color: var(--bg-muted);
  color: var(--text-secondary);
  font-weight: 600;
  text-align: left;
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.cards-table td {
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.cards-table tr:last-child td {
  border-bottom: none;
}

.cards-table tr:hover {
  background-color: var(--bg-muted);
}

.cards-table a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.cards-table a:hover {
  text-decoration: underline;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.status-badge.archived {
  background-color: var(--danger-color);
  color: white;
}

.status-badge.active {
  background-color: var(--success-color);
  color: white;
}

.status-badge.pending {
  background-color: var(--warning-color);
  color: white;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-state-icon {
  font-size: 3rem;
  color: var(--text-disabled);
  margin-bottom: 1rem;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-state-description {
  color: var(--text-secondary);
  max-width: 400px;
  margin-bottom: 1.5rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-subtle);
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
}

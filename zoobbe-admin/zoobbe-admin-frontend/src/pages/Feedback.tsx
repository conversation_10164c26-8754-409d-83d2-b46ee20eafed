import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchFeedback, updateFeedbackStatus } from '../store/slices/feedbackSlice';
import InfiniteVirtualList from '../components/common/InfiniteVirtualList';

const Feedback = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { data, loading, error, pagination } = useAppSelector((state) => state.feedback);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50); // Increased default limit for better UX
  const [status, setStatus] = useState('');
  const [type, setType] = useState('');
  const [priority, setPriority] = useState('');
  const [search, setSearch] = useState('');

  // Load feedback based on current filters and pagination
  const loadFeedback = useCallback((pageToLoad: number) => {
    dispatch(fetchFeedback({
      page: pageToLoad,
      limit,
      status,
      type,
      priority,
      search
    }));
  }, [dispatch, limit, status, type, priority, search]);

  // Reset and load first page when filters change
  useEffect(() => {
    setPage(1);
    loadFeedback(1);
  }, [status, type, priority, search, limit, loadFeedback]);

  // Load next page of feedback for infinite scroll
  const loadNextPage = useCallback(() => {
    if (page < pagination.totalPages && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadFeedback(nextPage);
    }
  }, [page, pagination.totalPages, loading, loadFeedback]);

  const handleStatusChange = async (feedbackId: string, newStatus: string) => {
    if (window.confirm(`Are you sure you want to change this feedback's status to ${newStatus}?`)) {
      try {
        await dispatch(updateFeedbackStatus({ feedbackId, status: newStatus }));
        // Refresh the feedback list
        setPage(1);
        loadFeedback(1);
      } catch (error) {
        console.error('Failed to update feedback status:', error);
      }
    }
  };

  const handleViewDetails = (feedbackId: string) => {
    navigate(`/feedback/${feedbackId}`);
  };

  if (loading && !data.length) {
    return <div className="flex justify-center items-center p-8 text-gray-600">Loading feedback...</div>;
  }

  if (error) {
    return <div className="p-8 text-red-600 bg-red-50 rounded-md">Error loading feedback: {error}</div>;
  }

  return (
    <div className="p-5">
      <h1 className="text-2xl font-bold mb-5">User Feedback</h1>

      <div className="flex flex-wrap gap-4 mb-5">
        <div className="flex-grow max-w-md">
          <input
            type="text"
            placeholder="Search feedback..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label htmlFor="status-select" className="sr-only">Filter by status</label>
          <select
            id="status-select"
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            aria-label="Filter feedback by status"
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            <option value="new">New</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>
        </div>

        <div>
          <label htmlFor="type-select" className="sr-only">Filter by type</label>
          <select
            id="type-select"
            value={type}
            onChange={(e) => setType(e.target.value)}
            aria-label="Filter feedback by type"
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            <option value="bug">Bug</option>
            <option value="feature">Feature Request</option>
            <option value="improvement">Improvement</option>
            <option value="question">Question</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div>
          <label htmlFor="priority-select" className="sr-only">Filter by priority</label>
          <select
            id="priority-select"
            value={priority}
            onChange={(e) => setPriority(e.target.value)}
            aria-label="Filter feedback by priority"
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
      </div>

      <div className="flex bg-gray-100 border-b-2 border-gray-200 font-bold py-3">
        <div className="flex-1 px-4 text-left">Title</div>
        <div className="flex-1 px-4 text-left">User</div>
        <div className="flex-1 px-4 text-left">Type</div>
        <div className="flex-1 px-4 text-left">Priority</div>
        <div className="flex-1 px-4 text-left">Status</div>
        <div className="flex-1 px-4 text-left">Created</div>
        <div className="flex-1 px-4 text-left">Actions</div>
      </div>

      <div className="h-[calc(100vh-250px)] overflow-hidden border-none">
        <InfiniteVirtualList
          items={data}
          hasNextPage={page < pagination.totalPages}
          isLoading={loading}
          loadNextPage={loadNextPage}
          estimateSize={70} // Estimated row height
          loadingComponent={<div className="text-center p-2.5 italic text-gray-600">Loading more feedback...</div>}
          emptyComponent={
            <div className="p-5 text-center text-gray-600 italic bg-gray-50">
              {search || status || type || priority
                ? 'No feedback matches your filters'
                : 'No feedback found'}
            </div>
          }
          renderItem={(feedback) => (
            <div className="flex border-b border-gray-200 py-3 items-center hover:bg-gray-50" key={feedback._id}>
              <div className="flex-1 px-4 overflow-hidden text-ellipsis whitespace-nowrap">{feedback.title}</div>
              <div className="flex-1 px-4 overflow-hidden text-ellipsis whitespace-nowrap">{feedback.user?.name || 'Unknown'}</div>
              <div className="flex-1 px-4 overflow-hidden text-ellipsis whitespace-nowrap">
                <span className={`inline-block px-2 py-1 rounded text-sm font-medium capitalize ${
                  feedback.type === 'bug' ? 'bg-red-100 text-red-900' :
                  feedback.type === 'feature' ? 'bg-indigo-100 text-indigo-900' :
                  feedback.type === 'improvement' ? 'bg-cyan-100 text-cyan-900' :
                  feedback.type === 'question' ? 'bg-purple-100 text-purple-900' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {feedback.type.charAt(0).toUpperCase() + feedback.type.slice(1)}
                </span>
              </div>
              <div className="flex-1 px-4 overflow-hidden text-ellipsis whitespace-nowrap">
                <span className={`inline-block px-2 py-1 rounded text-sm font-medium capitalize ${
                  feedback.priority === 'low' ? 'bg-green-100 text-green-900' :
                  feedback.priority === 'medium' ? 'bg-amber-100 text-amber-900' :
                  feedback.priority === 'high' ? 'bg-orange-100 text-orange-900' :
                  feedback.priority === 'critical' ? 'bg-red-100 text-red-900' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {feedback.priority.charAt(0).toUpperCase() + feedback.priority.slice(1)}
                </span>
              </div>
              <div className="flex-1 px-4 overflow-hidden text-ellipsis whitespace-nowrap">
                <span className={`inline-block px-2 py-1 rounded text-sm font-medium capitalize ${
                  feedback.status === 'new' ? 'bg-blue-100 text-blue-900' :
                  feedback.status === 'in_progress' ? 'bg-amber-100 text-amber-900' :
                  feedback.status === 'resolved' ? 'bg-green-100 text-green-900' :
                  'bg-gray-200 text-gray-700'
                }`}>
                  {feedback.status.replace('_', ' ').charAt(0).toUpperCase() + feedback.status.replace('_', ' ').slice(1)}
                </span>
              </div>
              <div className="flex-1 px-4 overflow-hidden text-ellipsis whitespace-nowrap">
                {new Date(feedback.createdAt).toLocaleDateString()}
              </div>
              <div className="flex-1 px-4 overflow-hidden text-ellipsis whitespace-nowrap">
                <div className="flex gap-1">
                  <button
                    type="button"
                    onClick={() => handleViewDetails(feedback._id)}
                    className="px-2 py-1 bg-blue-100 text-blue-900 rounded text-sm hover:bg-blue-200 transition-colors"
                  >
                    View
                  </button>

                  {feedback.status === 'new' && (
                    <button
                      type="button"
                      onClick={() => handleStatusChange(feedback._id, 'in_progress')}
                      className="px-2 py-1 bg-amber-100 text-amber-900 rounded text-sm hover:bg-amber-200 transition-colors"
                    >
                      Start
                    </button>
                  )}

                  {feedback.status === 'in_progress' && (
                    <button
                      type="button"
                      onClick={() => handleStatusChange(feedback._id, 'resolved')}
                      className="px-2 py-1 bg-green-100 text-green-900 rounded text-sm hover:bg-green-200 transition-colors"
                    >
                      Resolve
                    </button>
                  )}

                  {(feedback.status === 'resolved' || feedback.status === 'closed') && (
                    <button
                      type="button"
                      onClick={() => handleStatusChange(feedback._id, 'in_progress')}
                      className="px-2 py-1 bg-purple-100 text-purple-900 rounded text-sm hover:bg-purple-200 transition-colors"
                    >
                      Reopen
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
        />
      </div>

      <div className="mt-4 flex items-center">
        <label htmlFor="limit-select" className="mr-2 text-gray-700">Items per page:</label>
        <select
          id="limit-select"
          value={limit}
          onChange={(e) => {
            setLimit(Number(e.target.value));
          }}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100">100</option>
          <option value="200">200</option>
        </select>
      </div>
    </div>
  );
};

export default Feedback;

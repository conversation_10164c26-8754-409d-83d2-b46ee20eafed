/* BoardDetail.css */

/* Animation for modal */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
}

/* Card cover image */
.card-cover-image {
  height: 8rem;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Card cover image with URL */
.card-cover-image[data-url] {
  background-image: var(--bg-image);
}

/* Board detail layout */
.board-detail-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Info cards */
.info-card {
  background-color: #1e293b;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-card-dark {
  background-color: #0f172a;
}

/* Table styling */
.board-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.board-table th {
  background-color: #0f172a;
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.75rem 1.5rem;
  text-align: left;
}

.board-table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #334155;
  color: #e2e8f0;
}

.board-table tr:hover {
  background-color: #334155;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge-active {
  background-color: #065f46;
  color: #a7f3d0;
}

.status-badge-inactive {
  background-color: #4b5563;
  color: #e5e7eb;
}

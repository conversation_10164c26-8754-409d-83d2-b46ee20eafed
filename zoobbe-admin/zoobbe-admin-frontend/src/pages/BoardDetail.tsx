import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  fetchBoardById,
  updateBoardStatus,
  updateBoardDetailsThunk,
  transferBoardThunk,
  fetchBoardMembersThunk,
  updateBoardMemberRoleThunk,
  removeBoardMemberThunk
} from '../store/slices/boardSlice';
import './BoardDetail.css';
import Input from '../components/ui/Input';
import Select from '../components/ui/Select';
import Button from '../components/ui/Button';
import useCardCover from '../hooks/useCardCover';

const BoardDetail = () => {
  const { boardId } = useParams<{ boardId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { selectedBoard, loading, error } = useAppSelector((state) => state.boards);
  const { data: workspaces } = useAppSelector((state) => state.workspaces);

  const [activeTab, setActiveTab] = useState('details');

  // Edit mode states
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [newTitle, setNewTitle] = useState('');
  const [isTransferring, setIsTransferring] = useState(false);
  const [targetWorkspaceId, setTargetWorkspaceId] = useState('');

  useEffect(() => {
    if (boardId) {
      dispatch(fetchBoardById(boardId));
    }
  }, [dispatch, boardId]);

  useEffect(() => {
    if (selectedBoard) {
      setNewTitle(selectedBoard.title);
    }
  }, [selectedBoard]);

  const handleStatusChange = async (status: string) => {
    if (boardId && window.confirm(`Are you sure you want to change this board's status to ${status}?`)) {
      try {
        await dispatch(updateBoardStatus({ boardId, status }));
        // Refresh board data
        dispatch(fetchBoardById(boardId));
      } catch (error) {
        console.error('Failed to update board status:', error);
      }
    }
  };

  const handleTitleUpdate = async () => {
    if (boardId && newTitle && newTitle !== selectedBoard?.title) {
      try {
        await dispatch(updateBoardDetailsThunk({
          boardId,
          data: { title: newTitle }
        }));
        setIsEditingTitle(false);
      } catch (error) {
        console.error('Failed to update board title:', error);
      }
    } else {
      setIsEditingTitle(false);
    }
  };

  const handleVisibilityChange = async (visibility: string) => {
    if (boardId && visibility !== selectedBoard?.visibility) {
      try {
        await dispatch(updateBoardDetailsThunk({
          boardId,
          data: { visibility }
        }));
      } catch (error) {
        console.error('Failed to update board visibility:', error);
      }
    }
  };

  const handleTransferBoard = async () => {
    if (boardId && targetWorkspaceId) {
      try {
        await dispatch(transferBoardThunk({
          boardId,
          workspaceId: targetWorkspaceId
        }));
        setIsTransferring(false);
        setTargetWorkspaceId('');
      } catch (error) {
        console.error('Failed to transfer board:', error);
      }
    }
  };

  const handleMemberRoleChange = async (userId: string, role: string) => {
    if (boardId && userId) {
      try {
        await dispatch(updateBoardMemberRoleThunk({
          boardId,
          userId,
          role
        }));
      } catch (error) {
        console.error('Failed to update member role:', error);
      }
    }
  };

  const handleRemoveMember = async (userId: string) => {
    if (boardId && userId && window.confirm('Are you sure you want to remove this member from the board?')) {
      try {
        await dispatch(removeBoardMemberThunk({
          boardId,
          userId
        }));
      } catch (error) {
        console.error('Failed to remove member:', error);
      }
    }
  };

  if (loading && !selectedBoard) {
    return <div className="flex items-center justify-center h-screen text-lg font-medium text-gray-700 dark:text-white">
      <div className="flex items-center">
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading board details...
      </div>
    </div>;
  }

  if (error) {
    return <div className="flex items-center justify-center h-screen text-lg font-medium text-red-600 dark:text-red-400">Error loading board: {error}</div>;
  }

  if (!selectedBoard) {
    return <div className="flex items-center justify-center h-screen text-lg font-medium text-red-600 dark:text-red-400">Board not found</div>;
  }

  return (
    <div className="space-y-8 animate-fadeIn px-6 py-8">
      <div id="board-detail-header" className="mb-6">
        <div className="flex items-center mb-2">
          <button
            type="button"
            className="inline-flex items-center mr-4 text-primary-600 hover:text-primary-500 transition-colors dark:text-primary-400 dark:hover:text-primary-300"
            onClick={() => navigate('/boards')}
            aria-label="Back to boards"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Boards
          </button>
        </div>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col">
            <h1 className="text-3xl font-bold text-white">
              {selectedBoard.title}
            </h1>
            <div className="flex items-center mt-2">
              <span className="text-sm text-gray-400">
                Workspace:
              </span>
              <button
                type="button"
                className="ml-1 text-sm text-primary-400 hover:text-primary-300 hover:underline font-medium"
                onClick={() => navigate(`/workspaces/${selectedBoard.workspace._id}`)}
              >
                {selectedBoard.workspace.name}
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-3 mt-4 md:mt-0">
            <span className={`inline-flex px-2.5 py-1 rounded-full text-xs font-medium ${selectedBoard.archived
              ? 'bg-gray-800 text-gray-300'
              : 'bg-green-900 text-green-300'}`}>
              {selectedBoard.archived ? 'Archived' : 'Active'}
            </span>

            {selectedBoard.archived ? (
              <Button
                type="button"
                onClick={() => handleStatusChange('active')}
                variant="primary"
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                }
              >
                Unarchive Board
              </Button>
            ) : (
              <Button
                type="button"
                onClick={() => handleStatusChange('archived')}
                variant="warning"
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" />
                    <path fillRule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                }
              >
                Archive Board
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            {isEditingTitle ? (
              <div className="flex items-center space-x-2 mb-4">
                <Input
                  type="text"
                  value={newTitle}
                  onChange={(e) => setNewTitle(e.target.value)}
                  autoFocus
                  placeholder="Enter board title"
                  fullWidth
                />
                <Button
                  type="button"
                  onClick={handleTitleUpdate}
                  variant="primary"
                  size="small"
                >
                  Save
                </Button>
                <Button
                  type="button"
                  onClick={() => setIsEditingTitle(false)}
                  variant="outline"
                  size="small"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <div className="flex items-center mb-4">
                <h2 className="text-xl font-bold text-gray-800 dark:text-white mr-2">
                  {selectedBoard.title}
                </h2>
                <button
                  type="button"
                  className="p-1 text-gray-500 hover:text-primary-600 transition-colors dark:text-gray-400 dark:hover:text-primary-400"
                  onClick={() => setIsEditingTitle(true)}
                  aria-label="Edit board title"
                  title="Edit board title"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                </button>
              </div>
            )}

            <div className="flex items-center mb-4">
              <span className="text-gray-600 dark:text-gray-400 mr-2">Workspace:</span>
              <button
                type="button"
                className="text-primary-600 hover:text-primary-800 hover:underline font-medium dark:text-primary-400 dark:hover:text-primary-300 mr-2"
                onClick={() => navigate(`/workspaces/${selectedBoard.workspace._id}`)}
              >
                {selectedBoard.workspace.name}
              </button>
              <button
                type="button"
                className="text-sm text-gray-600 hover:text-gray-800 hover:underline dark:text-gray-400 dark:hover:text-gray-300"
                onClick={() => setIsTransferring(true)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                </svg>
                Transfer
              </button>
            </div>
          </div>

          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Status:</span>
              <span className={`inline-flex px-2.5 py-1 rounded-full text-xs font-medium ${selectedBoard.archived
                ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'}`}>
                {selectedBoard.archived ? 'Archived' : 'Active'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Visibility:</span>
              <Select
                value={selectedBoard.visibility}
                onChange={(e) => handleVisibilityChange(e.target.value)}
                options={[
                  { value: 'Private', label: 'Private' },
                  { value: 'Workspace', label: 'Workspace' },
                  { value: 'Public', label: 'Public' }
                ]}
                size="sm"
              />
            </div>
          </div>
        </div>
      </div>

      {isTransferring && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg shadow-xl max-w-md w-full overflow-hidden animate-fade-in-up">
            <div className="px-6 py-5 border-b border-slate-700 flex justify-between items-center">
              <h3 className="text-xl font-bold text-white">Transfer Board</h3>
              <button
                type="button"
                onClick={() => setIsTransferring(false)}
                className="text-gray-400 hover:text-gray-300 transition-colors"
                aria-label="Close modal"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <div className="flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                  </svg>
                  <p className="text-white font-medium">Transfer Board to Another Workspace</p>
                </div>
                <p className="text-gray-400 text-sm ml-7 mb-4">
                  This action will move the board "{selectedBoard.title}" to another workspace. All members and cards will be preserved.
                </p>
              </div>

              <div className="mb-6">
                <label htmlFor="target-workspace" className="block text-sm font-medium text-gray-300 mb-2">
                  Select Target Workspace
                </label>
                <Select
                  id="target-workspace"
                  value={targetWorkspaceId}
                  onChange={(e) => setTargetWorkspaceId(e.target.value)}
                  options={[
                    { value: '', label: 'Select a workspace' },
                    ...(workspaces || []).map(workspace => ({
                      value: workspace._id,
                      label: `${workspace.name} ${workspace._id === selectedBoard.workspace._id ? '(Current)' : ''}`,
                      disabled: workspace._id === selectedBoard.workspace._id
                    }))
                  ]}
                  variant="filled"
                  fullWidth
                />
              </div>

              <div className="flex justify-end space-x-3 mt-8">
                <Button
                  type="button"
                  onClick={() => setIsTransferring(false)}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleTransferBoard}
                  variant="primary"
                  disabled={!targetWorkspaceId}
                >
                  Transfer Board
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex border-b border-slate-800 mb-8">
        <button
          type="button"
          className={`py-4 px-6 font-medium text-sm relative ${
            activeTab === 'details'
              ? 'text-primary-400 border-b-2 border-primary-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('details')}
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
              <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
            </svg>
            Details
          </span>
        </button>
        <button
          type="button"
          className={`py-4 px-6 font-medium text-sm relative ${
            activeTab === 'members'
              ? 'text-primary-400 border-b-2 border-primary-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('members')}
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
            </svg>
            Members
          </span>
        </button>
        <button
          type="button"
          className={`py-4 px-6 font-medium text-sm relative ${
            activeTab === 'cards'
              ? 'text-primary-400 border-b-2 border-primary-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('cards')}
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
            </svg>
            Cards
          </span>
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'details' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="bg-slate-800 rounded-lg shadow-sm p-6 mb-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-white">Board Information</h3>

                  <div className="flex items-center space-x-3">
                    <button
                      type="button"
                      className="text-sm text-primary-400 hover:text-primary-300 flex items-center"
                      onClick={() => setIsTransferring(true)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                      </svg>
                      Transfer Board
                    </button>

                    <button
                      type="button"
                      className="text-sm text-primary-400 hover:text-primary-300 flex items-center"
                      onClick={() => setIsEditingTitle(true)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                      Edit Title
                    </button>
                  </div>
                </div>

                {isEditingTitle ? (
                  <div className="flex items-center space-x-2 mb-6 p-4 bg-slate-900 rounded-lg">
                    <Input
                      type="text"
                      value={newTitle}
                      onChange={(e) => setNewTitle(e.target.value)}
                      autoFocus
                      placeholder="Enter board title"
                      variant="filled"
                      fullWidth
                    />
                    <Button
                      type="button"
                      onClick={handleTitleUpdate}
                      variant="primary"
                      size="small"
                    >
                      Save
                    </Button>
                    <Button
                      type="button"
                      onClick={() => setIsEditingTitle(false)}
                      variant="outline"
                      size="small"
                    >
                      Cancel
                    </Button>
                  </div>
                ) : null}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="p-4 bg-slate-900 rounded-lg">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 4h3a3 3 0 006 0h3a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm2.5 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm2.45 4a2.5 2.5 0 10-4.9 0h4.9zM12 9a1 1 0 100 2h3a1 1 0 100-2h-3zm-1 4a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      <div className="text-sm font-medium text-gray-400">ID</div>
                    </div>
                    <div className="font-mono text-white break-all text-sm">{selectedBoard._id}</div>
                  </div>

                  <div className="p-4 bg-slate-900 rounded-lg">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                      </svg>
                      <div className="text-sm font-medium text-gray-400">Visibility</div>
                    </div>
                    <Select
                      value={selectedBoard.visibility}
                      onChange={(e) => handleVisibilityChange(e.target.value)}
                      options={[
                        { value: 'Private', label: 'Private' },
                        { value: 'Workspace', label: 'Workspace' },
                        { value: 'Public', label: 'Public' }
                      ]}
                      variant="filled"
                      size="sm"
                    />
                  </div>

                  <div className="p-4 bg-slate-900 rounded-lg">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                      </svg>
                      <div className="text-sm font-medium text-gray-400">Members</div>
                    </div>
                    <div className="text-white flex items-center">
                      <span className="text-2xl font-semibold mr-2">{selectedBoard.members?.length || 0}</span>
                      <button
                        type="button"
                        onClick={() => setActiveTab('members')}
                        className="text-xs text-primary-400 hover:text-primary-300"
                      >
                        View all members
                      </button>
                    </div>
                  </div>

                  <div className="p-4 bg-slate-900 rounded-lg">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                      </svg>
                      <div className="text-sm font-medium text-gray-400">Cards</div>
                    </div>
                    <div className="text-white flex items-center">
                      <span className="text-2xl font-semibold mr-2">{selectedBoard.cards?.length || 0}</span>
                      <button
                        type="button"
                        onClick={() => setActiveTab('cards')}
                        className="text-xs text-primary-400 hover:text-primary-300"
                      >
                        View all cards
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="lg:col-span-1">
              <div className="bg-slate-800 rounded-lg shadow-sm p-6 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4">Board Timeline</h3>

                <div className="space-y-4">
                  <div className="p-4 bg-slate-900 rounded-lg">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      <div className="text-sm font-medium text-gray-400">Created At</div>
                    </div>
                    <div className="text-white">{new Date(selectedBoard.createdAt).toLocaleString()}</div>
                  </div>

                  <div className="p-4 bg-slate-900 rounded-lg">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                      </svg>
                      <div className="text-sm font-medium text-gray-400">Updated At</div>
                    </div>
                    <div className="text-white">{new Date(selectedBoard.updatedAt).toLocaleString()}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="bg-slate-800 rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-5 border-b border-slate-700 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white">Board Members</h3>

              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => dispatch(fetchBoardMembersThunk(boardId!))}
                  className="text-sm text-primary-400 hover:text-primary-300 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                  Refresh Members
                </button>
              </div>
            </div>

            {selectedBoard.members && selectedBoard.members.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-slate-700">
                  <thead className="bg-slate-900">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Name</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Email</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Role</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-slate-800 divide-y divide-slate-700">
                    {selectedBoard.members.map((member) => (
                      <tr key={member.user._id} className="hover:bg-slate-700 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0 rounded-full bg-primary-900 flex items-center justify-center text-primary-300 font-medium">
                              {member.user.name.charAt(0).toUpperCase()}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-white">{member.user.name}</div>
                              <div className="text-xs text-gray-400">{member.user.username || 'No username'}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{member.user.email}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          <Select
                            value={member.role}
                            onChange={(e) => handleMemberRoleChange(member.user._id, e.target.value)}
                            options={[
                              { value: 'admin', label: 'Admin' },
                              { value: 'member', label: 'Member' }
                            ]}
                            variant="filled"
                            size="sm"
                            aria-label={`Role for ${member.user.name}`}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2.5 py-1 rounded-full text-xs font-medium ${
                            member.status === 'active'
                              ? 'bg-green-900 text-green-300'
                              : 'bg-gray-700 text-gray-300'
                          }`}>
                            {member.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-3">
                            <Button
                              type="button"
                              onClick={() => navigate(`/users/${member.user._id}`)}
                              variant="link"
                              size="small"
                              icon={
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                  <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                                </svg>
                              }
                            >
                              View
                            </Button>
                            <Button
                              type="button"
                              onClick={() => handleRemoveMember(member.user._id)}
                              variant="danger-link"
                              size="small"
                              icon={
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                              }
                            >
                              Remove
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-12 text-center text-gray-400">
                <svg className="w-20 h-20 mb-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                <p className="text-xl font-medium mb-2 text-white">No members found</p>
                <p className="text-sm max-w-md">This board doesn't have any members yet.</p>
                <Button
                  type="button"
                  onClick={() => dispatch(fetchBoardMembersThunk(boardId!))}
                  variant="primary"
                  className="mt-4"
                >
                  Refresh Members
                </Button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'cards' && (
          <div className="bg-slate-800 rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-5 border-b border-slate-700 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white">Board Cards</h3>

              <div className="flex items-center">
                <span className="text-sm text-gray-400 mr-2">
                  {selectedBoard.cards?.length || 0} cards found
                </span>
              </div>
            </div>

            {selectedBoard.cards && selectedBoard.cards.length > 0 ? (
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {selectedBoard.cards.map((card) => (
                    <div key={card._id} className="bg-slate-900 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 group">
                      {card.cover?.url && (
                        <div
                          ref={useCardCover(card.cover.url)}
                          className="card-cover-image"
                          data-url={card.cover.url}
                          role="img"
                          aria-label="Card cover image"
                        ></div>
                      )}
                      <div className="p-5">
                        <div className="flex justify-between items-start mb-3">
                          <h4 className="text-lg font-medium text-white group-hover:text-primary-400 transition-colors">{card.title}</h4>
                          <span className={`inline-flex px-2.5 py-1 rounded-full text-xs font-medium ${
                            card.priority === 'high'
                              ? 'bg-red-900 text-red-300'
                              : card.priority === 'medium'
                                ? 'bg-yellow-900 text-yellow-300'
                                : 'bg-blue-900 text-blue-300'
                          }`}>
                            {card.priority}
                          </span>
                        </div>

                        <p className="text-gray-300 mb-4 text-sm line-clamp-2">
                          {card.description || 'No description provided for this card.'}
                        </p>

                        <div className="flex items-center justify-between mt-4 pt-4 border-t border-slate-700">
                          <div className="flex items-center">
                            {card.members && card.members.length > 0 ? (
                              <div className="flex -space-x-2">
                                {card.members.slice(0, 3).map((member, index) => (
                                  <div key={index} className="h-6 w-6 rounded-full bg-primary-900 flex items-center justify-center text-xs text-primary-300 font-medium ring-2 ring-slate-900">
                                    {member.name ? member.name.charAt(0).toUpperCase() : 'U'}
                                  </div>
                                ))}
                                {card.members.length > 3 && (
                                  <div className="h-6 w-6 rounded-full bg-slate-700 flex items-center justify-center text-xs text-gray-300 font-medium ring-2 ring-slate-900">
                                    +{card.members.length - 3}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <span className="text-xs text-gray-400">No members</span>
                            )}
                          </div>

                          {card.dueDate && (
                            <div className="flex items-center text-xs text-gray-400">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                              </svg>
                              Due: {new Date(card.dueDate).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-12 text-center text-gray-400">
                <svg className="w-20 h-20 mb-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p className="text-xl font-medium mb-2 text-white">No cards found</p>
                <p className="text-sm max-w-md">This board doesn't have any cards yet.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BoardDetail;

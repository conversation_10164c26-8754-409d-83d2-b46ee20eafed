import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { activityAPI } from '../../services/api';

interface ActivityUser {
  _id: string;
  name: string;
  email: string;
}

interface Activity {
  _id: string;
  user: ActivityUser;
  action: string;
  targetType: string;
  target: string;
  metadata: any;
  createdAt: string;
}

interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface ActivityState {
  data: Activity[];
  types: string[];
  loading: boolean;
  error: string | null;
  pagination: Pagination;
}

export const fetchActivityLog = createAsyncThunk(
  'activity/fetchActivityLog',
  async (params: { page?: number; limit?: number; type?: string; search?: string }, { rejectWithValue }) => {
    try {
      const response = await activityAPI.getActivityLog(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch activity log');
    }
  }
);

export const fetchActivityTypes = createAsyncThunk(
  'activity/fetchActivityTypes',
  async (_, { rejectWithValue }) => {
    try {
      const response = await activityAPI.getActivityTypes();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch activity types');
    }
  }
);

export const fetchActivityStats = createAsyncThunk(
  'activity/fetchActivityStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await activityAPI.getActivityStats();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch activity statistics');
    }
  }
);

const initialState: ActivityState = {
  data: [],
  types: [],
  loading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 1,
  },
};

const activitySlice = createSlice({
  name: 'activity',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Activity Log
      .addCase(fetchActivityLog.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActivityLog.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload.activities;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchActivityLog.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Activity Types
      .addCase(fetchActivityTypes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActivityTypes.fulfilled, (state, action) => {
        state.loading = false;
        state.types = action.payload.types;
      })
      .addCase(fetchActivityTypes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError } = activitySlice.actions;

export default activitySlice.reducer;

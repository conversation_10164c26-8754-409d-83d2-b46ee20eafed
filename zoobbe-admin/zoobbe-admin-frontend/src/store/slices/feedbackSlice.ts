import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { feedbackAPI } from '../../services/api';

interface FeedbackItem {
  _id: string;
  userId: string;
  user: {
    _id: string;
    name: string;
    email: string;
    username: string;
  };
  type: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  assignedTo?: string;
  responses: Array<{
    _id: string;
    message: string;
    createdBy: string;
    createdAt: string;
  }>;
  notes: Array<{
    _id: string;
    note: string;
    createdBy: string;
    createdAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface FeedbackState {
  data: FeedbackItem[];
  currentFeedback: FeedbackItem | null;
  loading: boolean;
  error: string | null;
  pagination: Pagination;
}

export const fetchFeedback = createAsyncThunk(
  'feedback/fetchFeedback',
  async (params: { 
    page?: number; 
    limit?: number; 
    status?: string; 
    type?: string;
    priority?: string;
    search?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await feedbackAPI.getFeedback(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch feedback');
    }
  }
);

export const fetchFeedbackById = createAsyncThunk(
  'feedback/fetchFeedbackById',
  async (feedbackId: string, { rejectWithValue }) => {
    try {
      const response = await feedbackAPI.getFeedbackById(feedbackId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch feedback details');
    }
  }
);

export const updateFeedbackStatus = createAsyncThunk(
  'feedback/updateFeedbackStatus',
  async ({ feedbackId, status }: { feedbackId: string; status: string }, { rejectWithValue }) => {
    try {
      const response = await feedbackAPI.updateFeedbackStatus(feedbackId, status);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update feedback status');
    }
  }
);

export const addFeedbackResponse = createAsyncThunk(
  'feedback/addFeedbackResponse',
  async ({ feedbackId, message }: { feedbackId: string; message: string }, { rejectWithValue }) => {
    try {
      const response = await feedbackAPI.addFeedbackResponse(feedbackId, message);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add response');
    }
  }
);

export const addInternalNote = createAsyncThunk(
  'feedback/addInternalNote',
  async ({ feedbackId, note }: { feedbackId: string; note: string }, { rejectWithValue }) => {
    try {
      const response = await feedbackAPI.addInternalNote(feedbackId, note);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add note');
    }
  }
);

export const assignFeedback = createAsyncThunk(
  'feedback/assignFeedback',
  async ({ feedbackId, adminId }: { feedbackId: string; adminId: string }, { rejectWithValue }) => {
    try {
      const response = await feedbackAPI.assignFeedback(feedbackId, adminId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to assign feedback');
    }
  }
);

const initialState: FeedbackState = {
  data: [],
  currentFeedback: null,
  loading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1,
  },
};

const feedbackSlice = createSlice({
  name: 'feedback',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    resetCurrentFeedback: (state) => {
      state.currentFeedback = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Feedback
      .addCase(fetchFeedback.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFeedback.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload.feedback;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchFeedback.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Feedback by ID
      .addCase(fetchFeedbackById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFeedbackById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentFeedback = action.payload.feedback;
      })
      .addCase(fetchFeedbackById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update Feedback Status
      .addCase(updateFeedbackStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateFeedbackStatus.fulfilled, (state, action) => {
        state.loading = false;
        if (state.currentFeedback && state.currentFeedback._id === action.payload.feedback._id) {
          state.currentFeedback = action.payload.feedback;
        }
        const index = state.data.findIndex(item => item._id === action.payload.feedback._id);
        if (index !== -1) {
          state.data[index] = action.payload.feedback;
        }
      })
      .addCase(updateFeedbackStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Add Feedback Response
      .addCase(addFeedbackResponse.fulfilled, (state, action) => {
        if (state.currentFeedback && state.currentFeedback._id === action.payload.feedback._id) {
          state.currentFeedback = action.payload.feedback;
        }
      })
      
      // Add Internal Note
      .addCase(addInternalNote.fulfilled, (state, action) => {
        if (state.currentFeedback && state.currentFeedback._id === action.payload.feedback._id) {
          state.currentFeedback = action.payload.feedback;
        }
      })
      
      // Assign Feedback
      .addCase(assignFeedback.fulfilled, (state, action) => {
        if (state.currentFeedback && state.currentFeedback._id === action.payload.feedback._id) {
          state.currentFeedback = action.payload.feedback;
        }
        const index = state.data.findIndex(item => item._id === action.payload.feedback._id);
        if (index !== -1) {
          state.data[index] = action.payload.feedback;
        }
      });
  },
});

export const { clearError, resetCurrentFeedback } = feedbackSlice.actions;

export default feedbackSlice.reducer;

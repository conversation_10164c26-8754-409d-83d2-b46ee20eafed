import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { statsAPI } from '../../services/api';

interface SystemStats {
  users: {
    total: number;
    active: number;
    new: number;
    monthlyActive: number;
  };
  workspaces: {
    total: number;
    new: number;
  };
  boards: {
    total: number;
  };
  cards: {
    total: number;
  };
  activity: {
    recent: number;
  };
  subscriptions: {
    total: number;
    active: number;
    trial: number;
    canceled: number;
    byPlan: Array<{
      _id: string;
      count: number;
      active: number;
    }>;
    mrr: number;
  };
  systemHealth: {
    status: string;
    lastChecked: string;
  };
}

interface ActivityStats {
  totalUsers: number;
  newUsers: number;
  totalWorkspaces: number;
  newWorkspaces: number;
  totalBoards: number;
  totalCards: number;
  byType: Array<{
    type: string;
    count: number;
  }>;
}

interface SubscriptionStats {
  totalActive: number;
  totalCanceled: number;
  monthlyRevenue: number;
  byPlan: Array<{
    plan: string;
    count: number;
  }>;
}

interface StatsState {
  data: SystemStats | null;
  userGrowth: Array<{
    date: string;
    count: number;
  }>;
  workspaceGrowth: Array<{
    date: string;
    count: number;
  }>;
  activityStats: ActivityStats | null;
  subscriptionStats: SubscriptionStats | null;
  loading: boolean;
  error: string | null;
}

export const fetchSystemStats = createAsyncThunk(
  'stats/fetchSystemStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await statsAPI.getSystemStats();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch system stats');
    }
  }
);

export const fetchUserGrowthStats = createAsyncThunk(
  'stats/fetchUserGrowthStats',
  async (period: string = 'month', { rejectWithValue }) => {
    try {
      const response = await statsAPI.getUserGrowthStats(period);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user growth stats');
    }
  }
);

export const fetchWorkspaceGrowthStats = createAsyncThunk(
  'stats/fetchWorkspaceGrowthStats',
  async (period: string = 'month', { rejectWithValue }) => {
    try {
      const response = await statsAPI.getWorkspaceGrowthStats(period);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch workspace growth stats');
    }
  }
);

export const fetchActivityStats = createAsyncThunk(
  'stats/fetchActivityStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await statsAPI.getActivityStats();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch activity stats');
    }
  }
);

export const fetchSubscriptionStats = createAsyncThunk(
  'stats/fetchSubscriptionStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await statsAPI.getSubscriptionStats();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch subscription stats');
    }
  }
);

const initialState: StatsState = {
  data: null,
  userGrowth: [],
  workspaceGrowth: [],
  activityStats: null,
  subscriptionStats: null,
  loading: false,
  error: null,
};

const statsSlice = createSlice({
  name: 'stats',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch System Stats
      .addCase(fetchSystemStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSystemStats.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchSystemStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch User Growth Stats
      .addCase(fetchUserGrowthStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserGrowthStats.fulfilled, (state, action) => {
        state.loading = false;
        state.userGrowth = action.payload.data;
      })
      .addCase(fetchUserGrowthStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Workspace Growth Stats
      .addCase(fetchWorkspaceGrowthStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWorkspaceGrowthStats.fulfilled, (state, action) => {
        state.loading = false;
        state.workspaceGrowth = action.payload.data;
      })
      .addCase(fetchWorkspaceGrowthStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Activity Stats
      .addCase(fetchActivityStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActivityStats.fulfilled, (state, action) => {
        state.loading = false;
        state.activityStats = action.payload.data;
      })
      .addCase(fetchActivityStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Subscription Stats
      .addCase(fetchSubscriptionStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptionStats.fulfilled, (state, action) => {
        state.loading = false;
        state.subscriptionStats = action.payload.data;
      })
      .addCase(fetchSubscriptionStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError } = statsSlice.actions;

export default statsSlice.reducer;

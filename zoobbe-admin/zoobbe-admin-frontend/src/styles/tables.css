/* Table styles with proper light/dark mode contrast */

/* Base table styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

/* Table header */
thead {
  background-color: var(--bg-muted);
  border-bottom: 1px solid var(--border-color);
}

thead th {
  padding: 0.75rem 1rem;
  font-weight: 600;
  text-align: left;
  color: var(--text-secondary);
}

/* Table body */
tbody tr {
  border-bottom: 1px solid var(--border-color);
}

tbody tr:last-child {
  border-bottom: none;
}

tbody td {
  padding: 0.75rem 1rem;
  color: var(--text-primary);
}

/* Alternating row colors */
tbody tr:nth-child(even) {
  background-color: var(--bg-subtle);
}

tbody tr:nth-child(odd) {
  background-color: var(--bg-surface);
}

/* Hover effect */
tbody tr:hover {
  background-color: var(--bg-muted);
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.status-badge.archived {
  background-color: var(--danger-color);
  color: white;
}

.status-badge.active {
  background-color: var(--success-color);
  color: white;
}

.status-badge.pending {
  background-color: var(--warning-color);
  color: white;
}

/* Links in tables */
table a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

table a:hover {
  text-decoration: underline;
}

/* Responsive tables */
@media (max-width: 768px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

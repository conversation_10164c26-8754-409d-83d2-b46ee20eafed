/* Card styles with proper light/dark mode contrast */

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: 1rem;
  border-bottom: 1px solid var(--card-border);
  background-color: var(--bg-surface);
}

.card-header h2, 
.card-header h3, 
.card-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-weight: 600;
}

.card-body {
  padding: 1rem;
  color: var(--text-primary);
}

.card-footer {
  padding: 1rem;
  border-top: 1px solid var(--card-border);
  background-color: var(--bg-subtle);
}

/* Card variants */
.card-primary {
  border-top: 3px solid var(--primary-color);
}

.card-success {
  border-top: 3px solid var(--success-color);
}

.card-danger {
  border-top: 3px solid var(--danger-color);
}

.card-warning {
  border-top: 3px solid var(--warning-color);
}

/* Card with hover effect */
.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-2px);
}

/* Card grid layout */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

/* Card content styles */
.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.card-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.card-text {
  color: var(--text-primary);
  line-height: 1.5;
}

/* Card with icon */
.card-with-icon {
  display: flex;
  align-items: flex-start;
}

.card-icon {
  flex-shrink: 0;
  margin-right: 1rem;
  color: var(--primary-color);
}

.card-content {
  flex: 1;
}

/* Stats card */
.stat-card {
  text-align: center;
  padding: 1.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

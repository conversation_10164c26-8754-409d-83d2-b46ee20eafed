/* Feedback List Page Styles */
.feedback-page {
  padding: 20px;
}

.feedback-virtual-list {
  height: calc(100vh - 250px);
  overflow: hidden;
  border: none;
}

/* Status, Type, and Priority Badges */
.status, .type, .priority {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

/* Status Colors */
.status.new {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.status.in_progress {
  background-color: #fff8e1;
  color: #ff6f00;
}

.status.resolved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status.closed {
  background-color: #eeeeee;
  color: #616161;
}

/* Type Colors */
.type.bug {
  background-color: #ffebee;
  color: #c62828;
}

.type.feature {
  background-color: #e8eaf6;
  color: #303f9f;
}

.type.improvement {
  background-color: #e0f7fa;
  color: #00838f;
}

.type.question {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.type.other {
  background-color: #f5f5f5;
  color: #424242;
}

/* Priority Colors */
.priority.low {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.priority.medium {
  background-color: #fff8e1;
  color: #ff6f00;
}

.priority.high {
  background-color: #fff3e0;
  color: #e65100;
}

.priority.critical {
  background-color: #ffebee;
  color: #c62828;
}

/* Action Buttons */
.actions button {
  margin-right: 5px;
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.actions button.view {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.actions button.progress {
  background-color: #fff8e1;
  color: #ff6f00;
}

.actions button.resolve {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.actions button.reopen {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

/* Feedback Detail Page Styles */
.feedback-detail-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  background: none;
  border: none;
  color: #2196f3;
  cursor: pointer;
  font-size: 1rem;
  margin-right: 20px;
  padding: 0;
}

.feedback-meta {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
}

.meta-item {
  display: flex;
  flex-direction: column;
}

.meta-item .label {
  font-weight: bold;
  margin-bottom: 5px;
  color: #757575;
}

.feedback-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.feedback-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.progress-button {
  background-color: #fff8e1;
  color: #ff6f00;
}

.resolve-button {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.close-button {
  background-color: #eeeeee;
  color: #616161;
}

.reopen-button {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.feedback-content, .feedback-responses, .internal-notes {
  margin-bottom: 30px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.feedback-content h2, .feedback-responses h2, .internal-notes h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.description p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.no-responses, .no-notes {
  color: #757575;
  font-style: italic;
}

.responses-list, .notes-list {
  margin-bottom: 20px;
}

.response-item, .note-item {
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.response-header, .note-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.response-author, .note-author {
  font-weight: bold;
}

.response-date, .note-date {
  color: #757575;
}

.response-content p, .note-content p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.response-form, .note-form {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}

.response-form h3, .note-form h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.response-form textarea, .note-form textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  resize: vertical;
}

.send-button, .add-note-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  background-color: #2196f3;
  color: white;
}

.send-button:hover, .add-note-button:hover {
  background-color: #1976d2;
}

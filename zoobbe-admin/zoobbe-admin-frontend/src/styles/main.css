/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Intel One Mono - Regular */
@font-face {
  font-family: 'Intel One Mono';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff2/IntelOneMono-Regular.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff/IntelOneMono-Regular.woff') format('woff');
}

/* Intel One Mono - Medium */
@font-face {
  font-family: 'Intel One Mono';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff2/IntelOneMono-Medium.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff/IntelOneMono-Medium.woff') format('woff');
}

/* Intel One Mono - Bold */
@font-face {
  font-family: 'Intel One Mono';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff2/IntelOneMono-Bold.woff2') format('woff2'),
       url('https://cdn.jsdelivr.net/gh/intel/intel-one-mono@v1.3.0/fonts/woff/IntelOneMono-Bold.woff') format('woff');
}

/* Base styles */
:root {
  /* Typography */
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Light Theme Colors (Default) */
  --primary-color: #2563eb;
  --primary-color-rgb: 37, 99, 235;
  --primary-hover: #1d4ed8;
  --secondary-color: #3b82f6;
  --secondary-color-rgb: 59, 130, 246;
  --success-color: #22c55e;
  --success-color-rgb: 34, 197, 94;
  --danger-color: #ef4444;
  --danger-color-rgb: 239, 68, 68;
  --warning-color: #f59e0b;
  --warning-color-rgb: 245, 158, 11;

  /* Text Colors */
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #4b5563;
  --text-disabled: #6b7280;

  /* Background Colors */
  --bg-body: #f5f7fa;
  --bg-surface: #ffffff;
  --bg-elevated: #ffffff;
  --bg-muted: #f3f4f6;
  --bg-subtle: #f9fafb;
  --bg-input: #ffffff;

  /* Border Colors */
  --border-color: #e5e7eb;
  --border-color-hover: #d1d5db;
  --border-color-focus: #3b82f6;

  /* UI Element Colors */
  --card-bg: #ffffff;
  --card-border: #e5e7eb;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  /* Form Elements */
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-text: #111827;
  --input-placeholder: #9ca3af;
  --input-focus-border: #3b82f6;
  --input-focus-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);

  /* Buttons */
  --button-primary-bg: #2563eb;
  --button-primary-text: #ffffff;
  --button-primary-hover: #1d4ed8;
  --button-secondary-bg: #f3f4f6;
  --button-secondary-text: #4b5563;
  --button-secondary-hover: #e5e7eb;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Layout */
  --sidebar-width: 256px;
  --header-height: 64px;
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --transition-speed: 0.3s;
}

/* Dark Theme Colors */
:root[data-theme="dark"], .dark {
  /* Dark Theme Colors */
  --primary-color: #3b82f6;
  --primary-color-rgb: 59, 130, 246;
  --primary-hover: #60a5fa;
  --secondary-color: #4b5563;
  --secondary-color-rgb: 75, 85, 99;
  --success-color: #10b981;
  --success-color-rgb: 16, 185, 129;
  --danger-color: #f87171;
  --danger-color-rgb: 248, 113, 113;
  --warning-color: #fbbf24;
  --warning-color-rgb: 251, 191, 36;

  /* Text Colors */
  --text-primary: #e2e8f0;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-disabled: #64748b;

  /* Background Colors */
  --bg-body: #0f172a;
  --bg-surface: #1e293b;
  --bg-elevated: #334155;
  --bg-muted: #1e293b;
  --bg-subtle: #0f172a;
  --bg-input: #1e293b;

  /* Border Colors */
  --border-color: #334155;
  --border-color-hover: #475569;
  --border-color-focus: #60a5fa;

  /* UI Element Colors */
  --card-bg: #1e293b;
  --card-border: #334155;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

  /* Form Elements */
  --input-bg: #1e293b;
  --input-border: #334155;
  --input-text: #e2e8f0;
  --input-placeholder: #64748b;
  --input-focus-border: #60a5fa;
  --input-focus-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);

  /* Buttons */
  --button-primary-bg: #3b82f6;
  --button-primary-text: #ffffff;
  --button-primary-hover: #60a5fa;
  --button-secondary-bg: #334155;
  --button-secondary-text: #e2e8f0;
  --button-secondary-hover: #475569;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-body);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Apply monospace font to code elements */
code, pre, kbd, samp {
  font-family: 'Intel One Mono', monospace;
}

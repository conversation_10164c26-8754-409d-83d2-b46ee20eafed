@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* This file contains only custom styles that are not covered by Tailwind CSS */


/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}



/* Activity Log */
.activity-page {
  padding: 20px;
}

.activity-page h1 {
  margin-bottom: 20px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.activity-item {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 15px;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.activity-user {
  font-weight: 500;
}

.activity-time {
  font-size: 0.9rem;
  color: var(--text-light);
}

.activity-content {
  margin-top: 10px;
}

.activity-action {
  margin-bottom: 5px;
}

.action-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
  margin-right: 5px;
}

.target-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--success-color);
}

.activity-details {
  margin-top: 10px;
  font-size: 0.9rem;
}

.activity-target {
  margin-bottom: 5px;
}

.activity-metadata {
  background-color: rgba(0, 0, 0, 0.02);
  padding: 10px;
  border-radius: var(--border-radius);
  overflow-x: auto;
}

.activity-metadata pre {
  font-family: monospace;
  font-size: 0.8rem;
}

/* Not Found Page */
.not-found-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
  padding: 20px;
}

.not-found-page h1 {
  font-size: 6rem;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.not-found-page h2 {
  font-size: 2rem;
  margin-bottom: 20px;
}

.not-found-page p {
  margin-bottom: 30px;
  color: var(--text-light);
}

.back-link {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: var(--text-white);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: background-color 0.3s;
}

.back-link:hover {
  background-color: var(--secondary-color);
}

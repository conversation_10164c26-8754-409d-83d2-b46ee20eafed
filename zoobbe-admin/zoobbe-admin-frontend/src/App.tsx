import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store';
import './App.css';

// Layouts
import AdminLayout from './layouts/AdminLayout';
import AuthLayout from './layouts/AuthLayout';

// Context Providers
import { ThemeProvider } from './context/ThemeContext';

// Pages
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import UserDetail from './pages/UserDetail';
import Workspaces from './pages/Workspaces';
import WorkspaceDetail from './pages/WorkspaceDetail';
import Boards from './pages/Boards';
import BoardDetail from './pages/BoardDetail';
import Cards from './pages/Cards';
import Subscriptions from './pages/Subscriptions';
import SubscriptionPlans from './pages/SubscriptionPlans';
import SystemSettings from './pages/SystemSettings';
import ActivityLog from './pages/ActivityLog';
import Analytics from './pages/Analytics';
import Feedback from './pages/Feedback';
import FeedbackDetail from './pages/FeedbackDetail';
import FormComponentsDemo from './pages/FormComponentsDemo';
import Login from './pages/Login';
import NotFound from './pages/NotFound';

// Feature Flags
import {
  FeatureFlagsList,
  FeatureFlagDetail,
  CreateFeatureFlag
} from './pages/FeatureFlags';

// Auth components
import ProtectedRoute from './components/auth/ProtectedRoute';
import { AuthProvider } from './context/AuthContext';

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Auth Routes */}
            <Route path="/login" element={
              <AuthLayout>
                <Login />
              </AuthLayout>
            } />

            {/* Admin Routes */}
            <Route element={
              <ProtectedRoute>
                <AdminLayout />
              </ProtectedRoute>
            }>
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="/dashboard" element={<Dashboard />} />

              {/* User Management */}
              <Route path="/users" element={<Users />} />
              <Route path="/users/:userId" element={<UserDetail />} />

              {/* Workspace Management */}
              <Route path="/workspaces" element={<Workspaces />} />
              <Route path="/workspaces/:workspaceId" element={<WorkspaceDetail />} />

              {/* Board Management */}
              <Route path="/boards" element={<Boards />} />
              <Route path="/boards/:boardId" element={<BoardDetail />} />

              {/* Card Management */}
              <Route path="/cards" element={<Cards />} />

              {/* Subscription Management */}
              <Route path="/subscriptions" element={<Subscriptions />} />
              <Route path="/subscription-plans" element={<SubscriptionPlans />} />

              {/* System Management */}
              <Route path="/settings" element={<SystemSettings />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/activity" element={<ActivityLog />} />

              {/* Feature Flags */}
              <Route path="/feature-flags" element={<FeatureFlagsList />} />
              <Route path="/feature-flags/new" element={<CreateFeatureFlag />} />
              <Route path="/feature-flags/:id" element={<FeatureFlagDetail />} />

              {/* Feedback */}
              <Route path="/feedback" element={<Feedback />} />
              <Route path="/feedback/:id" element={<FeedbackDetail />} />

              {/* UI Components Demo */}
              <Route path="/ui-components" element={<FormComponentsDemo />} />
            </Route>

            {/* 404 Route */}
            <Route path="*" element={<NotFound />} />
            </Routes>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </Provider>
  );
}

export default App;

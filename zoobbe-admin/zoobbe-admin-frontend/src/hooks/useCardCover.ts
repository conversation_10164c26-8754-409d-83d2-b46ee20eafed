import { useEffect, useRef } from 'react';

/**
 * Custom hook to handle card cover images
 * This hook sets the CSS custom property for the card cover image
 * to avoid using inline styles
 */
export const useCardCover = (url: string | undefined) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current && url) {
      // Set the CSS custom property
      ref.current.style.setProperty('--bg-image', `url(${url})`);
    }
  }, [url]);

  return ref;
};

export default useCardCover;

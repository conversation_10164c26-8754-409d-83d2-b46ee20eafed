import { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  RiLogoutBoxRLine,
  RiUserLine,
  RiMenuLine,
  RiCloseLine,
  RiSearchLine,
  RiNotification3Line,
  RiSettings4Line,
  RiQuestionLine,
  RiArrowDownSLine,
  RiDashboardLine,
  RiGithubFill,
  RiSlackFill
} from 'react-icons/ri';
import { useAuth } from '../../context/AuthContext';
import ThemeToggle from '../ui/ThemeToggle';

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
}

const Header = ({ toggleSidebar, sidebarOpen }: HeaderProps) => {
  const { admin, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get current page title from path
  const getPageTitle = () => {
    const path = location.pathname.split('/')[1] || 'dashboard';
    return path.charAt(0).toUpperCase() + path.slice(1);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="bg-white dark:bg-slate-900 z-20 sticky top-0">
      <div className="px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button
              type="button"
              className="text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 focus:outline-none lg:hidden p-1 rounded-lg transition-colors duration-200"
              onClick={toggleSidebar}
              aria-label="Toggle sidebar"
            >
              {sidebarOpen ? (
                <RiCloseLine className="h-6 w-6" />
              ) : (
                <RiMenuLine className="h-6 w-6" />
              )}
            </button>
            <div className="flex items-center">
              <RiDashboardLine className="h-6 w-6 text-indigo-600 dark:text-indigo-400 ml-2 mr-2 hidden sm:block" />
              <h1 className="text-xl font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                {getPageTitle()}
                <span className="ml-2 text-xs font-normal text-gray-400 dark:text-gray-500 hidden md:inline-block">
                  / Admin Panel
                </span>
              </h1>
            </div>
          </div>

          <div className="flex-1 max-w-xl mx-4 hidden md:block">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <RiSearchLine className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2.5 rounded-lg bg-gray-50 dark:bg-slate-800 placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:text-sm transition-all duration-200"
                placeholder="Search anything..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-4">
            <ThemeToggle
              className="block"
              showLabel={false}
              showSystemOption={true}
            />

            <button
              type="button"
              className="p-2 rounded-lg text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors duration-200"
              aria-label="View notifications"
            >
              <RiNotification3Line className="h-5 w-5" />
            </button>

            <button
              type="button"
              className="p-2 rounded-lg text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors duration-200"
              aria-label="Help"
            >
              <RiQuestionLine className="h-5 w-5" />
            </button>

            <div className="relative" ref={dropdownRef}>
              <div>
                <button
                  type="button"
                  className="flex items-center text-sm rounded-lg focus:outline-none p-1.5 hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors duration-200"
                  onClick={() => setShowDropdown(!showDropdown)}
                  aria-label="Open user menu"
                >
                  <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white">
                    {admin?.name ? admin.name.charAt(0).toUpperCase() : <RiUserLine />}
                  </div>
                  <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 hidden md:block">
                    {admin?.name || 'Admin'}
                  </span>
                  <RiArrowDownSLine className="ml-1 h-4 w-4 text-gray-400 dark:text-gray-500 hidden md:block" />
                </button>
              </div>

              {showDropdown && (
                <div className="origin-top-right absolute right-0 mt-2 w-64 rounded-xl shadow-lg py-1 bg-white dark:bg-slate-800 ring-1 ring-black ring-opacity-5 dark:ring-slate-700 focus:outline-none transform transition-all duration-200 ease-out z-50">
                  <div className="px-4 py-3 border-b border-gray-100 dark:border-slate-700 rounded-t-xl">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{admin?.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{admin?.email}</p>
                    <div className="mt-1">
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 capitalize">
                        {admin?.role || 'Admin'}
                      </span>
                    </div>
                  </div>
                  <Link
                    to="/profile"
                    className="flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700"
                  >
                    <RiUserLine className="mr-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    Your Profile
                  </Link>
                  <Link
                    to="/settings"
                    className="flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700"
                  >
                    <RiSettings4Line className="mr-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    Settings
                  </Link>
                  <button
                    type="button"
                    onClick={handleLogout}
                    className="w-full text-left flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700"
                  >
                    <RiLogoutBoxRLine className="mr-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    Sign out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;

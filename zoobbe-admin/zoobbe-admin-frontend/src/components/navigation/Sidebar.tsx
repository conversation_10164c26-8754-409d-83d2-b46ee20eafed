import { NavLink } from 'react-router-dom';
import {
  RiDashboardLine,
  RiUser3Line,
  RiTeamLine,
  RiSettings4Line,
  RiLayoutMasonryLine,
  RiFileList3Line,
  RiVipCrownLine,
  RiToggleLine,
  RiPaletteLine
} from 'react-icons/ri';
import { useLocation } from 'react-router-dom';

const Sidebar = () => {
  const location = useLocation();
  const currentPath = location.pathname.split('/')[1];

  // Reusable sidebar link component
  const SidebarLink = ({ to, icon: Icon, label }: { to: string; icon: React.ElementType; label: string }) => (
    <NavLink
      to={to}
      className={({ isActive }) =>
        `flex items-center px-4 py-2 my-0.5 text-sm font-medium rounded transition-colors ${
          isActive
            ? 'bg-indigo-500/10 text-indigo-400'
            : 'text-slate-400 hover:bg-slate-800/60 hover:text-slate-200'
        }`
      }
    >
      <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
      <span>{label}</span>
    </NavLink>
  );

  // Reusable sidebar section component
  const SidebarSection = ({
    name,
    icon: Icon,
    label,
    children
  }: {
    name: string;
    icon: React.ElementType;
    label: string;
    children: React.ReactNode
  }) => {
    const isActive = currentPath === name ||
      (name === 'subscriptions' && (currentPath === 'subscription-plans')) ||
      (name === 'features' && (currentPath === 'feature-flags' || currentPath === 'feedback')) ||
      (name === 'system' && (currentPath === 'settings' || currentPath === 'analytics' || currentPath === 'activity'));

    return (
      <div className="mt-1">
        <div
          className={`flex items-center px-4 py-2 text-sm font-medium ${
            isActive ? 'text-indigo-400' : 'text-slate-400'
          }`}
        >
          <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
          <span>{label}</span>
        </div>
        <div className="ml-9 space-y-0.5">
          {children}
        </div>
      </div>
    );
  };

  // Reusable nested link component
  const NestedLink = ({ to, label }: { to: string; label: string }) => (
    <NavLink
      to={to}
      className={({ isActive }) =>
        `flex items-center px-3 py-1.5 text-sm rounded transition-colors ${
          isActive
            ? 'bg-indigo-500/10 text-indigo-400 font-medium'
            : 'text-slate-500 hover:bg-slate-800/60 hover:text-slate-300'
        }`
      }
    >
      {label}
    </NavLink>
  );

  return (
    <div className="h-full flex flex-col bg-slate-900">
      <div className="flex items-center h-16 px-6 border-b border-slate-800">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-md bg-indigo-600 flex items-center justify-center">
            <span className="text-white font-bold text-lg">Z</span>
          </div>
          <h1 className="text-xl font-bold text-white">
            Zoobbe <span className="text-indigo-400 font-normal">Admin</span>
          </h1>
        </div>
      </div>

      <div className="flex-1 flex flex-col overflow-y-auto">
        <nav className="flex-1 px-3 py-4 space-y-1">
          <SidebarLink to="/dashboard" icon={RiDashboardLine} label="Dashboard" />

          <SidebarSection name="users" icon={RiUser3Line} label="User Management">
            <NestedLink to="/users" label="All Users" />
          </SidebarSection>

          <SidebarSection name="workspaces" icon={RiTeamLine} label="Workspace Management">
            <NestedLink to="/workspaces" label="All Workspaces" />
          </SidebarSection>

          <SidebarSection name="boards" icon={RiLayoutMasonryLine} label="Board Management">
            <NestedLink to="/boards" label="All Boards" />
          </SidebarSection>

          <SidebarLink to="/cards" icon={RiFileList3Line} label="Cards" />

          <SidebarSection name="subscriptions" icon={RiVipCrownLine} label="Subscription Management">
            <NestedLink to="/subscriptions" label="User Subscriptions" />
            <NestedLink to="/subscription-plans" label="Subscription Plans" />
          </SidebarSection>

          <SidebarSection name="features" icon={RiToggleLine} label="Feature Management">
            <NestedLink to="/feature-flags" label="Feature Flags" />
            <NestedLink to="/feedback" label="User Feedback" />
          </SidebarSection>

          <SidebarSection name="system" icon={RiSettings4Line} label="System Management">
            <NestedLink to="/settings" label="System Settings" />
            <NestedLink to="/analytics" label="Analytics" />
            <NestedLink to="/activity" label="Activity Log" />
          </SidebarSection>

          <SidebarLink to="/ui-components" icon={RiPaletteLine} label="UI Components" />
        </nav>
      </div>

      <div className="py-3 px-6 border-t border-slate-800">
        <div className="flex items-center justify-center">
          <span className="text-xs text-slate-500">© {new Date().getFullYear()} Zoobbe</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;

import { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { isAuthenticated, isLoading, checkAuth } = useAuth();
  const location = useLocation();
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  useEffect(() => {
    const performAuthCheck = async () => {
      if (!isAuthenticated && !isLoading && !hasCheckedAuth) {
        console.log('ProtectedRoute: Performing auth check');
        try {
          await checkAuth();
        } catch (error) {
          console.error('ProtectedRoute: Auth check failed:', error);
        } finally {
          setHasCheckedAuth(true);
        }
      }
    };

    performAuthCheck();
  }, [isAuthenticated, isLoading, checkAuth, hasCheckedAuth]);

  // Show loading while initial auth check is happening
  if (isLoading || (!isAuthenticated && !hasCheckedAuth)) {
    return (
      <div className="loading flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log('ProtectedRoute: Not authenticated, redirecting to login');
    // Redirect to login page with the return url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  console.log('ProtectedRoute: Authenticated, rendering children');
  return <>{children}</>;
};

export default ProtectedRoute;

import React from 'react';
import LoadingSpinner from './LoadingSpinner';

interface Column<T> {
  key: string;
  header: React.ReactNode;
  render?: (item: T, index: number) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

interface TableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  emptyMessage?: string;
  onRowClick?: (item: T, index: number) => void;
  className?: string;
  striped?: boolean;
  hoverable?: boolean;
  bordered?: boolean;
  compact?: boolean;
  variant?: 'default' | 'primary';
}

function Table<T>({
  data,
  columns,
  loading = false,
  emptyMessage = 'No data available',
  onRowClick,
  className = '',
  striped = true,
  hoverable = true,
  bordered = false,
  compact = false,
  variant = 'default'
}: TableProps<T>) {
  // Base classes
  const baseClasses = "min-w-full divide-y divide-gray-200";

  // Border classes
  const borderClasses = bordered
    ? "border border-gray-200"
    : "";

  // Variant classes
  const variantClasses = {
    default: "bg-white",
    primary: "bg-white"
  };

  // Header classes based on variant
  const headerClasses = {
    default: "bg-gray-50",
    primary: "bg-slate-800 text-white"
  };

  // Combine table classes
  const tableClasses = [
    baseClasses,
    variantClasses[variant],
    borderClasses
  ].filter(Boolean).join(' ');

  // Cell alignment classes
  const alignClasses = {
    left: "text-left",
    center: "text-center",
    right: "text-right"
  };

  // Header cell classes
  const getThClasses = (column: Column<T>) => [
    "px-6 py-3 text-xs font-medium uppercase tracking-wider",
    compact ? "py-2" : "",
    variant === 'default' ? "text-gray-500" : "text-gray-100",
    alignClasses[column.align || 'left'],
    column.className || ""
  ].filter(Boolean).join(' ');

  // Body cell classes
  const getTdClasses = (column: Column<T>) => [
    "px-6 py-4 whitespace-nowrap text-sm",
    compact ? "py-2" : "",
    variant === 'default' ? "text-gray-700" : "text-gray-900",
    alignClasses[column.align || 'left'],
    column.className || ""
  ].filter(Boolean).join(' ');

  // Row classes
  const getRowClasses = (index: number) => [
    onRowClick ? "cursor-pointer" : "",
    hoverable ? "hover:bg-gray-50 transition-colors duration-150" : "",
    striped && index % 2 === 1 ? 'bg-gray-50' : ''
  ].filter(Boolean).join(' ');

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow">
        <LoadingSpinner size="lg" color="primary" className="mb-4" />
        <p className="text-gray-600">Loading data...</p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center p-8 bg-white rounded-lg shadow">
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`w-full overflow-x-auto rounded-lg shadow-sm ${className}`}>
      <table className={tableClasses}>
        <thead className={headerClasses[variant]}>
          <tr>
            {columns.map((column) => (
              <th
                key={column.key}
                style={{ width: column.width }}
                className={getThClasses(column)}
              >
                {column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((item, rowIndex) => (
            <tr
              key={rowIndex}
              onClick={onRowClick ? () => onRowClick(item, rowIndex) : undefined}
              className={getRowClasses(rowIndex)}
            >
              {columns.map((column) => (
                <td
                  key={`${rowIndex}-${column.key}`}
                  className={getTdClasses(column)}
                >
                  {column.render
                    ? column.render(item, rowIndex)
                    // @ts-ignore - We're accessing a dynamic property
                    : item[column.key]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default Table;

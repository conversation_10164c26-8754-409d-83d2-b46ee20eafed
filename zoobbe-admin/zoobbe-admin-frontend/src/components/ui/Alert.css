.alert {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-radius: var(--border-radius);
  margin-bottom: 16px;
}

.alert-info {
  background-color: rgba(52, 152, 219, 0.1);
  border-left: 4px solid #3498db;
}

.alert-success {
  background-color: rgba(46, 204, 113, 0.1);
  border-left: 4px solid #2ecc71;
}

.alert-warning {
  background-color: rgba(243, 156, 18, 0.1);
  border-left: 4px solid #f39c12;
}

.alert-error {
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 4px solid #e74c3c;
}

.alert-icon-wrapper {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-icon {
  font-size: 1.25rem;
}

.alert-info .alert-icon {
  color: #3498db;
}

.alert-success .alert-icon {
  color: #2ecc71;
}

.alert-warning .alert-icon {
  color: #f39c12;
}

.alert-error .alert-icon {
  color: #e74c3c;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 0.875rem;
}

.alert-close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  transition: color 0.2s;
  margin-left: 12px;
}

.alert-close:hover {
  color: var(--text-color);
}

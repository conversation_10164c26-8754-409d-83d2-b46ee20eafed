import React from 'react';

interface SwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'success' | 'danger' | 'warning';
  className?: string;
  labelPosition?: 'left' | 'right';
}

const Switch: React.FC<SwitchProps> = ({
  checked,
  onChange,
  label,
  disabled = false,
  size = 'md',
  color = 'primary',
  className = '',
  labelPosition = 'right'
}) => {
  // Size classes for the switch container
  const containerSizeClasses = {
    sm: 'h-4 w-7',
    md: 'h-5 w-10',
    lg: 'h-6 w-12'
  };

  // Size classes for the switch thumb/handle
  const thumbSizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  // Translation classes for the thumb/handle when checked
  const thumbTranslateClasses = {
    sm: 'translate-x-3',
    md: 'translate-x-5',
    lg: 'translate-x-6'
  };

  // Color classes for the switch when checked
  const colorClasses = {
    primary: 'bg-slate-600',
    success: 'bg-green-600',
    danger: 'bg-rose-600',
    warning: 'bg-amber-500'
  };

  // Label size classes
  const labelSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const handleChange = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      {label && labelPosition === 'left' && (
        <span className={`mr-3 ${labelSizeClasses[size]} text-gray-700`}>{label}</span>
      )}
      
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        disabled={disabled}
        onClick={handleChange}
        className={`
          relative inline-flex flex-shrink-0 border-2 border-transparent rounded-full 
          cursor-pointer transition-colors ease-in-out duration-200 
          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500
          ${containerSizeClasses[size]}
          ${checked ? colorClasses[color] : 'bg-gray-200'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <span
          className={`
            pointer-events-none inline-block rounded-full bg-white shadow transform 
            ring-0 transition ease-in-out duration-200
            ${thumbSizeClasses[size]}
            ${checked ? thumbTranslateClasses[size] : 'translate-x-0'}
          `}
        />
      </button>
      
      {label && labelPosition === 'right' && (
        <span className={`ml-3 ${labelSizeClasses[size]} text-gray-700`}>{label}</span>
      )}
    </div>
  );
};

export default Switch;

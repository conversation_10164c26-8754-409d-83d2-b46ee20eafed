import React, { TextareaHTMLAttributes, forwardRef } from 'react';

interface TextareaProps extends Omit<TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'> {
  label?: string;
  helperText?: string;
  error?: string;
  fullWidth?: boolean;
  variant?: 'default' | 'filled' | 'outline';
  rows?: number;
}

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      helperText,
      error,
      fullWidth = false,
      variant = 'default',
      className = '',
      disabled = false,
      rows = 4,
      ...props
    },
    ref
  ) => {
    const baseClasses = "block w-full rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0";
    
    const variantClasses = {
      default: "border border-gray-300 focus:border-primary-500 focus:ring-primary-500 bg-white dark:bg-slate-800 dark:border-slate-700 dark:text-white",
      filled: "border border-transparent bg-gray-100 focus:bg-white focus:border-primary-500 focus:ring-primary-500 dark:bg-slate-700 dark:focus:bg-slate-800 dark:text-white",
      outline: "border border-gray-300 focus:border-primary-500 focus:ring-primary-500 bg-transparent dark:border-slate-700 dark:text-white"
    };
    
    const errorClasses = error 
      ? "border-rose-500 focus:border-rose-500 focus:ring-rose-500 text-rose-500 dark:text-rose-400" 
      : "";
    
    const disabledClasses = disabled 
      ? "opacity-60 cursor-not-allowed bg-gray-100 dark:bg-slate-700" 
      : "";
    
    const widthClass = fullWidth ? "w-full" : "";
    
    const textareaClasses = [
      baseClasses,
      variantClasses[variant],
      "px-4 py-2 text-base",
      errorClasses,
      disabledClasses,
      widthClass,
      className
    ].filter(Boolean).join(' ');

    return (
      <div className={`${fullWidth ? 'w-full' : ''}`}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {label}
          </label>
        )}
        <textarea
          ref={ref}
          className={textareaClasses}
          disabled={disabled}
          rows={rows}
          {...props}
        />
        {(helperText || error) && (
          <p className={`mt-1 text-sm ${error ? 'text-rose-500 dark:text-rose-400' : 'text-gray-500 dark:text-gray-400'}`}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export default Textarea;

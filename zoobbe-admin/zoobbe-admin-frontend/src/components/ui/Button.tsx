import React from 'react';
import type { ButtonHTMLAttributes } from 'react';
import LoadingSpinner from './LoadingSpinner';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  className = '',
  type = 'button',
  onClick,
  ...props
}) => {
  // Base classes for all buttons
  const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2";

  // Variant classes
  const variantClasses = {
    primary: "bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 border border-primary-600 dark:bg-primary-700 dark:hover:bg-primary-800 dark:border-primary-700",
    secondary: "bg-gray-100 dark:bg-slate-700 text-gray-800 dark:text-white hover:bg-gray-200 dark:hover:bg-slate-600 focus:ring-gray-400 border border-gray-200 dark:border-slate-600",
    danger: "bg-rose-600 text-white hover:bg-rose-700 focus:ring-rose-500 border border-rose-600 dark:bg-rose-700 dark:hover:bg-rose-800 dark:border-rose-700",
    success: "bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 border border-green-600 dark:bg-green-700 dark:hover:bg-green-800 dark:border-green-700",
    warning: "bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-500 border border-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 dark:border-amber-600",
    outline: "bg-white dark:bg-slate-800 text-gray-700 dark:text-slate-300 border border-gray-300 dark:border-slate-600 hover:bg-gray-50 dark:hover:bg-slate-700 focus:ring-gray-400",
    ghost: "bg-transparent text-gray-700 dark:text-slate-300 hover:bg-gray-100 dark:hover:bg-slate-700 focus:ring-gray-400"
  };

  // Size classes
  const sizeClasses = {
    small: "px-3 py-1.5 text-sm",
    medium: "px-4 py-2 text-sm",
    large: "px-5 py-2.5 text-base"
  };

  // Width class
  const widthClass = fullWidth ? "w-full" : "";

  // Disabled class
  const disabledClass = (disabled || isLoading) ? "opacity-60 cursor-not-allowed pointer-events-none" : "";

  // Add hover effect for focus-visible (keyboard navigation)
  const focusVisibleClass = "focus-visible:ring-2 focus-visible:ring-offset-2";

  // Combine all classes
  const buttonClasses = [
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    widthClass,
    disabledClass,
    focusVisibleClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled || isLoading}
      onClick={onClick}
      {...props}
    >
      {isLoading && (
        <LoadingSpinner
          size="sm"
          color={variant === 'primary' || variant === 'danger' || variant === 'success' || variant === 'warning' ? 'white' : 'gray'}
          className="mr-2"
        />
      )}
      {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
      {children}
      {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
    </button>
  );
}

export default Button;

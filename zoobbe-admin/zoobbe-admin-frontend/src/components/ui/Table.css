.table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--card-bg);
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: rgba(0, 0, 0, 0.02);
  font-weight: 600;
  color: var(--text-color);
  position: sticky;
  top: 0;
  z-index: 1;
}

.table tr:last-child td {
  border-bottom: none;
}

.table tr.clickable {
  cursor: pointer;
  transition: background-color 0.2s;
}

.table tr.clickable:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.table-loading,
.table-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

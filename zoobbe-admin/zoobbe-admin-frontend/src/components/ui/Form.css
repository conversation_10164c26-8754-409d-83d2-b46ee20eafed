.form {
  width: 100%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group-full-width {
  width: 100%;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  font-size: 0.875rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: var(--card-bg);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.input-error,
.textarea-error,
.select-error {
  border-color: var(--danger-color);
}

.input-error:focus,
.textarea-error:focus,
.select-error:focus {
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.form-error {
  color: var(--danger-color);
  font-size: 0.75rem;
  margin-top: 4px;
}

.form-group-checkbox {
  margin-bottom: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.form-checkbox {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.checkbox-text {
  font-size: 0.875rem;
}

.form-group-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group-container > .form-group {
  flex: 1;
  min-width: 200px;
  margin-bottom: 0;
}

import React from 'react';
import {
  RiInformationLine,
  RiCheckboxCircleLine,
  RiErrorWarningLine,
  RiAlertLine,
  RiCloseLine
} from 'react-icons/ri';

interface AlertProps {
  children: React.ReactNode;
  variant?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  onClose?: () => void;
  className?: string;
  icon?: React.ReactNode;
}

const Alert: React.FC<AlertProps> = ({
  children,
  variant = 'info',
  title,
  onClose,
  className = '',
  icon
}) => {
  // Variant-based styling
  const variantStyles = {
    info: {
      bg: 'bg-blue-50',
      border: 'border-l-4 border-blue-500',
      icon: <RiInformationLine className="h-5 w-5 text-blue-500" />,
      title: 'text-blue-800',
      text: 'text-blue-700',
      closeButton: 'text-blue-500 hover:bg-blue-100'
    },
    success: {
      bg: 'bg-green-50',
      border: 'border-l-4 border-green-500',
      icon: <RiCheckboxCircleLine className="h-5 w-5 text-green-500" />,
      title: 'text-green-800',
      text: 'text-green-700',
      closeButton: 'text-green-500 hover:bg-green-100'
    },
    warning: {
      bg: 'bg-amber-50',
      border: 'border-l-4 border-amber-500',
      icon: <RiAlertLine className="h-5 w-5 text-amber-500" />,
      title: 'text-amber-800',
      text: 'text-amber-700',
      closeButton: 'text-amber-500 hover:bg-amber-100'
    },
    error: {
      bg: 'bg-rose-50',
      border: 'border-l-4 border-rose-500',
      icon: <RiErrorWarningLine className="h-5 w-5 text-rose-500" />,
      title: 'text-rose-800',
      text: 'text-rose-700',
      closeButton: 'text-rose-500 hover:bg-rose-100'
    }
  };

  const styles = variantStyles[variant];

  return (
    <div
      className={`
        rounded-lg p-4 ${styles.bg} ${styles.border} mb-4
        ${className}
      `}
      role="alert"
    >
      <div className="flex">
        {/* Icon */}
        <div className="flex-shrink-0 mr-3">
          {icon || styles.icon}
        </div>

        {/* Content */}
        <div className="flex-1">
          {title && (
            <h3 className={`text-sm font-medium ${styles.title}`}>
              {title}
            </h3>
          )}
          <div className={`text-sm ${styles.text} ${title ? 'mt-2' : ''}`}>
            {children}
          </div>
        </div>

        {/* Close button */}
        {onClose && (
          <div className="ml-auto pl-3">
            <button
              type="button"
              onClick={onClose}
              className={`
                inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2
                ${styles.closeButton}
              `}
            >
              <span className="sr-only">Dismiss</span>
              <RiCloseLine className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Alert;

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Variants */
.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--secondary-color);
}

.btn-secondary {
  background-color: var(--sidebar-bg);
  color: var(--text-white);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--sidebar-active);
}

.btn-danger {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.btn-danger:hover:not(:disabled) {
  background-color: #c0392b;
}

.btn-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.btn-success:hover:not(:disabled) {
  background-color: #27ae60;
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-warning:hover:not(:disabled) {
  background-color: #e67e22;
}

.btn-outline {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Sizes */
.btn-small {
  padding: 6px 12px;
  font-size: 0.875rem;
}

.btn-medium {
  padding: 8px 16px;
  font-size: 1rem;
}

.btn-large {
  padding: 12px 24px;
  font-size: 1.125rem;
}

/* Width */
.btn-full-width {
  width: 100%;
}

/* Icon */
.btn-icon {
  display: flex;
  align-items: center;
}

import React from 'react';
import { RiFileCopyLine, RiCheckLine } from 'react-icons/ri';

interface CodeBlockProps {
  code: string;
  language?: string;
  showLineNumbers?: boolean;
  title?: string;
  className?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language = 'javascript',
  showLineNumbers = true,
  title,
  className = ''
}) => {
  const [copied, setCopied] = React.useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Split code into lines for line numbers
  const codeLines = code.split('\n');

  return (
    <div className={`rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-slate-700 ${className}`}>
      {/* Header with title and copy button */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-slate-800 border-b border-gray-200 dark:border-slate-700">
        <div className="flex items-center">
          {title && (
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {title}
            </span>
          )}
          {language && !title && (
            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
              {language}
            </span>
          )}
        </div>
        <button
          type="button"
          onClick={handleCopy}
          className="p-1.5 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors duration-200"
          aria-label="Copy code"
        >
          {copied ? (
            <RiCheckLine className="h-4 w-4 text-green-500" />
          ) : (
            <RiFileCopyLine className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Code content */}
      <div className="overflow-x-auto bg-gray-50 dark:bg-slate-900">
        <pre className="p-4 text-sm">
          <code className="font-mono text-gray-800 dark:text-gray-200">
            {showLineNumbers ? (
              <table className="border-collapse">
                <tbody>
                  {codeLines.map((line, index) => (
                    <tr key={index} className="leading-relaxed">
                      <td className="pr-4 text-right select-none text-gray-400 dark:text-gray-600">
                        {index + 1}
                      </td>
                      <td className="whitespace-pre">
                        {line || ' '}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="whitespace-pre-wrap">{code}</div>
            )}
          </code>
        </pre>
      </div>
    </div>
  );
};

export default CodeBlock;

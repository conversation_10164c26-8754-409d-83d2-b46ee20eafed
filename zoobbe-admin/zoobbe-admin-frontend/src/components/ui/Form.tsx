import React from 'react';

// Input Component
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  fullWidth?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  fullWidth = false,
  className = '',
  ...props
}) => {
  const inputClasses = [
    'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors',
    error ? 'border-red-500' : 'border-gray-300',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={`mb-4 ${fullWidth ? 'w-full' : ''}`}>
      {label && <label className="block mb-2 text-sm font-medium text-gray-700">{label}</label>}
      <input className={inputClasses} {...props} />
      {error && <div className="mt-1 text-sm text-red-600">{error}</div>}
    </div>
  );
};

// Textarea Component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  fullWidth?: boolean;
}

export const Textarea: React.FC<TextareaProps> = ({
  label,
  error,
  fullWidth = false,
  className = '',
  ...props
}) => {
  const textareaClasses = [
    'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors min-h-[100px] resize-vertical',
    error ? 'border-red-500' : 'border-gray-300',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={`mb-4 ${fullWidth ? 'w-full' : ''}`}>
      {label && <label className="block mb-2 text-sm font-medium text-gray-700">{label}</label>}
      <textarea className={textareaClasses} {...props} />
      {error && <div className="mt-1 text-sm text-red-600">{error}</div>}
    </div>
  );
};

// Select Component
interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {
  label?: string;
  options: SelectOption[];
  error?: string;
  fullWidth?: boolean;
  onChange?: (value: string) => void;
}

export const Select: React.FC<SelectProps> = ({
  label,
  options,
  error,
  fullWidth = false,
  className = '',
  onChange,
  ...props
}) => {
  const selectClasses = [
    'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white',
    error ? 'border-red-500' : 'border-gray-300',
    className
  ].filter(Boolean).join(' ');

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <div className={`mb-4 ${fullWidth ? 'w-full' : ''}`}>
      {label && <label className="block mb-2 text-sm font-medium text-gray-700">{label}</label>}
      <div className="relative">
        <select
          className={selectClasses}
          onChange={handleChange}
          {...props}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
          <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
          </svg>
        </div>
      </div>
      {error && <div className="mt-1 text-sm text-red-600">{error}</div>}
    </div>
  );
};

// Checkbox Component
interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label: string;
  error?: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  error,
  className = '',
  ...props
}) => {
  const checkboxClasses = [
    'h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded transition-colors',
    error ? 'border-red-500' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="mb-4 flex items-start">
      <div className="flex items-center h-5">
        <input type="checkbox" className={checkboxClasses} {...props} />
      </div>
      <div className="ml-3 text-sm">
        <label className="font-medium text-gray-700">{label}</label>
        {error && <div className="mt-1 text-sm text-red-600">{error}</div>}
      </div>
    </div>
  );
};

// Form Component
interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
}

export const Form: React.FC<FormProps> = ({
  children,
  className = '',
  ...props
}) => {
  const formClasses = [
    'w-full',
    className
  ].filter(Boolean).join(' ');

  return (
    <form className={formClasses} {...props}>
      {children}
    </form>
  );
};

// FormGroup Component
interface FormGroupProps {
  children: React.ReactNode;
  className?: string;
}

export const FormGroup: React.FC<FormGroupProps> = ({
  children,
  className = '',
}) => {
  const groupClasses = [
    'space-y-4',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={groupClasses}>
      {children}
    </div>
  );
};

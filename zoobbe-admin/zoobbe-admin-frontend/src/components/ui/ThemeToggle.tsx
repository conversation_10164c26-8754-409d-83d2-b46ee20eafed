import React from 'react';
import { RiSunLine, RiMoonLine, RiComputerLine } from 'react-icons/ri';
import { useTheme } from '../../context/ThemeContext';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  showSystemOption?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  showLabel = false,
  showSystemOption = false
}) => {
  const { theme, isDarkMode, setTheme } = useTheme();

  const toggleTheme = () => {
    // If not showing system option, just toggle between light and dark
    if (!showSystemOption) {
      setTheme(isDarkMode ? 'light' : 'dark');
      return;
    }

    // If showing system option, cycle through all three options
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  // Get the icon based on current theme
  const getIcon = () => {
    if (showSystemOption && theme === 'system') {
      return <RiComputerLine className="h-5 w-5" />;
    }
    return isDarkMode
      ? <RiSunLine className="h-5 w-5" />
      : <RiMoonLine className="h-5 w-5" />;
  };

  // Get the label text based on current theme
  const getLabelText = () => {
    if (showSystemOption) {
      if (theme === 'light') return 'Light';
      if (theme === 'dark') return 'Dark';
      return 'System';
    }
    return isDarkMode ? 'Light mode' : 'Dark mode';
  };

  return (
    <button
      type="button"
      onClick={toggleTheme}
      className={`
        p-2 rounded-lg transition-colors duration-200 flex items-center
        ${isDarkMode
          ? 'bg-slate-700 text-yellow-300 hover:bg-slate-600'
          : 'bg-slate-100 text-slate-700 hover:bg-slate-200'}
        ${className}
      `}
      aria-label={`Switch to ${getLabelText()}`}
      title={`Switch to ${getLabelText()}`}
    >
      {getIcon()}
      {showLabel && (
        <span className="ml-2 text-sm font-medium">{getLabelText()}</span>
      )}
    </button>
  );
};

export default ThemeToggle;

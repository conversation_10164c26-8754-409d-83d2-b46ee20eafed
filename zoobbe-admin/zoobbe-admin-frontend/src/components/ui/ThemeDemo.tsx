import React from 'react';
import { useTheme } from '../../context/ThemeContext';
import ThemeToggle from './ThemeToggle';

const ThemeDemo: React.FC = () => {
  const { theme, isDarkMode } = useTheme();

  return (
    <div className="card p-6 mb-6">
      <h2 className="text-xl font-semibold mb-4">Theme Settings</h2>
      
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <span>Current Theme:</span>
          <span className="font-medium">{theme} {isDarkMode ? '(Dark)' : '(Light)'}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span>Toggle Theme:</span>
          <ThemeToggle showLabel={true} showSystemOption={true} />
        </div>
        
        <div className="mt-4">
          <h3 className="font-medium mb-2">Color Samples</h3>
          <div className="grid grid-cols-2 gap-2">
            <div className="p-3 rounded" style={{ backgroundColor: 'var(--primary-color)', color: 'white' }}>
              Primary Color
            </div>
            <div className="p-3 rounded" style={{ backgroundColor: 'var(--secondary-color)', color: 'white' }}>
              Secondary Color
            </div>
            <div className="p-3 rounded" style={{ backgroundColor: 'var(--success-color)', color: 'white' }}>
              Success Color
            </div>
            <div className="p-3 rounded" style={{ backgroundColor: 'var(--danger-color)', color: 'white' }}>
              Danger Color
            </div>
            <div className="p-3 rounded" style={{ backgroundColor: 'var(--warning-color)', color: 'white' }}>
              Warning Color
            </div>
            <div className="p-3 rounded" style={{ backgroundColor: 'var(--bg-surface)', color: 'var(--text-primary)' }}>
              Surface Color
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeDemo;

import React from 'react';

interface CardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  className?: string;
  footer?: React.ReactNode;
  headerAction?: React.ReactNode;
  icon?: React.ReactNode;
  hover?: boolean;
  variant?: 'default' | 'primary' | 'secondary' | 'outline';
}

const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  className = '',
  footer,
  headerAction,
  icon,
  hover = false,
  variant = 'default',
}) => {
  const hasHeader = title || subtitle || headerAction || icon;

  const variantClasses = {
    default: 'bg-white dark:bg-slate-800 border border-gray-100 dark:border-slate-700',
    primary: 'bg-gradient-to-r from-slate-800 to-slate-700 text-white',
    secondary: 'bg-gradient-to-r from-gray-50 to-gray-100 dark:from-slate-700 dark:to-slate-800',
    outline: 'bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700'
  };

  const headerBorderClass = variant === 'primary'
    ? 'border-slate-600/50'
    : 'border-gray-100 dark:border-slate-700';

  const footerBgClass = variant === 'primary'
    ? 'bg-slate-700/50 border-slate-600/50'
    : 'bg-gray-50 dark:bg-slate-700/50 border-gray-100 dark:border-slate-700';

  const titleTextClass = variant === 'primary'
    ? 'text-white'
    : 'text-gray-800 dark:text-white';

  const subtitleTextClass = variant === 'primary'
    ? 'text-slate-300'
    : 'text-gray-500 dark:text-gray-400';

  return (
    <div
      className={`
        ${variantClasses[variant]}
        rounded-2xl shadow-sm overflow-hidden
        ${hover ? 'transition-all duration-300 hover:shadow-md hover:translate-y-[-2px]' : ''}
        ${className}
      `}
    >
      {hasHeader && (
        <div className={`px-6 py-4 border-b ${headerBorderClass} flex justify-between items-center`}>
          <div className="flex items-center">
            {icon && (
              <div className="mr-4 flex-shrink-0">
                {icon}
              </div>
            )}
            <div>
              {title && <h3 className={`text-lg font-semibold ${titleTextClass}`}>{title}</h3>}
              {subtitle && <p className={`mt-1 text-sm ${subtitleTextClass}`}>{subtitle}</p>}
            </div>
          </div>
          {headerAction && (
            <div className="ml-4 flex-shrink-0">
              {headerAction}
            </div>
          )}
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
      {footer && (
        <div className={`px-6 py-4 ${footerBgClass} border-t`}>
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;

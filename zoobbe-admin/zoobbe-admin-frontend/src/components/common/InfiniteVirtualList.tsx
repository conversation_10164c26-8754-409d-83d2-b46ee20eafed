import React, { useEffect, useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useInView } from 'react-intersection-observer';

interface InfiniteVirtualListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  hasNextPage: boolean;
  isLoading: boolean;
  loadNextPage: () => void;
  estimateSize?: number;
  overscan?: number;
  className?: string;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
}

function InfiniteVirtualList<T>({
  items,
  renderItem,
  hasNextPage,
  isLoading,
  loadNextPage,
  estimateSize = 50,
  overscan = 10,
  className = '',
  loadingComponent = <div className="loading-more loading-indicator">Loading more items...</div>,
  emptyComponent = <div className="empty-list empty-state">No items found</div>
}: InfiniteVirtualListProps<T>) {
  const parentRef = useRef<HTMLDivElement>(null);
  const [loadMoreRef, inView] = useInView({
    threshold: 0.1,
    rootMargin: '200px',
  });

  // Load more items when the load more element comes into view
  useEffect(() => {
    if (inView && hasNextPage && !isLoading) {
      loadNextPage();
    }
  }, [inView, hasNextPage, isLoading, loadNextPage]);

  const virtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimateSize,
    overscan,
  });

  // If there are no items, show the empty component
  if (items.length === 0 && !isLoading) {
    return <>{emptyComponent}</>;
  }

  // NOTE: We need to use inline styles for dynamic values that depend on the virtualizer.
  // This is a common pattern in virtualized lists and is necessary for proper functioning.
  // The height and transform values must be calculated dynamically based on the virtualizer's state.
  // These cannot be moved to an external CSS file as they change during runtime.
  return (
    <div
      ref={parentRef}
      className={`infinite-virtual-list overflow-auto relative h-full ${className}`}
    >
      <div
        className="w-full relative"
        style={{
          height: `${virtualizer.getTotalSize()}px`,
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            className="absolute top-0 left-0 w-full"
            style={{
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`,
            }}
          >
            {renderItem(items[virtualItem.index], virtualItem.index)}
          </div>
        ))}
      </div>

      {/* Load more trigger element */}
      {(hasNextPage || isLoading) && (
        <div ref={loadMoreRef} className="h-5 w-full">
          {isLoading && loadingComponent}
        </div>
      )}
    </div>
  );
}

export default InfiniteVirtualList;

import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';

const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('adminToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    console.log('API Interceptor: Request failed', {
      url: originalRequest?.url,
      status: error.response?.status,
      message: error.message
    });

    // If the error is 401 and we haven't already tried to refresh the token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      console.log('API Interceptor: Attempting token refresh');

      try {
        // Try to refresh the token
        const refreshResponse = await axios.post(
          `${API_URL}/auth/refresh-token`,
          {},
          { withCredentials: true }
        );

        if (refreshResponse.status === 200) {
          console.log('API Interceptor: Token refresh successful');
          // If we got a new token, update it in localStorage
          const { token } = refreshResponse.data;
          if (token) {
            localStorage.setItem('adminToken', token);
            // Update the Authorization header for the original request
            originalRequest.headers.Authorization = `Bearer ${token}`;
          }

          // Retry the original request
          return api(originalRequest);
        }
      } catch (refreshError) {
        console.error('API Interceptor: Token refresh failed:', refreshError);
        // If refresh failed, clear token and redirect to login only if not already on login page
        localStorage.removeItem('adminToken');
        if (!window.location.pathname.includes('/login')) {
          console.log('API Interceptor: Redirecting to login');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),

  logout: () =>
    api.post('/auth/logout'),

  getProfile: () =>
    api.get('/auth/profile'),

  refreshToken: () =>
    api.post('/auth/refresh-token'),
};

// Users API
export const usersAPI = {
  getUsers: (params: { page?: number; limit?: number; status?: string; search?: string }) =>
    api.get('/users', { params }),

  getUserById: (userId: string) =>
    api.get(`/users/${userId}`),

  updateUserStatus: (userId: string, status: string, reason?: string) =>
    api.patch(`/users/${userId}/status`, { status, reason }),

  getUserStats: () =>
    api.get('/users/stats'),
};

// Workspaces API
export const workspacesAPI = {
  getWorkspaces: (params: { page?: number; limit?: number; status?: string; search?: string }) =>
    api.get('/workspaces', { params }),

  getWorkspaceById: (workspaceId: string) =>
    api.get(`/workspaces/${workspaceId}`),

  updateWorkspaceStatus: (workspaceId: string, status: string, reason?: string) =>
    api.patch(`/workspaces/${workspaceId}/status`, { status, reason }),

  getWorkspaceStats: () =>
    api.get('/workspaces/stats'),
};

// Boards API
export const boardsAPI = {
  getBoards: (params: { page?: number; limit?: number; status?: string; search?: string; workspace?: string; visibility?: string }) =>
    api.get('/admin/boards', { params }),

  getBoardById: (boardId: string) =>
    api.get(`/admin/boards/${boardId}`),

  updateBoardStatus: (boardId: string, status: string) =>
    api.patch(`/admin/boards/${boardId}/status`, { status }),

  updateBoardDetails: (boardId: string, data: { title?: string; visibility?: string }) =>
    api.patch(`/admin/boards/${boardId}`, data),

  transferBoard: (boardId: string, workspaceId: string) =>
    api.post(`/admin/boards/${boardId}/transfer`, { workspaceId }),

  getBoardMembers: (boardId: string) =>
    api.get(`/admin/boards/${boardId}/members`),

  updateBoardMemberRole: (boardId: string, userId: string, role: string) =>
    api.patch(`/admin/boards/${boardId}/members/${userId}`, { role }),

  removeBoardMember: (boardId: string, userId: string) =>
    api.delete(`/admin/boards/${boardId}/members/${userId}`),
};

// Cards API
export const cardsAPI = {
  getCards: (params: { page?: number; limit?: number; status?: string; search?: string; board?: string; priority?: string }) =>
    api.get('/admin/cards', { params }),

  getCardById: (cardId: string) =>
    api.get(`/admin/cards/${cardId}`),

  updateCardStatus: (cardId: string, status: string) =>
    api.patch(`/admin/cards/${cardId}/status`, { status }),
};

// Subscriptions API
export const subscriptionsAPI = {
  getSubscriptions: (params: { page?: number; limit?: number; status?: string; search?: string; plan?: string }) =>
    api.get('/admin/subscriptions', { params }),

  getSubscriptionById: (subscriptionId: string) =>
    api.get(`/admin/subscriptions/${subscriptionId}`),

  updateSubscriptionStatus: (subscriptionId: string, status: string) =>
    api.patch(`/admin/subscriptions/${subscriptionId}/status`, { status }),

  getSubscriptionPlans: () =>
    api.get('/admin/subscription-plans'),

  updateSubscriptionPlan: (planId: string, planData: any) =>
    api.patch(`/admin/subscription-plans/${planId}`, planData),
};

// Settings API
export const settingsAPI = {
  getSystemSettings: () =>
    api.get('/admin/settings'),

  updateSystemSettings: (settingsData: any) =>
    api.patch('/admin/settings', settingsData),
};

// Stats API
export const statsAPI = {
  getSystemStats: () =>
    api.get('/stats'),

  getUserGrowthStats: (period: string = 'month') =>
    api.get(`/stats/users/growth?period=${period}`),

  getWorkspaceGrowthStats: (period: string = 'month') =>
    api.get(`/stats/workspaces/growth?period=${period}`),

  getActivityStats: (period: string = 'week') =>
    api.get(`/stats/activity?period=${period}`),

  getSubscriptionStats: (period: string = 'month') =>
    api.get(`/stats/subscriptions?period=${period}`),
};

// Activity API
export const activityAPI = {
  getActivityLog: (params: { page?: number; limit?: number; type?: string; search?: string }) =>
    api.get('/activity', { params }),

  getActivityTypes: () =>
    api.get('/activity/types'),
};

// Feature Flags API
export const featureFlagsAPI = {
  getFeatureFlags: (queryParams?: string) =>
    api.get(`/admin/feature-flags${queryParams ? `?${queryParams}` : ''}`),

  getFeatureFlagById: (flagId: string) =>
    api.get(`/admin/feature-flags/${flagId}`),

  createFeatureFlag: (flagData: any) =>
    api.post('/admin/feature-flags', flagData),

  updateFeatureFlag: (flagId: string, flagData: any) =>
    api.put(`/admin/feature-flags/${flagId}`, flagData),

  deleteFeatureFlag: (flagId: string) =>
    api.delete(`/admin/feature-flags/${flagId}`),
};

// Feedback API
export const feedbackAPI = {
  getFeedback: (params: { page?: number; limit?: number; status?: string; type?: string; priority?: string; search?: string }) =>
    api.get('/admin/feedback', { params }),

  getFeedbackById: (feedbackId: string) =>
    api.get(`/admin/feedback/${feedbackId}`),

  updateFeedbackStatus: (feedbackId: string, status: string) =>
    api.patch(`/admin/feedback/${feedbackId}/status`, { status }),

  addFeedbackResponse: (feedbackId: string, message: string) =>
    api.post(`/admin/feedback/${feedbackId}/response`, { message }),

  addInternalNote: (feedbackId: string, note: string) =>
    api.post(`/admin/feedback/${feedbackId}/note`, { note }),

  assignFeedback: (feedbackId: string, adminId: string) =>
    api.post(`/admin/feedback/${feedbackId}/assign`, { adminId }),
};

// Convenience functions for components
export const getFeatureFlags = (queryParams?: string) =>
  featureFlagsAPI.getFeatureFlags(queryParams).then(res => res.data);

export const getFeatureFlagById = (flagId: string) =>
  featureFlagsAPI.getFeatureFlagById(flagId).then(res => res.data);

export const createFeatureFlag = (flagData: any) =>
  featureFlagsAPI.createFeatureFlag(flagData).then(res => res.data);

export const updateFeatureFlag = (flagId: string, flagData: any) =>
  featureFlagsAPI.updateFeatureFlag(flagId, flagData).then(res => res.data);

export const deleteFeatureFlag = (flagId: string) =>
  featureFlagsAPI.deleteFeatureFlag(flagId).then(res => res.data);

export const getFeedback = (params: any) =>
  feedbackAPI.getFeedback(params).then(res => res.data);

export const getFeedbackById = (feedbackId: string) =>
  feedbackAPI.getFeedbackById(feedbackId).then(res => res.data);

export const updateFeedbackStatus = (feedbackId: string, status: string) =>
  feedbackAPI.updateFeedbackStatus(feedbackId, status).then(res => res.data);

export const addFeedbackResponse = (feedbackId: string, message: string) =>
  feedbackAPI.addFeedbackResponse(feedbackId, message).then(res => res.data);

export const addInternalNote = (feedbackId: string, note: string) =>
  feedbackAPI.addInternalNote(feedbackId, note).then(res => res.data);

export const assignFeedback = (feedbackId: string, adminId: string) =>
  feedbackAPI.assignFeedback(feedbackId, adminId).then(res => res.data);

// Stats convenience functions
export const getSystemStats = () =>
  statsAPI.getSystemStats().then(res => res.data);

export const getUserGrowthStats = (period: string = 'month') =>
  statsAPI.getUserGrowthStats(period).then(res => res.data);

export const getWorkspaceGrowthStats = (period: string = 'month') =>
  statsAPI.getWorkspaceGrowthStats(period).then(res => res.data);

export const getActivityStats = (period: string = 'week') =>
  statsAPI.getActivityStats(period).then(res => res.data);

export const getSubscriptionStats = (period: string = 'month') =>
  statsAPI.getSubscriptionStats(period).then(res => res.data);

// Board convenience functions
export const getBoards = (params: any) =>
  boardsAPI.getBoards(params).then(res => res.data);

export const getBoardById = (boardId: string) =>
  boardsAPI.getBoardById(boardId).then(res => res.data);

export const updateBoardStatus = (boardId: string, status: string) =>
  boardsAPI.updateBoardStatus(boardId, status).then(res => res.data);

export const updateBoardDetails = (boardId: string, data: { title?: string; visibility?: string }) =>
  boardsAPI.updateBoardDetails(boardId, data).then(res => res.data);

export const transferBoard = (boardId: string, workspaceId: string) =>
  boardsAPI.transferBoard(boardId, workspaceId).then(res => res.data);

export const getBoardMembers = (boardId: string) =>
  boardsAPI.getBoardMembers(boardId).then(res => res.data);

export const updateBoardMemberRole = (boardId: string, userId: string, role: string) =>
  boardsAPI.updateBoardMemberRole(boardId, userId, role).then(res => res.data);

export const removeBoardMember = (boardId: string, userId: string) =>
  boardsAPI.removeBoardMember(boardId, userId).then(res => res.data);

export default api;

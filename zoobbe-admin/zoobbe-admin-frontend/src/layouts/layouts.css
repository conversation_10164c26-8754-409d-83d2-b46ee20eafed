/* Layout styles using CSS variables */

/* Auth Layout */
.auth-layout {
  background: linear-gradient(to bottom right, var(--primary-color), var(--secondary-color));
}

.dark .auth-layout {
  background: linear-gradient(to bottom right, var(--bg-body), var(--bg-elevated));
}

/* Admin Layout */
.admin-layout {
  background-color: var(--bg-body);
  color: var(--text-primary);
}

.admin-layout main {
  background-color: var(--bg-body);
}

.admin-layout footer {
  background-color: var(--bg-surface);
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Sidebar */
.sidebar {
  background-color: var(--bg-surface);
  border-right: 1px solid var(--border-color);
  span{
    line-height: 0;
  }
}

/* Header */
.header {
  background-color: var(--bg-surface);
  border-bottom: 1px solid var(--border-color);
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  box-shadow: var(--card-shadow);
  border-radius: var(--border-radius-md);
}

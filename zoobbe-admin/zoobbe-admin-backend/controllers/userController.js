const User = require('../models/User');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Activity = require('../models/Activity');
const LoginHistory = require('../models/LoginHistory');

// Get all users with pagination, filtering, and search
const getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search, plan, joinDateStart, joinDateEnd } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};

    // Add status filter if provided
    if (status) {
      if (status === 'active') {
        query.online = true;
        query.suspended = false;
      } else if (status === 'inactive') {
        query.online = false;
        query.suspended = false;
      } else if (status === 'unverified') {
        query.verified = false;
      } else if (status === 'suspended') {
        query.suspended = true;
      }
    }

    // Add plan filter if provided
    if (plan) {
      query.plan = plan;
    }

    // Add join date filter if provided
    if (joinDateStart || joinDateEnd) {
      query.createdAt = {};
      if (joinDateStart) {
        query.createdAt.$gte = new Date(joinDateStart);
      }
      if (joinDateEnd) {
        query.createdAt.$lte = new Date(joinDateEnd);
      }
    }

    // Add search filter if provided
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { username: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const users = await User.find(query)
      .select('_id name email username profilePicture type verified online createdAt suspended plan lastLoginAt')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await User.countDocuments(query);

    // Return users with pagination info
    res.status(200).json({
      users,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting users:', error);
    res.status(500).json({ message: 'Error getting users' });
  }
};

// Get user by ID
const getUserById = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find user
    const user = await User.findById(userId)
      .select('-passwordHash')
      .populate('workspaces', 'name shortName shortId')
      .populate('guests', 'title shortId')
      .populate('subscription');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get login history
    const loginHistory = await LoginHistory.find({ user: userId })
      .sort({ timestamp: -1 })
      .limit(10);

    // Get user activities
    const activities = await Activity.find({ user: userId })
      .sort({ createdAt: -1 })
      .limit(20);

    // Return user with additional data
    res.status(200).json({
      user,
      loginHistory,
      activities
    });
  } catch (error) {
    console.error('Error getting user:', error);
    res.status(500).json({ message: 'Error getting user' });
  }
};

// Update user status
const updateUserStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    const { status, reason } = req.body;
    const adminId = req.admin.id;

    // Validate input
    if (!userId || !status) {
      return res.status(400).json({ message: 'User ID and status are required' });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user based on status
    switch (status) {
      case 'admin':
        user.type = 'ADMIN';

        // Log activity
        await Activity.create({
          user: userId,
          action: 'PROMOTED_TO_ADMIN',
          targetType: 'USER',
          target: userId,
          metadata: {
            adminId,
            reason
          }
        });
        break;

      case 'user':
        user.type = 'USER';

        // Log activity
        await Activity.create({
          user: userId,
          action: 'DEMOTED_TO_USER',
          targetType: 'USER',
          target: userId,
          metadata: {
            adminId,
            reason
          }
        });
        break;

      case 'suspended':
        user.suspended = true;
        user.suspensionReason = reason;
        user.suspendedAt = new Date();
        user.suspendedBy = adminId;

        // Log activity
        await Activity.create({
          user: userId,
          action: 'ACCOUNT_SUSPENDED',
          targetType: 'USER',
          target: userId,
          metadata: {
            adminId,
            reason
          }
        });
        break;

      case 'active':
        user.suspended = false;
        user.suspensionReason = null;
        user.suspendedAt = null;
        user.suspendedBy = null;

        // Log activity
        await Activity.create({
          user: userId,
          action: 'ACCOUNT_REACTIVATED',
          targetType: 'USER',
          target: userId,
          metadata: {
            adminId,
            reason
          }
        });
        break;

      case 'verify':
        user.verified = true;

        // Log activity
        await Activity.create({
          user: userId,
          action: 'ACCOUNT_VERIFIED',
          targetType: 'USER',
          target: userId,
          metadata: {
            adminId,
            reason
          }
        });
        break;

      default:
        return res.status(400).json({ message: 'Invalid status' });
    }

    // Save user
    await user.save();

    // Return updated user
    res.status(200).json({
      message: 'User status updated successfully',
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        username: user.username,
        type: user.type,
        verified: user.verified,
        suspended: user.suspended,
        suspensionReason: user.suspensionReason,
        suspendedAt: user.suspendedAt
      }
    });
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({ message: 'Error updating user status' });
  }
};

// Get user statistics
const getUserStats = async (req, res) => {
  try {
    // Get total users
    const totalUsers = await User.countDocuments();

    // Get verified users
    const verifiedUsers = await User.countDocuments({ verified: true });

    // Get active users (online)
    const activeUsers = await User.countDocuments({ online: true });

    // Get users created in the last 30 days
    const newUsers = await User.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Return stats
    res.status(200).json({
      totalUsers,
      verifiedUsers,
      activeUsers,
      newUsers,
      unverifiedUsers: totalUsers - verifiedUsers
    });
  } catch (error) {
    console.error('Error getting user stats:', error);
    res.status(500).json({ message: 'Error getting user statistics' });
  }
};

// Impersonate user (generate impersonation token)
const impersonateUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const adminId = req.admin.id;

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Generate impersonation token (short-lived)
    const impersonationToken = jwt.sign(
      {
        id: user._id,
        impersonatedBy: adminId,
        isImpersonation: true
      },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    // Log activity
    await Activity.create({
      user: userId,
      action: 'USER_IMPERSONATED',
      targetType: 'USER',
      target: userId,
      metadata: {
        adminId
      }
    });

    // Return token
    res.status(200).json({
      message: 'Impersonation token generated successfully',
      impersonationToken,
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        username: user.username
      }
    });
  } catch (error) {
    console.error('Error impersonating user:', error);
    res.status(500).json({ message: 'Error impersonating user' });
  }
};

// Reset user password
const resetUserPassword = async (req, res) => {
  try {
    const { userId } = req.params;
    const adminId = req.admin.id;

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Generate temporary password
    const tempPassword = Math.random().toString(36).slice(-8);

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(tempPassword, salt);

    // Update user
    user.passwordHash = hashedPassword;
    user.isDefaultPasswordSet = true;
    await user.save();

    // Log activity
    await Activity.create({
      user: userId,
      action: 'PASSWORD_RESET',
      targetType: 'USER',
      target: userId,
      metadata: {
        adminId
      }
    });

    // Return temporary password
    res.status(200).json({
      message: 'User password reset successfully',
      tempPassword
    });
  } catch (error) {
    console.error('Error resetting user password:', error);
    res.status(500).json({ message: 'Error resetting user password' });
  }
};

// Get user login history
const getUserLoginHistory = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get login history
    const loginHistory = await LoginHistory.find({ user: userId })
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await LoginHistory.countDocuments({ user: userId });

    // Return login history with pagination info
    res.status(200).json({
      loginHistory,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting user login history:', error);
    res.status(500).json({ message: 'Error getting user login history' });
  }
};

// Add notes to user
const addUserNotes = async (req, res) => {
  try {
    const { userId } = req.params;
    const { notes } = req.body;
    const adminId = req.admin.id;

    // Validate input
    if (!notes) {
      return res.status(400).json({ message: 'Notes are required' });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user
    user.notes = notes;
    await user.save();

    // Log activity
    await Activity.create({
      user: userId,
      action: 'NOTES_UPDATED',
      targetType: 'USER',
      target: userId,
      metadata: {
        adminId
      }
    });

    // Return success
    res.status(200).json({
      message: 'User notes updated successfully',
      user: {
        _id: user._id,
        name: user.name,
        notes: user.notes
      }
    });
  } catch (error) {
    console.error('Error updating user notes:', error);
    res.status(500).json({ message: 'Error updating user notes' });
  }
};

module.exports = {
  getUsers,
  getUserById,
  updateUserStatus,
  getUserStats,
  impersonateUser,
  resetUserPassword,
  getUserLoginHistory,
  addUserNotes
};

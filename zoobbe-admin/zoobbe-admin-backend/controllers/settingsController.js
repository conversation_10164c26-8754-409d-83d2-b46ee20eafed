const SystemSettings = require('../models/SystemSettings');
const Activity = require('../models/Activity');

// Get system settings
const getSystemSettings = async (req, res) => {
  try {
    // Find settings (there should only be one document)
    let settings = await SystemSettings.findOne()
      .populate('updatedBy', 'name email');
    
    // If no settings exist, create default settings
    if (!settings) {
      settings = await SystemSettings.create({});
    }
    
    res.status(200).json({ settings });
  } catch (error) {
    console.error('Error getting system settings:', error);
    res.status(500).json({ message: 'Error getting system settings' });
  }
};

// Update system settings
const updateSystemSettings = async (req, res) => {
  try {
    const {
      maintenanceMode,
      allowedDomains,
      maxFileSize,
      maxFilesPerCard,
      maxBoardsPerWorkspace,
      maxMembersPerWorkspace,
      maxCardsPerBoard,
      integrations,
      security,
      email,
      analytics
    } = req.body;
    const adminId = req.admin.id;
    
    // Find settings (there should only be one document)
    let settings = await SystemSettings.findOne();
    
    // If no settings exist, create default settings
    if (!settings) {
      settings = new SystemSettings({});
    }
    
    // Update fields if provided
    if (maintenanceMode !== undefined) settings.maintenanceMode = maintenanceMode;
    if (allowedDomains !== undefined) settings.allowedDomains = allowedDomains;
    if (maxFileSize !== undefined) settings.maxFileSize = maxFileSize;
    if (maxFilesPerCard !== undefined) settings.maxFilesPerCard = maxFilesPerCard;
    if (maxBoardsPerWorkspace !== undefined) settings.maxBoardsPerWorkspace = maxBoardsPerWorkspace;
    if (maxMembersPerWorkspace !== undefined) settings.maxMembersPerWorkspace = maxMembersPerWorkspace;
    if (maxCardsPerBoard !== undefined) settings.maxCardsPerBoard = maxCardsPerBoard;
    if (integrations !== undefined) settings.integrations = integrations;
    if (security !== undefined) settings.security = security;
    if (email !== undefined) settings.email = email;
    if (analytics !== undefined) settings.analytics = analytics;
    
    settings.updatedBy = adminId;
    settings.updatedAt = new Date();
    
    await settings.save();
    
    // Log activity
    await Activity.create({
      user: adminId,
      action: 'SYSTEM_SETTINGS_UPDATED',
      targetType: 'SYSTEM',
      metadata: {
        adminId
      }
    });
    
    res.status(200).json({
      message: 'System settings updated successfully',
      settings
    });
  } catch (error) {
    console.error('Error updating system settings:', error);
    res.status(500).json({ message: 'Error updating system settings' });
  }
};

// Toggle maintenance mode
const toggleMaintenanceMode = async (req, res) => {
  try {
    const { enabled, message, scheduledStart, scheduledEnd } = req.body;
    const adminId = req.admin.id;
    
    // Find settings (there should only be one document)
    let settings = await SystemSettings.findOne();
    
    // If no settings exist, create default settings
    if (!settings) {
      settings = new SystemSettings({});
    }
    
    // Update maintenance mode
    settings.maintenanceMode = {
      enabled: enabled !== undefined ? enabled : settings.maintenanceMode.enabled,
      message: message || settings.maintenanceMode.message,
      scheduledStart: scheduledStart ? new Date(scheduledStart) : settings.maintenanceMode.scheduledStart,
      scheduledEnd: scheduledEnd ? new Date(scheduledEnd) : settings.maintenanceMode.scheduledEnd
    };
    
    settings.updatedBy = adminId;
    settings.updatedAt = new Date();
    
    await settings.save();
    
    // Log activity
    await Activity.create({
      user: adminId,
      action: enabled ? 'MAINTENANCE_MODE_ENABLED' : 'MAINTENANCE_MODE_DISABLED',
      targetType: 'SYSTEM',
      metadata: {
        adminId,
        maintenanceMode: settings.maintenanceMode
      }
    });
    
    res.status(200).json({
      message: `Maintenance mode ${enabled ? 'enabled' : 'disabled'} successfully`,
      settings
    });
  } catch (error) {
    console.error('Error toggling maintenance mode:', error);
    res.status(500).json({ message: 'Error toggling maintenance mode' });
  }
};

// Update allowed domains
const updateAllowedDomains = async (req, res) => {
  try {
    const { domains } = req.body;
    const adminId = req.admin.id;
    
    if (!domains || !Array.isArray(domains)) {
      return res.status(400).json({ message: 'Domains must be an array' });
    }
    
    // Find settings (there should only be one document)
    let settings = await SystemSettings.findOne();
    
    // If no settings exist, create default settings
    if (!settings) {
      settings = new SystemSettings({});
    }
    
    // Update allowed domains
    settings.allowedDomains = domains;
    settings.updatedBy = adminId;
    settings.updatedAt = new Date();
    
    await settings.save();
    
    // Log activity
    await Activity.create({
      user: adminId,
      action: 'ALLOWED_DOMAINS_UPDATED',
      targetType: 'SYSTEM',
      metadata: {
        adminId,
        domains
      }
    });
    
    res.status(200).json({
      message: 'Allowed domains updated successfully',
      settings
    });
  } catch (error) {
    console.error('Error updating allowed domains:', error);
    res.status(500).json({ message: 'Error updating allowed domains' });
  }
};

// Toggle integration
const toggleIntegration = async (req, res) => {
  try {
    const { integration, enabled } = req.body;
    const adminId = req.admin.id;
    
    if (!integration) {
      return res.status(400).json({ message: 'Integration name is required' });
    }
    
    if (enabled === undefined) {
      return res.status(400).json({ message: 'Enabled status is required' });
    }
    
    // Find settings (there should only be one document)
    let settings = await SystemSettings.findOne();
    
    // If no settings exist, create default settings
    if (!settings) {
      settings = new SystemSettings({});
    }
    
    // Check if integration exists
    if (!settings.integrations[integration]) {
      return res.status(400).json({ message: 'Invalid integration name' });
    }
    
    // Update integration
    settings.integrations[integration].enabled = enabled;
    settings.updatedBy = adminId;
    settings.updatedAt = new Date();
    
    await settings.save();
    
    // Log activity
    await Activity.create({
      user: adminId,
      action: enabled ? 'INTEGRATION_ENABLED' : 'INTEGRATION_DISABLED',
      targetType: 'SYSTEM',
      metadata: {
        adminId,
        integration,
        enabled
      }
    });
    
    res.status(200).json({
      message: `${integration} integration ${enabled ? 'enabled' : 'disabled'} successfully`,
      settings
    });
  } catch (error) {
    console.error('Error toggling integration:', error);
    res.status(500).json({ message: 'Error toggling integration' });
  }
};

module.exports = {
  getSystemSettings,
  updateSystemSettings,
  toggleMaintenanceMode,
  updateAllowedDomains,
  toggleIntegration
};

const Workspace = require('../models/Workspace');
const Board = require('../models/Board');

// Get all workspaces with pagination, filtering, and search
const getWorkspaces = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};

    // Add status filter if provided
    if (status) {
      if (status === 'active') {
        query.status = 'active';
      } else if (status === 'suspended') {
        query.status = 'suspended';
      }
    }

    // Add search filter if provided
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const workspaces = await Workspace.find(query)
      .populate('members.user', '_id name email username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Workspace.countDocuments(query);

    // Return workspaces with pagination info
    res.status(200).json({
      workspaces,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting workspaces:', error);
    res.status(500).json({ message: 'Error getting workspaces' });
  }
};

// Get workspace by ID
const getWorkspaceById = async (req, res) => {
  try {
    const { workspaceId } = req.params;

    // Find workspace
    const workspace = await Workspace.findById(workspaceId)
      .populate('members.user', '_id name email username')
      .populate('boards', 'title');

    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Get boards count
    const boardsCount = await Board.countDocuments({ workspace: workspaceId });

    // Return workspace with additional info
    res.status(200).json({
      workspace,
      boardsCount
    });
  } catch (error) {
    console.error('Error getting workspace:', error);
    res.status(500).json({ message: 'Error getting workspace' });
  }
};

// Update workspace status
const updateWorkspaceStatus = async (req, res) => {
  try {
    const { workspaceId } = req.params;
    const { status, reason } = req.body;

    // Validate input
    if (!workspaceId || !status) {
      return res.status(400).json({ message: 'Workspace ID and status are required' });
    }

    // Find workspace
    const workspace = await Workspace.findById(workspaceId);
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Update workspace based on status
    switch (status) {
      case 'active':
        workspace.status = 'active';
        break;
      case 'suspended':
        workspace.status = 'suspended';
        break;
      default:
        return res.status(400).json({ message: 'Invalid status' });
    }

    // Save workspace
    await workspace.save();

    // Return updated workspace
    res.status(200).json({
      message: 'Workspace status updated successfully',
      workspace: {
        _id: workspace._id,
        name: workspace.name,
        status: workspace.status
      }
    });
  } catch (error) {
    console.error('Error updating workspace status:', error);
    res.status(500).json({ message: 'Error updating workspace status' });
  }
};

// Get workspace statistics
const getWorkspaceStats = async (req, res) => {
  try {
    // Get total workspaces
    const totalWorkspaces = await Workspace.countDocuments();

    // Get active workspaces
    const activeWorkspaces = await Workspace.countDocuments({ status: 'active' });

    // Get workspaces created in the last 30 days
    const newWorkspaces = await Workspace.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get total boards
    const totalBoards = await Board.countDocuments();

    // Return stats
    res.status(200).json({
      totalWorkspaces,
      activeWorkspaces,
      newWorkspaces,
      totalBoards,
      suspendedWorkspaces: totalWorkspaces - activeWorkspaces
    });
  } catch (error) {
    console.error('Error getting workspace stats:', error);
    res.status(500).json({ message: 'Error getting workspace statistics' });
  }
};

module.exports = {
  getWorkspaces,
  getWorkspaceById,
  updateWorkspaceStatus,
  getWorkspaceStats
};

const Feedback = require('../models/Feedback');
const User = require('../models/User');
const Activity = require('../models/Activity');

// Get all feedback with pagination, filtering, and search
const getFeedback = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, type, priority, search } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};

    // Add status filter if provided
    if (status) {
      query.status = status;
    }

    // Add type filter if provided
    if (type) {
      query.type = type;
    }

    // Add priority filter if provided
    if (priority) {
      query.priority = priority;
    }

    // Add search filter if provided
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const feedback = await Feedback.find(query)
      .populate('user', '_id name email username')
      .populate('assignedTo', '_id name email')
      .populate('resolvedBy', '_id name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Feedback.countDocuments(query);

    // Return feedback with pagination info
    res.status(200).json({
      feedback,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting feedback:', error);
    res.status(500).json({ message: 'Error getting feedback' });
  }
};

// Get feedback by ID
const getFeedbackById = async (req, res) => {
  try {
    const { feedbackId } = req.params;

    // Find feedback
    const feedback = await Feedback.findById(feedbackId)
      .populate('user', '_id name email username')
      .populate('assignedTo', '_id name email')
      .populate('resolvedBy', '_id name email')
      .populate('responses.admin', '_id name email')
      .populate('internalNotes.admin', '_id name email');

    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    // Return feedback
    res.status(200).json({ feedback });
  } catch (error) {
    console.error('Error getting feedback:', error);
    res.status(500).json({ message: 'Error getting feedback' });
  }
};

// Update feedback status
const updateFeedbackStatus = async (req, res) => {
  try {
    const { feedbackId } = req.params;
    const { status } = req.body;
    const adminId = req.admin.id;

    // Validate input
    if (!status) {
      return res.status(400).json({ message: 'Status is required' });
    }

    // Find feedback
    const feedback = await Feedback.findById(feedbackId);
    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    // Update feedback status
    feedback.status = status;
    
    // If status is resolved, set resolvedAt and resolvedBy
    if (status === 'resolved') {
      feedback.resolvedAt = new Date();
      feedback.resolvedBy = adminId;
    }
    
    await feedback.save();

    // Log activity
    await Activity.create({
      user: adminId,
      action: 'FEEDBACK_STATUS_UPDATED',
      targetType: 'FEEDBACK',
      target: feedbackId,
      metadata: {
        oldStatus: feedback.status,
        newStatus: status
      }
    });

    // Return updated feedback
    res.status(200).json({
      message: 'Feedback status updated successfully',
      feedback
    });
  } catch (error) {
    console.error('Error updating feedback status:', error);
    res.status(500).json({ message: 'Error updating feedback status' });
  }
};

// Add response to feedback
const addFeedbackResponse = async (req, res) => {
  try {
    const { feedbackId } = req.params;
    const { message } = req.body;
    const adminId = req.admin.id;

    // Validate input
    if (!message) {
      return res.status(400).json({ message: 'Response message is required' });
    }

    // Find feedback
    const feedback = await Feedback.findById(feedbackId);
    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    // Add response
    feedback.responses.push({
      admin: adminId,
      message,
      createdAt: new Date()
    });
    
    await feedback.save();

    // Log activity
    await Activity.create({
      user: adminId,
      action: 'FEEDBACK_RESPONSE_ADDED',
      targetType: 'FEEDBACK',
      target: feedbackId
    });

    // Return updated feedback
    res.status(200).json({
      message: 'Response added successfully',
      feedback
    });
  } catch (error) {
    console.error('Error adding feedback response:', error);
    res.status(500).json({ message: 'Error adding feedback response' });
  }
};

// Add internal note to feedback
const addInternalNote = async (req, res) => {
  try {
    const { feedbackId } = req.params;
    const { note } = req.body;
    const adminId = req.admin.id;

    // Validate input
    if (!note) {
      return res.status(400).json({ message: 'Note is required' });
    }

    // Find feedback
    const feedback = await Feedback.findById(feedbackId);
    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    // Add internal note
    feedback.internalNotes.push({
      admin: adminId,
      note,
      createdAt: new Date()
    });
    
    await feedback.save();

    // Return updated feedback
    res.status(200).json({
      message: 'Internal note added successfully',
      feedback
    });
  } catch (error) {
    console.error('Error adding internal note:', error);
    res.status(500).json({ message: 'Error adding internal note' });
  }
};

// Assign feedback to admin
const assignFeedback = async (req, res) => {
  try {
    const { feedbackId } = req.params;
    const { adminId } = req.body;
    const currentAdminId = req.admin.id;

    // Find feedback
    const feedback = await Feedback.findById(feedbackId);
    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    // Update assigned admin
    feedback.assignedTo = adminId;
    await feedback.save();

    // Log activity
    await Activity.create({
      user: currentAdminId,
      action: 'FEEDBACK_ASSIGNED',
      targetType: 'FEEDBACK',
      target: feedbackId,
      metadata: {
        assignedTo: adminId
      }
    });

    // Return updated feedback
    res.status(200).json({
      message: 'Feedback assigned successfully',
      feedback
    });
  } catch (error) {
    console.error('Error assigning feedback:', error);
    res.status(500).json({ message: 'Error assigning feedback' });
  }
};

module.exports = {
  getFeedback,
  getFeedbackById,
  updateFeedbackStatus,
  addFeedbackResponse,
  addInternalNote,
  assignFeedback
};

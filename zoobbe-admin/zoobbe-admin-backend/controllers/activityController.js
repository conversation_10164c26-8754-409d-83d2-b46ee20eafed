const Activity = require('../models/Activity');

// Get activity log with pagination, filtering, and search
const getActivityLog = async (req, res) => {
  try {
    const { page = 1, limit = 10, type, search, startDate, endDate } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    let query = {};

    // Add type filter if provided
    if (type) {
      query.action = type;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    // Add search filter if provided
    if (search) {
      // This is a simplified search - you might need to adjust based on your data structure
      query.$or = [
        { 'user.name': { $regex: search, $options: 'i' } },
        { 'user.email': { $regex: search, $options: 'i' } },
        { action: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const activities = await Activity.find(query)
      .populate('user', '_id name email username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Activity.countDocuments(query);

    // Return activities with pagination info
    res.status(200).json({
      activities,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting activity log:', error);
    res.status(500).json({ message: 'Error getting activity log' });
  }
};

// Get activity types (for filtering)
const getActivityTypes = async (req, res) => {
  try {
    // Get distinct activity types
    const types = await Activity.distinct('action');

    // Return types
    res.status(200).json({ types });
  } catch (error) {
    console.error('Error getting activity types:', error);
    res.status(500).json({ message: 'Error getting activity types' });
  }
};

// Get activity statistics
const getActivityStats = async (req, res) => {
  try {
    // Get total activities
    const totalActivities = await Activity.countDocuments();

    // Get activities in the last 24 hours
    const recentActivities = await Activity.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    // Get activities by type
    const activityByType = await Activity.aggregate([
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Return stats
    res.status(200).json({
      totalActivities,
      recentActivities,
      activityByType
    });
  } catch (error) {
    console.error('Error getting activity stats:', error);
    res.status(500).json({ message: 'Error getting activity statistics' });
  }
};

module.exports = {
  getActivityLog,
  getActivityTypes,
  getActivityStats
};

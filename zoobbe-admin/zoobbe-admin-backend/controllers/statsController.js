const User = require('../models/User');
const Workspace = require('../models/Workspace');
const Board = require('../models/Board');
const Card = require('../models/Card');
const Activity = require('../models/Activity');
const Subscription = require('../models/Subscription');

// Get system overview statistics
const getSystemStats = async (req, res) => {
  try {
    // Get counts of various entities
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ online: true });
    const totalWorkspaces = await Workspace.countDocuments();
    const totalBoards = await Board.countDocuments();
    const totalCards = await Card.countDocuments();

    // Get recent activity count
    const recentActivityCount = await Activity.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
    });

    // Get new users in the last 30 days
    const newUsers = await User.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get new workspaces in the last 30 days
    const newWorkspaces = await Workspace.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get Monthly Active Users (users who logged in within the last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const monthlyActiveUsers = await User.countDocuments({
      lastLoginAt: { $gte: thirtyDaysAgo }
    });

    // Get subscription statistics
    const totalSubscriptions = await Subscription.countDocuments();
    const activeSubscriptions = await Subscription.countDocuments({ status: 'active' });
    const trialSubscriptions = await Subscription.countDocuments({ status: 'trial' });
    const canceledSubscriptions = await Subscription.countDocuments({ status: 'canceled' });

    // Get subscription by plan
    const subscriptionsByPlan = await Subscription.aggregate([
      {
        $match: { status: 'active' }
      },
      {
        $group: {
          _id: '$planId',
          count: { $sum: 1 }
        }
      }
    ]);

    // Calculate Monthly Recurring Revenue (MRR)
    const subscriptionsWithAmount = await Subscription.find({
      status: 'active',
      amount: { $exists: true, $gt: 0 }
    });

    const monthlyRecurringRevenue = subscriptionsWithAmount.reduce((total, sub) => {
      // Convert to monthly if billing period is different
      const monthlyAmount = sub.billingPeriod === 'year'
        ? sub.amount / 12
        : sub.amount;
      return total + monthlyAmount;
    }, 0);

    // Return statistics
    res.status(200).json({
      users: {
        total: totalUsers,
        active: activeUsers,
        new: newUsers,
        monthlyActive: monthlyActiveUsers
      },
      workspaces: {
        total: totalWorkspaces,
        new: newWorkspaces
      },
      boards: {
        total: totalBoards
      },
      cards: {
        total: totalCards
      },
      activity: {
        recent: recentActivityCount
      },
      subscriptions: {
        total: totalSubscriptions,
        active: activeSubscriptions,
        trial: trialSubscriptions,
        canceled: canceledSubscriptions,
        byPlan: subscriptionsByPlan,
        mrr: monthlyRecurringRevenue
      },
      systemHealth: {
        status: 'healthy',
        lastChecked: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error getting system stats:', error);
    res.status(500).json({ message: 'Error getting system statistics' });
  }
};

// Get user growth statistics (for charts)
const getUserGrowthStats = async (req, res) => {
  try {
    const { period = 'month' } = req.query;
    let dateFormat, groupBy, lookback;

    // Configure date format and grouping based on period
    switch (period) {
      case 'week':
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 7;
        break;
      case 'month':
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 30;
        break;
      case 'year':
        dateFormat = '%Y-%m';
        groupBy = { month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 365;
        break;
      default:
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 30;
    }

    // Get user growth data
    const userGrowth = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - lookback * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: groupBy,
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateToString: {
              format: dateFormat,
              date: {
                $dateFromParts: {
                  year: '$_id.year',
                  month: '$_id.month',
                  day: { $ifNull: ['$_id.day', 1] }
                }
              }
            }
          },
          count: 1
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);

    // Return growth data
    res.status(200).json({
      period,
      data: userGrowth
    });
  } catch (error) {
    console.error('Error getting user growth stats:', error);
    res.status(500).json({ message: 'Error getting user growth statistics' });
  }
};

// Get workspace growth statistics (for charts)
const getWorkspaceGrowthStats = async (req, res) => {
  try {
    const { period = 'month' } = req.query;
    let dateFormat, groupBy, lookback;

    // Configure date format and grouping based on period
    switch (period) {
      case 'week':
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 7;
        break;
      case 'month':
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 30;
        break;
      case 'year':
        dateFormat = '%Y-%m';
        groupBy = { month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 365;
        break;
      default:
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 30;
    }

    // Get workspace growth data
    const workspaceGrowth = await Workspace.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - lookback * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: groupBy,
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateToString: {
              format: dateFormat,
              date: {
                $dateFromParts: {
                  year: '$_id.year',
                  month: '$_id.month',
                  day: { $ifNull: ['$_id.day', 1] }
                }
              }
            }
          },
          count: 1
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);

    // Return growth data
    res.status(200).json({
      period,
      data: workspaceGrowth
    });
  } catch (error) {
    console.error('Error getting workspace growth stats:', error);
    res.status(500).json({ message: 'Error getting workspace growth statistics' });
  }
};

// Get subscription statistics
const getSubscriptionStats = async (req, res) => {
  try {
    // Get subscription counts by status
    const totalSubscriptions = await Subscription.countDocuments();
    const activeSubscriptions = await Subscription.countDocuments({ status: 'active' });
    const trialSubscriptions = await Subscription.countDocuments({ status: 'trial' });
    const canceledSubscriptions = await Subscription.countDocuments({ status: 'canceled' });

    // Get subscription by plan
    const subscriptionsByPlan = await Subscription.aggregate([
      {
        $group: {
          _id: '$planId',
          count: { $sum: 1 },
          active: {
            $sum: {
              $cond: [{ $eq: ['$status', 'active'] }, 1, 0]
            }
          }
        }
      }
    ]);

    // Get subscription growth over time
    const { period = 'month' } = req.query;
    let dateFormat, groupBy, lookback;

    switch (period) {
      case 'week':
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 7;
        break;
      case 'month':
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 30;
        break;
      case 'year':
        dateFormat = '%Y-%m';
        groupBy = { month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 365;
        break;
      default:
        dateFormat = '%Y-%m-%d';
        groupBy = { day: { $dayOfMonth: '$createdAt' }, month: { $month: '$createdAt' }, year: { $year: '$createdAt' } };
        lookback = 30;
    }

    const subscriptionGrowth = await Subscription.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - lookback * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: groupBy,
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateToString: {
              format: dateFormat,
              date: {
                $dateFromParts: {
                  year: '$_id.year',
                  month: '$_id.month',
                  day: { $ifNull: ['$_id.day', 1] }
                }
              }
            }
          },
          count: 1
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);

    // Calculate Monthly Recurring Revenue (MRR)
    const subscriptionsWithAmount = await Subscription.find({
      status: 'active',
      amount: { $exists: true, $gt: 0 }
    });

    const monthlyRecurringRevenue = subscriptionsWithAmount.reduce((total, sub) => {
      // Convert to monthly if billing period is different
      const monthlyAmount = sub.billingPeriod === 'year'
        ? sub.amount / 12
        : sub.amount;
      return total + monthlyAmount;
    }, 0);

    // Get MRR growth over time
    const mrrByMonth = await Subscription.aggregate([
      {
        $match: {
          status: 'active',
          amount: { $exists: true, $gt: 0 }
        }
      },
      {
        $group: {
          _id: {
            month: { $month: '$createdAt' },
            year: { $year: '$createdAt' }
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateToString: {
              format: '%Y-%m',
              date: {
                $dateFromParts: {
                  year: '$_id.year',
                  month: '$_id.month',
                  day: 1
                }
              }
            }
          },
          mrr: {
            $cond: [
              { $eq: ['$billingPeriod', 'year'] },
              { $divide: ['$totalAmount', 12] },
              '$totalAmount'
            ]
          },
          count: 1
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);

    res.status(200).json({
      counts: {
        total: totalSubscriptions,
        active: activeSubscriptions,
        trial: trialSubscriptions,
        canceled: canceledSubscriptions
      },
      byPlan: subscriptionsByPlan,
      growth: subscriptionGrowth,
      revenue: {
        mrr: monthlyRecurringRevenue,
        mrrGrowth: mrrByMonth
      }
    });
  } catch (error) {
    console.error('Error getting subscription stats:', error);
    res.status(500).json({ message: 'Error getting subscription statistics' });
  }
};

// Get activity statistics
const getActivityStats = async (req, res) => {
  try {
    // Get activity counts by type
    const activityByType = await Activity.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get activity over time
    const { period = 'week' } = req.query;
    let dateFormat, groupBy, lookback;

    switch (period) {
      case 'day':
        dateFormat = '%Y-%m-%d %H:00';
        groupBy = {
          hour: { $hour: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' },
          month: { $month: '$createdAt' },
          year: { $year: '$createdAt' }
        };
        lookback = 1;
        break;
      case 'week':
        dateFormat = '%Y-%m-%d';
        groupBy = {
          day: { $dayOfMonth: '$createdAt' },
          month: { $month: '$createdAt' },
          year: { $year: '$createdAt' }
        };
        lookback = 7;
        break;
      case 'month':
        dateFormat = '%Y-%m-%d';
        groupBy = {
          day: { $dayOfMonth: '$createdAt' },
          month: { $month: '$createdAt' },
          year: { $year: '$createdAt' }
        };
        lookback = 30;
        break;
      default:
        dateFormat = '%Y-%m-%d';
        groupBy = {
          day: { $dayOfMonth: '$createdAt' },
          month: { $month: '$createdAt' },
          year: { $year: '$createdAt' }
        };
        lookback = 7;
    }

    const activityOverTime = await Activity.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - lookback * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: groupBy,
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateToString: {
              format: dateFormat,
              date: {
                $dateFromParts: {
                  year: '$_id.year',
                  month: '$_id.month',
                  day: { $ifNull: ['$_id.day', 1] },
                  hour: { $ifNull: ['$_id.hour', 0] }
                }
              }
            }
          },
          count: 1
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);

    // Get most active users
    const mostActiveUsers = await Activity.aggregate([
      {
        $match: {
          userId: { $exists: true },
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: '$userId',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          _id: 0,
          userId: '$_id',
          name: '$user.name',
          email: '$user.email',
          count: 1
        }
      }
    ]);

    res.status(200).json({
      byType: activityByType,
      overTime: activityOverTime,
      mostActiveUsers
    });
  } catch (error) {
    console.error('Error getting activity stats:', error);
    res.status(500).json({ message: 'Error getting activity statistics' });
  }
};

module.exports = {
  getSystemStats,
  getUserGrowthStats,
  getWorkspaceGrowthStats,
  getSubscriptionStats,
  getActivityStats
};

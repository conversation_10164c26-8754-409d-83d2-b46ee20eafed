const jwt = require('jsonwebtoken');
const Admin = require('../models/Admin');

// Authenticate admin using JWT
const authenticateAdmin = async (req, res, next) => {
  try {
    // Get token from cookies or authorization header
    const token = req.cookies.adminToken || 
                 (req.headers.authorization && req.headers.authorization.startsWith('Bearer') 
                  ? req.headers.authorization.split(' ')[1] : null);

    if (!token) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Find admin by ID
    const admin = await Admin.findById(decoded.id);
    if (!admin || !admin.active) {
      return res.status(401).json({ message: 'Invalid or inactive admin account' });
    }

    // Attach admin to request object
    req.admin = admin;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    }
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Check if admin has required permission
const checkPermission = (permission) => {
  return (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.admin.role === 'super-admin') {
      return next(); // Super admins have all permissions
    }

    if (!req.admin.permissions.includes(permission)) {
      return res.status(403).json({ message: 'Permission denied' });
    }

    next();
  };
};

module.exports = { authenticateAdmin, checkPermission };

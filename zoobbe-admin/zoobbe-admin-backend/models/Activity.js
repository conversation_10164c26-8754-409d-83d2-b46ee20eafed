const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define a simplified version of the Activity schema that matches the main application
const activitySchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'User' },
  action: { type: String, required: true },
  targetType: { type: String, required: true },
  target: { type: String },
  metadata: { type: Schema.Types.Mixed },
  createdAt: { type: Date, default: Date.now }
});

// Create the Activity model
const Activity = mongoose.model('Activity', activitySchema);

module.exports = Activity;

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const featureFlagSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    unique: true 
  },
  description: { 
    type: String 
  },
  enabled: { 
    type: Boolean, 
    default: false 
  },
  rolloutPercentage: { 
    type: Number, 
    min: 0, 
    max: 100, 
    default: 0 
  },
  targetUsers: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }],
  targetWorkspaces: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'Workspace' 
  }],
  targetPlans: [{ 
    type: String, 
    enum: ['free', 'standard', 'premium', 'enterprise'] 
  }],
  isExperimental: { 
    type: Boolean, 
    default: true 
  },
  startDate: { 
    type: Date 
  },
  endDate: { 
    type: Date 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'Admin' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'Admin' 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create indexes for better query performance
featureFlagSchema.index({ name: 1 });
featureFlagSchema.index({ enabled: 1 });
featureFlagSchema.index({ isExperimental: 1 });

// Update the updatedAt field on save
featureFlagSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const FeatureFlag = mongoose.model('FeatureFlag', featureFlagSchema);

module.exports = FeatureFlag;

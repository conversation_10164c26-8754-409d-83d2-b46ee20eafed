const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const feedbackSchema = new Schema({
  user: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  type: { 
    type: String, 
    enum: ['bug', 'feature', 'improvement', 'general'], 
    required: true 
  },
  title: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  status: { 
    type: String, 
    enum: ['new', 'in-progress', 'resolved', 'closed', 'duplicate'], 
    default: 'new' 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  assignedTo: { 
    type: Schema.Types.ObjectId, 
    ref: 'Admin' 
  },
  tags: [{ 
    type: String 
  }],
  screenshots: [{ 
    url: { type: String },
    name: { type: String }
  }],
  browser: { 
    type: String 
  },
  os: { 
    type: String 
  },
  device: { 
    type: String 
  },
  url: { 
    type: String 
  },
  responses: [{
    admin: { type: Schema.Types.ObjectId, ref: 'Admin' },
    message: { type: String },
    createdAt: { type: Date, default: Date.now }
  }],
  internalNotes: [{
    admin: { type: Schema.Types.ObjectId, ref: 'Admin' },
    note: { type: String },
    createdAt: { type: Date, default: Date.now }
  }],
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  },
  resolvedAt: { 
    type: Date 
  },
  resolvedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'Admin' 
  }
});

// Create indexes for better query performance
feedbackSchema.index({ user: 1 });
feedbackSchema.index({ type: 1 });
feedbackSchema.index({ status: 1 });
feedbackSchema.index({ priority: 1 });
feedbackSchema.index({ createdAt: -1 });

// Update the updatedAt field on save
feedbackSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const Feedback = mongoose.model('Feedback', feedbackSchema);

module.exports = Feedback;

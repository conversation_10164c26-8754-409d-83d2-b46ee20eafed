const express = require('express');
const router = express.Router();
const { 
  getFeatureFlags, 
  getFeatureFlagById, 
  createFeatureFlag, 
  updateFeatureFlag, 
  deleteFeatureFlag 
} = require('../controllers/featureFlagController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get all feature flags
router.get('/', checkPermission('manage_feature_flags'), getFeatureFlags);

// Get feature flag by ID
router.get('/:flagId', checkPermission('manage_feature_flags'), getFeatureFlagById);

// Create feature flag
router.post('/', checkPermission('manage_feature_flags'), createFeatureFlag);

// Update feature flag
router.put('/:flagId', checkPermission('manage_feature_flags'), updateFeatureFlag);

// Delete feature flag
router.delete('/:flagId', checkPermission('manage_feature_flags'), deleteFeatureFlag);

module.exports = router;

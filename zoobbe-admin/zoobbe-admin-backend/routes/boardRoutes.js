const express = require('express');
const router = express.Router();
const {
  getBoards,
  getBoardById,
  updateBoardStatus,
  updateBoardDetails,
  transferBoard,
  getBoardMembers,
  updateBoardMemberRole,
  removeBoardMember
} = require('../controllers/boardController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get all boards
router.get('/', checkPermission('manage_boards'), getBoards);

// Get board by ID
router.get('/:boardId', checkPermission('manage_boards'), getBoardById);

// Update board status
router.patch('/:boardId/status', checkPermission('manage_boards'), updateBoardStatus);

// Update board details
router.patch('/:boardId', checkPermission('manage_boards'), updateBoardDetails);

// Transfer board to another workspace
router.post('/:boardId/transfer', checkPermission('manage_boards'), transferBoard);

// Get board members
router.get('/:boardId/members', checkPermission('manage_boards'), getBoardMembers);

// Update board member role
router.patch('/:boardId/members/:userId', checkPermission('manage_boards'), updateBoardMemberRole);

// Remove member from board
router.delete('/:boardId/members/:userId', checkPermission('manage_boards'), removeBoardMember);

module.exports = router;

const express = require('express');
const router = express.Router();
const { 
  getCards, 
  getCardById, 
  updateCardStatus 
} = require('../controllers/cardController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get all cards
router.get('/', checkPermission('manage_cards'), getCards);

// Get card by ID
router.get('/:cardId', checkPermission('manage_cards'), getCardById);

// Update card status
router.patch('/:cardId/status', checkPermission('manage_cards'), updateCardStatus);

module.exports = router;

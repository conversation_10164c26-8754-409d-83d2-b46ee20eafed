const express = require('express');
const router = express.Router();
const { 
  registerAdmin, 
  loginAdmin, 
  logoutAdmin, 
  getProfile, 
  refreshToken 
} = require('../controllers/authController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// Public routes
router.post('/login', loginAdmin);
router.post('/refresh-token', refreshToken);

// Protected routes
router.post('/register', authenticateAdmin, checkPermission('manage_admins'), registerAdmin);
router.post('/logout', authenticateAdmin, logoutAdmin);
router.get('/profile', authenticateAdmin, getProfile);

module.exports = router;

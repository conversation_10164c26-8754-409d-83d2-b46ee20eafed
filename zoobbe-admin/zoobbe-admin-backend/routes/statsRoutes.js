const express = require('express');
const router = express.Router();
const {
  getSystemStats,
  getUserGrowthStats,
  getWorkspaceGrowthStats,
  getSubscriptionStats,
  getActivityStats
} = require('../controllers/statsController');
const { authenticateAdmin, checkPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticateAdmin);

// Get system overview statistics
router.get('/', checkPermission('view_stats'), getSystemStats);

// Get user growth statistics
router.get('/users/growth', checkPermission('view_stats'), getUserGrowthStats);

// Get workspace growth statistics
router.get('/workspaces/growth', checkPermission('view_stats'), getWorkspaceGrowthStats);

// Get subscription statistics
router.get('/subscriptions', checkPermission('view_stats'), getSubscriptionStats);

// Get activity statistics
router.get('/activity', checkPermission('view_stats'), getActivityStats);

module.exports = router;

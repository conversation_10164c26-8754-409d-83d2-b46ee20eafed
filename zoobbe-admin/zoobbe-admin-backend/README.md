# Zoobbe Admin Backend

This is the backend API for the Zoobbe Admin Dashboard, providing administrative functionality for managing users, workspaces, and system statistics.

## Features

- Admin authentication and authorization
- User management
- Workspace management
- System statistics and analytics
- Activity logging

## Prerequisites

- Node.js (v14 or higher)
- MongoDB
- npm or yarn

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file in the root directory with the following variables:
   ```
   PORT=5001
   MONGO_URI=mongodb://localhost:27017/zoobbe
   JWT_SECRET=your_jwt_secret
   JWT_REFRESH_SECRET=your_refresh_secret
   ADMIN_FRONTEND_URL=http://localhost:3001
   ```

## Setup

1. Create the initial admin user:
   ```
   npm run create-admin
   ```
   This will create a super-admin user with the following credentials:
   - Email: <EMAIL>
   - Password: adminPassword123

   **Important**: Change this password after the first login.

## Running the Application

### Development Mode
```
npm run dev
```

### Production Mode
```
npm start
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login admin
- `POST /api/auth/register` - Register new admin (requires super-admin privileges)
- `POST /api/auth/logout` - Logout admin
- `GET /api/auth/profile` - Get current admin profile
- `POST /api/auth/refresh-token` - Refresh access token

### Users
- `GET /api/users` - Get all users
- `GET /api/users/:userId` - Get user by ID
- `PATCH /api/users/:userId/status` - Update user status
- `GET /api/users/stats` - Get user statistics

### Workspaces
- `GET /api/workspaces` - Get all workspaces
- `GET /api/workspaces/:workspaceId` - Get workspace by ID
- `PATCH /api/workspaces/:workspaceId/status` - Update workspace status
- `GET /api/workspaces/stats` - Get workspace statistics

### Statistics
- `GET /api/stats` - Get system overview statistics
- `GET /api/stats/users/growth` - Get user growth statistics
- `GET /api/stats/workspaces/growth` - Get workspace growth statistics

### Activity
- `GET /api/activity` - Get activity log
- `GET /api/activity/types` - Get activity types
- `GET /api/activity/stats` - Get activity statistics

## License

ISC

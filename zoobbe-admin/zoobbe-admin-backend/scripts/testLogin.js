require('dotenv').config();
const axios = require('axios');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');

// Function to test login with the API
const testLoginAPI = async (email, password) => {
  try {
    console.log(`Testing login API with email: ${email}`);
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email,
      password
    });
    
    console.log('Login API Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Login API Error:', error.response?.data || error.message);
    return null;
  }
};

// Function to check admin in database
const checkAdminInDB = async (email) => {
  try {
    await connectDB();
    
    console.log(`Checking admin in database with email: ${email}`);
    const admin = await Admin.findOne({ email });
    
    if (!admin) {
      console.log('Admin not found in database');
      return null;
    }
    
    console.log('Admin found in database:', {
      id: admin._id,
      name: admin.name,
      email: admin.email,
      role: admin.role,
      passwordHash: admin.password.substring(0, 20) + '...' // Show only part of the hash for security
    });
    
    return admin;
  } catch (error) {
    console.error('Database Error:', error.message);
    return null;
  }
};

// Function to test password comparison
const testPasswordComparison = async (admin, password) => {
  if (!admin) return;
  
  try {
    console.log(`Testing password comparison for admin: ${admin.email}`);
    console.log(`Input password: ${password}`);
    
    const isMatch = await bcrypt.compare(password, admin.password);
    console.log(`Password match result: ${isMatch}`);
    
    return isMatch;
  } catch (error) {
    console.error('Password Comparison Error:', error.message);
    return false;
  }
};

// Main function
const main = async () => {
  try {
    // Get email and password from command line arguments
    const email = process.argv[2];
    const password = process.argv[3];
    
    if (!email || !password) {
      console.error('Please provide email and password as arguments');
      console.log('Usage: node testLogin.js <email> <password>');
      process.exit(1);
    }
    
    // Check admin in database
    const admin = await checkAdminInDB(email);
    
    // Test password comparison if admin exists
    if (admin) {
      await testPasswordComparison(admin, password);
    }
    
    // Test login API
    await testLoginAPI(email, password);
    
    // Close database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('Error:', error.message);
  }
};

// Run the main function
main();

require('dotenv').config();
const mongoose = require('mongoose');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');
const bcrypt = require('bcryptjs');

// Get admin details from command line arguments or use defaults
const adminName = process.argv[2] || 'New Admin';
const adminEmail = process.argv[3] || '<EMAIL>';
const adminPassword = process.argv[4] || 'adminPassword123';
const adminRole = process.argv[5] || 'super-admin';

// Admin details
const NEW_ADMIN = {
  name: adminName,
  email: adminEmail,
  password: adminPassword,
  role: adminRole
};

// Function to create admin user
const createAdmin = async () => {
  try {
    // Connect to database
    await connectDB();

    // Check if admin with this email already exists
    const existingAdmin = await Admin.findOne({ email: NEW_ADMIN.email });

    if (existingAdmin) {
      console.log(`Admin with email ${NEW_ADMIN.email} already exists.`);
      console.log('You can log in with:');
      console.log(`Email: ${NEW_ADMIN.email}`);
      console.log(`Password: [your previously set password]`);
      process.exit(0);
    }

    // Create admin user - don't hash the password here, let the model's pre-save hook handle it
    const admin = await Admin.create({
      name: NEW_ADMIN.name,
      email: NEW_ADMIN.email,
      password: NEW_ADMIN.password, // The model will hash this automatically
      role: NEW_ADMIN.role,
      permissions: [
        'manage_users',
        'manage_workspaces',
        'manage_boards',
        'manage_cards',
        'manage_subscriptions',
        'manage_feature_flags',
        'manage_feedback',
        'manage_system_settings',
        'view_stats',
        'view_activity',
        'view_logs',
        'manage_admins'
      ]
    });

    console.log(`Admin user created successfully!`);
    console.log('You can now log in to the admin panel with these credentials:');
    console.log(`Email: ${NEW_ADMIN.email}`);
    console.log(`Password: ${NEW_ADMIN.password}`);

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
};

// Run the function
createAdmin();

require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const connectDB = require('../config/db');

// Function to fix admin password
const fixAdminPassword = async () => {
  try {
    // Connect to database
    await connectDB();
    
    // Get email and new password from command line arguments
    const email = process.argv[2];
    const newPassword = process.argv[3];
    
    if (!email || !newPassword) {
      console.error('Please provide email and new password as arguments');
      console.log('Usage: node fixAdmin.js <email> <newPassword>');
      process.exit(1);
    }
    
    console.log(`Fixing password for admin with email: ${email}`);
    
    // Find admin by email
    const admin = await mongoose.connection.collection('admins').findOne({ email });
    
    if (!admin) {
      console.error(`Admin with email ${email} not found`);
      process.exit(1);
    }
    
    console.log('Admin found:', {
      id: admin._id,
      name: admin.name,
      email: admin.email,
      role: admin.role
    });
    
    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);
    
    // Update admin with new password directly in the database
    // This bypasses the pre-save hook that would hash it again
    const result = await mongoose.connection.collection('admins').updateOne(
      { email },
      { $set: { password: hashedPassword } }
    );
    
    if (result.modifiedCount === 1) {
      console.log('Password updated successfully!');
      console.log(`Email: ${email}`);
      console.log(`Password: ${newPassword}`);
    } else {
      console.error('Failed to update password');
    }
    
    // Close database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('Error fixing admin password:', error);
    process.exit(1);
  }
};

// Run the function
fixAdminPassword();

/**
 * <PERSON><PERSON>t to create a simple admin user without password hashing
 * This is for testing purposes only
 */

require('dotenv').config();
const mongoose = require('mongoose');
const connectDB = require('../config/db');
const Admin = require('../models/Admin');

// Connect to MongoDB
const connectToMongo = async () => {
  try {
    await connectDB();
    console.log('MongoDB connected...');
  } catch (err) {
    console.error('MongoDB connection error:', err.message);
    process.exit(1);
  }
};

// Create admin user directly in the database
const createSimpleAdmin = async () => {
  try {
    await connectToMongo();

    // Admin model is already imported

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });

    if (existingAdmin) {
      console.log('Admin user already exists. Deleting it...');
      await Admin.deleteOne({ email: '<EMAIL>' });
    }

    // Create a new admin directly in the database
    await mongoose.connection.collection('admins').insertOne({
      name: 'Test Admin',
      email: '<EMAIL>',
      password: 'admin', // plaintext: password123
      role: 'super-admin',
      permissions: [
        'manage_users',
        'manage_workspaces',
        'manage_boards',
        'manage_cards',
        'manage_subscriptions',
        'manage_feature_flags',
        'manage_feedback',
        'manage_system_settings',
        'view_stats',
        'view_activity',
        'view_logs',
        'manage_admins'
      ],
      active: true,
      createdAt: new Date()
    });

    console.log('Simple admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');

    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
};

// Run the function
createSimpleAdmin();

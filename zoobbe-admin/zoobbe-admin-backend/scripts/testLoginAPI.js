require('dotenv').config();
const axios = require('axios');

// Function to test login API
const testLoginAPI = async () => {
  try {
    // Get email and password from command line arguments
    const email = process.argv[2];
    const password = process.argv[3];
    
    if (!email || !password) {
      console.error('Please provide email and password as arguments');
      console.log('Usage: node testLoginAPI.js <email> <password>');
      process.exit(1);
    }
    
    console.log(`Testing login API with email: ${email}`);
    
    // Make API request
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email,
      password
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Login API Response:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Check if token is present in response
    if (response.data.token) {
      console.log('\nToken is present in response ✅');
    } else {
      console.log('\nToken is missing from response ❌');
    }
    
    // Check if admin data is present in response
    if (response.data.admin) {
      console.log('Admin data is present in response ✅');
    } else {
      console.log('Admin data is missing from response ❌');
    }
    
    // Check cookies in response headers
    const cookies = response.headers['set-cookie'];
    if (cookies && cookies.length > 0) {
      console.log('\nCookies set in response:');
      cookies.forEach(cookie => console.log(`- ${cookie}`));
    } else {
      console.log('\nNo cookies set in response');
    }
    
  } catch (error) {
    console.error('Login API Error:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response data:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
};

// Run the function
testLoginAPI();

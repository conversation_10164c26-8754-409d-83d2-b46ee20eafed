🧠 Zoobbe Pro Workspace Billing Algorithm

if subscription is active and planType standard/premium this is called pro workspace

1. Seat Management
When a new member is added to a Pro workspace:
A seat is automatically added.
The workspace admin is required to pay for the extra seat.
If payment is not completed:
The admin will see a payment notice.

in that notice every information will be shown  / make a way to show the details

The new member gets view-only access and will see a notice to inform the admin.

the notice will show in all pages

1. when admin will pay for the seat,  the seat will be reserved and the member will get full access

2. in the billing page admin can pay the pending seats also make a way to increase more reseve seats but not paid seat but not less the pending seats bill. admin must pay the pending seats first and then can increase the reserved seats


2. Billing Cycle
Billing is workspace-based, and follows the chosen cycle:
Either monthly or annually, based on the initial subscription.
The next billing cycle starts on the anniversary of the workspace subscription start date (month or year).

1. Seat Reuse Within the Same Cycle
If a member is removed during the current billing cycle, the seat remains reserved.
Admins can add a new member using the reserved seat without additional billing.
1. Prorated Charges
If a new seat is added in the middle of a billing cycle, <PERSON>bbe will:
Prorate the charge based on the remaining time in the current cycle.
Apply this logic for both monthly and annual billing.



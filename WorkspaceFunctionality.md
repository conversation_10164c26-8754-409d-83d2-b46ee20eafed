

when owner create workspace, he will be admin and board member by default
Workspace owner can invite in the workspace as an admin, member or others capabilities
An invitee workspace member can join/leave the board by themselves
An invitee Workspace member can create board by itself

When owner or inviter invite workspace with others it  will be ther workspaces too,
    admin can do anything
    member can't change the  settings

A board member can share board with others
When a user invitee in a board, that board workspace will be his geust workspaces 




When the workspace owner creates a workspace, they become the admin and a board member by default.
The workspace owner can invite others to join the workspace as an admin, member, or with other specific roles.

A workspace member who is invited can join or leave boards on their own.
They can also create boards within the workspace.

When the owner or an inviter shares a workspace with others, it becomes part of those users' workspaces too.
    Admins have full control over the workspace.
    Members can access and use the workspace but cannot change the settings.
Board members can share boards with others.
When a user is invited to a board, the workspace of that board will be added as a guest workspace for them.

only board members can join/asign the card
card member can add new members in the card

only watchers will get notifications for any activity in the card
When mentioned the members, they will get notifications also
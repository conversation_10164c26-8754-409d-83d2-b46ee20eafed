on:
  push:
    branches:
      - deploy 
name: 🚀 Deploy website on push
jobs:
  web-deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v4
    
    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.5
      with:
        server: ftp.zoobbee.com
        username: zoobbe<PERSON>@beta.zoobbe.com
        password: ${{ secrets.DEPLOY_PASSWORD }}
        local-dir: ./zoobbe-backend/  
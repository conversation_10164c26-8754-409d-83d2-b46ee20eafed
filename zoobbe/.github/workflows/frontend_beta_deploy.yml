on:
  push:
    branches:
      - beta-deploy 
name: 🚀 Deploy website on push
jobs:
  web-deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v4
    
    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.5
      with:
        server: 38.58.176.211
        username: zoo<PERSON><PERSON>@beta.zoobbe.com
        password: ${{ secrets.FRONT_DEPLOY_PASSWORD }}
        local-dir: ./zoobbe-frontend/build/
        server-dir: /beta.zoobbe.com/ 
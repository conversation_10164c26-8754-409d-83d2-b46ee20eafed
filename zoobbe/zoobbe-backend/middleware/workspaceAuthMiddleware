const Workspace = require("../models/Workspace");

const allowPaymentAction = async (req, res, next) => {
    const { workspaceId } = req.body;
    const userId = req.user.id;

    try {
        const workspace = await Workspace.findOne({
            _id: workspaceId,
            $or: [
                { ownerId: userId },
                { 'members.user': userId, 'members.role': 'admin' }
            ]
        });

        if (!workspace) {
            return res.status(403).json({
                error: 'Only workspace owners and admins can perform this action'
            });
        }

        req.workspace = workspace;
        next();
    } catch (err) {
        console.error('Workspace authorization error:', err);
        return res.status(500).json({
            error: 'Internal server error'
        });
    }
};

module.exports = allowPaymentAction;
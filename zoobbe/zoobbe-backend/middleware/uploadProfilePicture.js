const multer = require('multer');
const path = require('path');

// Set storage engine to memory storage
const storage = multer.memoryStorage();  // Change from diskStorage to memoryStorage

// Init upload
const uploadProfilePicture = multer({
    storage: storage,  // Use memory storage
    limits: { fileSize: 50000000 }, // Limit file size to 50MB
    fileFilter: function (req, file, cb) {
        checkFileType(file, cb);
    }
});

// Check file type
function checkFileType(file, cb) {
    // Allowed ext
    const filetypes = /jpeg|jpg|png|webp/;
    
    // Check ext
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    // Check mime
    const mimetype = filetypes.test(file.mimetype);

    if (mimetype && extname) {
        return cb(null, true);
    } else {
        cb('Error: Images Only!');
    }
}

module.exports = uploadProfilePicture;

const jwt = require('jsonwebtoken');
const Board = require('../models/Board');
const Card = require('../models/Card');
const User = require('../models/User');
const Workspace = require('../models/Workspace');

const authenticateToken = async (req, res, next) => {

    const accessToken = req.cookies.accessToken;
    const refreshToken = req.cookies.refreshToken;

    // If access token is missing, try refreshing it
    if (!accessToken) {
        if (!refreshToken) {
            return res.status(401).json({ message: 'Access denied, both tokens missing!' });
        }


        // Try to refresh the access token using the refresh token
        try {
            // Verify the refresh token
            const decodedRefresh = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
            const user = await User.findById(decodedRefresh.id);

            if (!user) {
                return res.status(403).json({ message: 'Invalid refresh token or user does not exist!' });
            }

            // Generate a new access token and refresh token
            const newAccessToken = generateAccessToken(user);
            const newRefreshToken = generateRefreshToken(user);

            // Set the new tokens in cookies
            res.cookie('accessToken', newAccessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'Strict',
                maxAge: 1 * 24 * 60 * 1000, // 1 day
            });
            res.cookie('refreshToken', newRefreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'Strict',
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
            });

            // Attach the user object to the request
            req.user = user;

            return next(); // Continue to the next middleware or route handler

        } catch (err) {
            return res.status(403).json({ message: 'Invalid or expired refresh token' });
        }
    }

    // If access token exists, verify it
    try {
        const decoded = jwt.verify(accessToken, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id);

        if (!user) {
            return res.status(401).json({ message: 'User not found, access denied!' });
        }

        // Attach the user object to the request
        req.user = user;
        return next(); // Proceed to the next middleware or route handler
    } catch (err) {
        return res.status(401).json({ message: 'Invalid access token or user does not exist!' });
    }
};

// Helper functions to generate tokens
const generateAccessToken = (user) => {
    return jwt.sign({ id: user.id }, process.env.JWT_SECRET, { expiresIn: '15m' });
};

const generateRefreshToken = (user) => {
    return jwt.sign({ id: user.id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '7d' });
};


const authenticateAndAuthorize = (type) => {
    return async (req, res, next) => {

        try {
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ message: 'User not authenticated!' });
            }

            let resource;

            if (type === 'board') {
                const boardId = req.params.boardId || req.body.boardId;

                resource = await Board.findOne({ shortId: boardId }).populate('workspace');
                if (!resource) return res.status(404).json({ message: 'Board not found!' });

                // Check if user is a workspace member (has access to all boards in workspace)
                const isWorkspaceMember = resource?.workspace.members?.some(member => member.user?.equals(userId));

                // Check if user is a board member
                const isBoardMember = resource?.members?.some(member => member.user?.equals(userId));

                // Allow access if user is either a workspace member or a board member
                if (!isWorkspaceMember && !isBoardMember) {
                    return res.status(403).json({ message: 'Access denied: You do not have access to this board' });
                }

            } else if (type === 'card') {
                const cardId = req.params.cardId || req.body.cardId;

                resource = await Card.findOne({ shortId: cardId }).populate({
                    path: 'actionList',
                    populate: {
                        path: 'board',
                        populate: { path: 'workspace' }
                    }
                });

                if (!resource) return res.status(404).json({ message: 'Card not found!' });

                const workspace = resource?.actionList.board.workspace;

                // Check if user is a workspace member (has access to all boards and cards in workspace)
                const isWorkspaceMember = workspace.members?.some(member => member.user?.equals(userId));

                // Check if user is a board member
                const isBoardMember = resource?.actionList.board.members?.some(member => member.user?.equals(userId));

                // Check if user is directly assigned to the card
                const isCardUser = resource?.users?.some(user => user?.equals(userId));

                // Allow access if user is a workspace member, a board member, or directly assigned to the card
                if (!isWorkspaceMember && !isBoardMember && !isCardUser) {
                    return res.status(403).json({ message: 'Access denied: You do not have access to this card' });
                }
            } else if (type === 'members') {
                const resourceType = req.params.type;
                const resourceId = req.params.id;

                if (!resourceType || !resourceId) {
                    return res.status(400).json({ message: 'Resource type or ID is missing' });
                }

                if (resourceType === 'board') {
                    // For board resources
                    resource = await Board.findOne({ shortId: resourceId }).populate('workspace');

                    if (!resource) {
                        return res.status(404).json({ message: 'Board not found' });
                    }

                    // Check if user is a workspace member (has access to all boards in workspace)
                    const isWorkspaceMember = resource.workspace?.members?.some(
                        member => member?.user?.toString() === userId
                    );

                    // Check if user is a board member
                    const isBoardMember = resource.members?.some(
                        member => member?.user?.toString() === userId
                    );

                    // Allow access if user is either a workspace member or a board member
                    if (!isWorkspaceMember && !isBoardMember) {
                        return res.status(403).json({ message: 'Access denied: You do not have access to this board' });
                    }
                }
                else if (resourceType === 'workspace') {
                    // For workspace resources
                    resource = await Workspace.findOne({ shortId: resourceId });

                    if (!resource) {
                        return res.status(404).json({ message: 'Workspace not found' });
                    }

                    // Only workspace members have access to workspace resources
                    const isWorkspaceMember = resource.members?.some(
                        member => member?.user?.toString() === userId
                    );

                    if (!isWorkspaceMember) {
                        return res.status(403).json({ message: 'Access denied: Only workspace members can access this resource' });
                    }
                }
                else if (resourceType === 'guests') {
                    // For guest-related resources
                    resource = await Workspace.findOne({ shortId: resourceId });

                    if (!resource) {
                        return res.status(404).json({ message: 'Workspace not found' });
                    }

                    // Check if user is a workspace member or a guest
                    const isWorkspaceMember = resource.members?.some(
                        member => member?.user?.toString() === userId
                    );
                    const isGuest = resource.guests?.some(
                        guest => guest?.toString() === userId
                    );

                    if (!isWorkspaceMember && !isGuest) {
                        return res.status(403).json({ message: 'Access denied' });
                    }
                }

            } else if (type === 'workspace' && !resource) {
                // This handles direct workspace access (not through members route)
                const workspaceId = req.params.workspaceId || req.body.workspaceId;

                if (!workspaceId) {
                    return res.status(400).json({ message: 'Workspace ID is missing' });
                }

                resource = await Workspace.findOne({ shortId: workspaceId });

                if (!resource) {
                    return res.status(404).json({ message: 'Workspace not found' });
                }

                // Only workspace members have access to workspace resources
                const isWorkspaceMember = resource.members?.some(
                    member => member?.user?.toString() === userId
                );

                if (!isWorkspaceMember) {
                    return res.status(403).json({ message: 'Access denied: Only workspace members can access this resource' });
                }
            }

            req.resource = resource;
            next();
        } catch (error) {
            console.error('Error in authentication middleware:', error);
            res.status(500).json({ message: 'Server error' });
        }
    };
};





module.exports = {
    authenticateToken,
    authenticateAndAuthorize
};

const multer = require('multer');
const path = require('path');

// Set storage engine to memory
const storage = multer.memoryStorage();

// Init upload with file size limit
const upload = multer({
    storage: storage,
    limits: { fileSize: 20 * 1024 * 1024 }, // Limit file size to 50MB
    fileFilter: function (req, file, cb) {
        checkFileType(file, cb);
    }
}).single('file'); // Change 'file' to match your form field name

// Check file type
function checkFileType(file, cb) {
    // Allowed extensions
    const filetypes = /jpeg|jpg|png|gif|bmp|tiff|webp|svg|zip|tar|gz|rar|7z|pdf|doc|docx|ppt|pptx|xls|xlsx|xml|json|csv|txt|rtf|md|html|css|js|ts|jsx|tsx|c|cpp|h|hpp|java|py|rb|php|go|sh|bat|ps1|sql|mp4|mp3|wav|ogg|avi|mov|webm|flac|aac/;

    // Check extension and MIME type
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (mimetype && extname) {
        cb(null, true);
    } else {
        cb(new multer.MulterError('LIMIT_UNEXPECTED_FILE', 'Invalid file type. Allowed formats: images, documents, audio, video, and code files.'));
    }
}

// Middleware to handle errors
const uploadMiddleware = (req, res, next) => {
    upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            if (err.code === 'LIMIT_FILE_SIZE') {
                return res.status(400).json({ error: 'File size exceeds the 20MB limit.' });
            } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
                return res.status(400).json({ error: 'Invalid file type. Allowed formats: images, documents, audio, video, and code files.' });
            }
        } else if (err) {
            return res.status(400).json({ error: err.message });
        }
        next();
    });
};

module.exports = uploadMiddleware;

const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Set storage engine
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const userId = req.user.id; // Assuming you have the user ID from authentication middleware
        const uploadPath = `uploads/importfile/`;

        // Create directory if it doesn't exist
        fs.mkdir(uploadPath, { recursive: true }, function (err) {
            if (err) {
                return cb(err);
            }
            cb(null, uploadPath);
        });
    },
    filename: function (req, file, cb) {
        cb(null, file.originalname);
    }
});

// Init upload
const UploadImport = multer({
    storage: storage,
    limits: { fileSize: 50000000 }, // Limit file size to 50MB
    fileFilter: function (req, file, cb) {
        checkFileType(file, cb);
    }
});

// Check file type
function checkFileType(file, cb) {
    // Allowed ext
    const filetypes = /json/;
    // Check ext
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    // Check mime
    const mimetype = filetypes.test(file.mimetype);

    if (mimetype && extname) {
        return cb(null, true);
    } else {
        cb('Error: Json Only!');
    }
}

module.exports = UploadImport;

const checkPermission = (entity, action) => {
    return (req, res, next) => {
        const userRole = req.user.role; // Assume user role is set after authentication
        // Fetch permissions for the user’s role and entity
        const permissions = getPermissionsForRole(userRole, entity);
        if (permissions.includes(action)) {
            return next(); // User is authorized
        }
        return res.status(403).json({ message: 'Forbidden' }); // User is not authorized
    };
};

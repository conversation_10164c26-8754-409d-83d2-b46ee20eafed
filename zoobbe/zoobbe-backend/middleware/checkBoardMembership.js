const { BoardMember, Card } = require('../models'); // Import your models

const checkBoardMembership = async (req, res, next) => {
    const userId = req.user.id; // Assume user ID is set after authentication
    const cardId = req.params.cardId; // Get the card ID from request params

    try {
        // Find the card and populate its board
        const card = await Card.findById(cardId).populate('board');
        if (!card) {
            return res.status(404).json({ message: 'Card not found' });
        }

        // Check if the user is a member of the board
        const isMember = await BoardMember.findOne({
            board: card.board._id,
            user: userId,
        });

        if (isMember) {
            return next(); // User is authorized
        }
        return res.status(403).json({ message: 'Forbidden' }); // User is not authorized
    } catch (error) {
        return res.status(500).json({ message: 'Server error', error });
    }
};

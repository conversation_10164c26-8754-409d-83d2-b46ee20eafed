const redis = require('redis');
const client = redis.createClient({
    url: 'redis://localhost:6379'
});

// Event listener for successful connection
client.on('connect', () => {
    console.log('Redis client connected');
});

// Event listener for errors
client.on('error', (err) => {
    console.error('Redis error:', err);
});

// Event listener for client disconnection
client.on('end', () => {
    console.log('Redis client disconnected');
});

module.exports = client;

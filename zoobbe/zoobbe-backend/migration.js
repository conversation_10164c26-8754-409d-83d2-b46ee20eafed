const mongoose = require('mongoose');
const Card = require('./models/Card');  // Adjust the path to your Card model
const Board = require('./models/Board');
const ActionList = require('./models/ActionList');
const Workspace = require('./models/Workspace');

// Your backfill function
const backfillCardTitles = async () => {
    try {
        const cards = await Card.find({ $or: [{ boardTitle: { $exists: false } }, { actionListTitle: { $exists: false } }] });

        for (const card of cards) {
            // Fetch the associated Board and ActionList documents
            const board = await Board.findOne({ shortId: card.board });
            const actionList = await ActionList.findById(card.actionList);

            // Update the card with the title of the Board and ActionList
            card.boardTitle = board ? board.title : null;
            card.actionListTitle = actionList ? actionList.title : null;

            // Save the updated card
            await card.save();
        }

        console.log('Backfill complete.');
    } catch (error) {
        console.error('Error backfilling cards:', error);
    }
};

const backfillCardBoardShortId = async () => {
    try {
        const cards = await Card.find({ board: 'RTdXjRUI' }).select('title boardLink board');


        for (const card of cards) {
            // Update the card with the title of the Board and ActionList
            card.board = 'Rjfp0TvL';
            card.boardLink = '/b/Rjfp0TvL/zoobbe-beta-1';

            // Save the updated card
            await card.save();
        }

        console.log('Backfill complete.');
    } catch (error) {
        console.error('Error backfilling cards:', error);
    }
};

const backfillBoardWShortId = async () => {
    try {
        const boards = await Board.find({ wShortId: { $exists: false } });

        // Iterate through each board and update the wShortId from the associated Workspace
        for (const board of boards) {
            // Fetch the associated Workspace based on the workspace reference
            const workspace = await Workspace.findById(board.workspace);

            if (workspace && workspace.shortId) {
                // Set the wShortId to the Workspace's shortId
                board.wShortId = workspace.shortId;

                // Save the updated board document
                await board.save();
                console.log(`Updated board with id: ${board._id}`);
            } else {
                console.log(`No workspace found for board with id: ${board._id}`);
            }
        }

        console.log('Backfill complete for wShortId.');
    } catch (error) {
        console.error('Error backfilling boards:', error);
    }
};

// Connect to the database and run the backfill
// mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
//     .then(() => {
//         console.log('Database connected');
//         return backfillCardTitles();
//     })
//     .then(() => {
//         mongoose.disconnect();
//         console.log('Database disconnected');
//     })
//     .catch((error) => {
//         console.error('Database connection error:', error);
//     });


const backfill = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Backfill connected');
        // backfillCardTitles()
        // backfillBoardWShortId();
        backfillCardBoardShortId()
    } catch (err) {
        console.error('MongoDB connection error:', err);
        process.exit(1);
    }
};

// const Background = require('./models/Background'); // Adjust to your actual model path

// const CLOUDFRONT_DOMAIN = 'https://cloud.zoobbe.com';
// const S3_DOMAIN = 'https://d1jfnbotqcxbko.cloudfront.net';

// const migrateToCloudFront = async () => {
//     try {
//         await mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
//         console.log('✅ Connected to MongoDB. Starting migration...');

//         const backgrounds = await Background.find();
//         console.log(`Found ${backgrounds.length} backgrounds to update.`);

//         for (let bg of backgrounds) {
//             if (bg.sizes?.original.includes(S3_DOMAIN)) {
//                 bg.sizes.original = bg.sizes.original.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);
//                 bg.sizes.thumbnail = bg.sizes.thumbnail.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);
//                 bg.sizes.medium = bg.sizes.medium.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);
//                 bg.sizes.large = bg.sizes.large.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);

//                 await bg.save();
//                 console.log(`✅ Updated: ${bg.fileName}`);
//             }
//         }

//         console.log('🎉 Migration completed successfully.');
//         mongoose.disconnect();
//     } catch (err) {
//         console.error('❌ Migration error:', err);
//         process.exit(1);
//     }
// };



const migrateToCloudFront = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });
        console.log('✅ Connected to MongoDB. Starting migration...');


        /** 🔹 Migrate Board Covers */
        const boards = await Board.find({ 'cover.url': { $regex: S3_DOMAIN } });
        console.log(`Found ${boards.length} boards to update.`);

        const boardUpdates = boards.map(board => {
            board.cover.url = board.cover.url.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);

            if (board.cover.sizes?.original) {
                board.cover.sizes.original = board.cover.sizes.original.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);
            }
            if (board.cover.sizes?.thumbnail) {
                board.cover.sizes.thumbnail = board.cover.sizes.thumbnail.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);
            }
            if (board.cover.sizes?.medium) {
                board.cover.sizes.medium = board.cover.sizes.medium.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);
            }
            if (board.cover.sizes?.large) {
                board.cover.sizes.large = board.cover.sizes.large.replace(S3_DOMAIN, CLOUDFRONT_DOMAIN);
            }

            return board.save();
        });

        await Promise.all(boardUpdates);
        console.log('✅ Board covers migration completed.');

        console.log('🎉 Migration completed successfully.');
    } catch (err) {
        console.error('❌ Migration error:', err);
    } finally {
        mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB.');
    }
};

let OLD_S3_URL = 'https://zoobbe-members.s3.ap-southeast-1.amazonaws.com/';
let NEW_CDN_URL = 'https://cloud.zoobbe.com/';
const User = require('./models/User');

const migrateProfilePictures = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('Connected to MongoDB');

        // Find users with profile pictures from the old S3 bucket
        const usersToUpdate = await User.find({
            profilePicture: { $regex: `^${OLD_S3_URL}` }
        });

        console.log(`Found ${usersToUpdate.length} users to update`);

        for (const user of usersToUpdate) {
            const newProfilePicture = user.profilePicture.replace(OLD_S3_URL, NEW_CDN_URL);

            await User.updateOne({ _id: user._id }, { $set: { profilePicture: newProfilePicture } });

            console.log(`Updated user ${user._id}`);
        }

        console.log('Migration complete');
        mongoose.disconnect();
    } catch (error) {
        console.error('Migration error:', error);
        mongoose.disconnect();
    }
};



OLD_S3_URL = 'https://zoobbe-card-attachments.s3.ap-southeast-1.amazonaws.com/';
NEW_CDN_URL = 'https://cloud.zoobbe.com/';

const migrateCardAttachments = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('Connected to MongoDB');

        // Find cards with attachments that contain old S3 URLs
        const cardsToUpdate = await Card.find({
            'attachments.url': { $regex: `^${OLD_S3_URL}` }
        });

        console.log(`Found ${cardsToUpdate.length} cards to update`);

        for (const card of cardsToUpdate) {
            let isUpdated = false;

            // Update attachment URLs
            card.attachments = card.attachments.map(attachment => {
                if (attachment.url.startsWith(OLD_S3_URL)) {
                    attachment.url = attachment.url.replace(OLD_S3_URL, NEW_CDN_URL);
                    isUpdated = true;
                }
                return attachment;
            });

            // Update cover URL if it matches an old attachment URL
            if (card.cover && card.cover.url && card.cover.url.startsWith(OLD_S3_URL)) {
                card.cover.url = card.cover.url.replace(OLD_S3_URL, NEW_CDN_URL);
                isUpdated = true;
            }

            if (isUpdated) {
                await card.save();
                console.log(`Updated card ${card._id}`);
            }
        }

        console.log('Migration complete');
        mongoose.disconnect();
    } catch (error) {
        console.error('Migration error:', error);
        mongoose.disconnect();
    }
};

const setAllUsersOffline = async () => {
    try {
        const result = await User.updateMany({}, { $set: { online: false } });
        console.log(`Updated ${result.modifiedCount} users to offline.`);
    } catch (error) {
        console.error('Error updating users:', error);
    } finally {
        mongoose.disconnect();
    }
};





module.exports = { backfill, migrateToCloudFront, migrateProfilePictures, migrateCardAttachments };

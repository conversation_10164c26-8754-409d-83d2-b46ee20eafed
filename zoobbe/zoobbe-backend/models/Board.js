const mongoose = require('mongoose');
const Counter = require('./counter'); // Import the counter schema if needed
const Schema = mongoose.Schema;

// Import nanoid asynchronously using dynamic import in CommonJS format
let nanoid;
(async () => {
  const nanoidModule = await import('nanoid');
  nanoid = nanoidModule.customAlphabet('1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 6);
})();

const defaultLabels = [
  {
    text: "",
    color: "#7f5f01",
  },
  {
    text: "",
    color: "#a54800",
  },
  {
    text: "",
    color: "#ae2e24",
  },
  {
    text: "",
    color: "Green",
  },
  {
    text: "",
    color: "#5e4db2",
  },
  {
    text: "",
    color: "#0054cc",
  }
];

const labelSchema = new Schema({
  text: { type: String, required: false },
  color: { type: String, required: false },
  createdAt: { type: Date, default: Date.now }
});

const attachmentSchema = new Schema({
  name: { type: String, required: false },
  url: { type: String, required: false },
  signedUrl: { type: String, required: false },
  uploadedAt: { type: Date, default: Date.now },
  boardId: { type: Schema.Types.Mixed, ref: 'Board', required: false },
  fileHash: { type: Schema.Types.Mixed, ref: 'Background', required: false },
  expiresAt: { type: Date, required: false }
});

// Define a schema for board members with a role
const boardMemberSchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  role: { type: String, default: 'member' },
  workspaceRole: { type: String, default: 'member' },
  status: { type: String, enum: ['pending', 'accepted'], default: 'pending' }
});

const boardSchema = new Schema({
  title: { type: String, required: true },
  actionLists: [{ type: Schema.Types.ObjectId, ref: 'ActionList' }],
  workspace: { type: Schema.Types.ObjectId, ref: 'Workspace' },
  wShortId: { type: String, ref: 'Workspace' },
  importId: { type: String, required: false },
  archived: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  lastViewed: { type: Date, default: Date.now },
  members: [boardMemberSchema],
  labels: { type: [labelSchema], default: defaultLabels },
  attachments: [attachmentSchema],
  cardsCount: { type: Number, default: null },
  cardNumberCounter: { type: Number, default: 0 },

  shortId: { type: String, unique: true, index: true },
  shortLink: { type: String },
  permalink: { type: String },
  type: { type: String, default: 'BOARD' },
  joinLink: { type: String, default: null },
  visibility: {
    type: String,
    enum: ['Private', 'Workspace', 'Public'],
    default: 'Private',
    required: true
  },
  cover: {
    name: { type: String, required: false },
    url: { type: String, required: false },
    sizes: {
      original: { type: String, required: false },
      thumbnail: { type: String, required: false },
      medium: { type: String, required: false },
      large: { type: String, required: false },
    },
    boardId: { type: Schema.Types.Mixed, ref: 'Board', required: false },
    coverColor: { type: [String], required: false },
    attachmentId: { type: String, required: false },
  },
});

boardSchema.pre('save', async function (next) {
  try {
    if (!this.shortId) {
      this.shortId = nanoid(8);
    }

    this.shortLink = `/b/${this.shortId}`;
    this.permalink = `/b/${this.shortId}/${this.title?.replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase()
      }`;

    next();
  } catch (error) {
    return next(error);
  }
});


// Method to get the next card number and increment cardNumberCounter
boardSchema.methods.getNextCardNumber = async function () {
  const cardCount = await mongoose.model('Card').countDocuments({ board: this.shortId });

  // If cardNumberCounter is smaller than cardCount, start from cardCount + 1
  if (this.cardNumberCounter < cardCount) {
    this.cardNumberCounter = cardCount + 1;
  } else {
    this.cardNumberCounter += 1;  // Increment for the next card
  }

  await this.save();  // Save the updated board document
  return this.cardNumberCounter;
};

module.exports = mongoose.model('Board', boardSchema);

const mongoose = require('mongoose');
const Workspace = require('./Workspace');
const Schema = mongoose.Schema;

// Define a schema for filters
const filterSchema = new mongoose.Schema({
  keyword: { type: String, default: '' },
  noMembers: { type: Boolean, default: false },
  selectMembers: { type: Boolean, default: false },
  assignedToMe: { type: Boolean, default: false },
  noDates: { type: Boolean, default: false },
  overdue: { type: Boolean, default: false },
  dueNextDay: { type: Boolean, default: false },
  noLabels: { type: Boolean, default: false },
  selectedMembers: [{ type: String }],  // Assuming selectedMembers are strings like usernames
  selectedLabels: [{ type: String }],  // Assuming selectedLabels are strings like label names
  filteredStatus: { type: Boolean, default: false },
});

// Define a schema for user settings
const settingsSchema = new mongoose.Schema({
  language: { type: String, default: 'en' }, // Default to English
  timezone: { type: String, default: 'UTC' }, // Default to UTC
  highContrast: { type: Boolean, default: false }, // Accessibility option
  themeColor: { type: String, default: 'blue' }, // User's selected theme color
  preferredTheme: { type: String, enum: ['light', 'dark', 'system'], default: 'system' }, // Light/Dark/System preference
  pushNotifications: { type: Boolean, default: false }, // Push notifications
  slackNotifications: { type: Boolean, default: false }, // Slack notifications
  emailNotifications: { type: Boolean, default: true }, // Email notifications
});

const slackSchema = new mongoose.Schema({
  connected: { type: Boolean, default: false },
  userId: { type: String, default: null },
  teamId: { type: String, default: null }, // Added team ID for better tracking
  channelId: { type: String, default: null },
  botToken: { type: String, default: null }, // Consider encrypting this in production
  refreshToken: { type: String, default: null }, // Required for token refresh
  expiresAt: { type: Number, default: null }, // Store token expiration timestamp
});


const userSchema = new mongoose.Schema({
  name: { type: String, required: false },
  email: { type: String, required: true, unique: true },
  passwordHash: { type: String, required: false },
  username: { type: String, required: true, unique: true },
  profilePicture: { type: String, required: false },
  bio: { type: String },
  isDefaultPasswordSet: { type: Boolean, default: true },
  isPremiumMember: { type: Boolean, default: false },
  canSeeOnlineStatus: { type: Boolean, default: false },
  newNotificationsCount: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now },
  workspaces: [{ type: Schema.Types.Mixed, ref: 'Workspace' }],
  guests: [{ type: Schema.Types.ObjectId, ref: 'Board' }],
  activities: [{ type: Schema.Types.ObjectId, ref: 'Activity' }],
  filters: filterSchema,
  settings: settingsSchema, // Added settings field
  type: { type: String, default: 'USER' },
  verified: { type: Boolean, default: false },
  resetToken: { type: String, default: null },
  resetTokenExpiry: { type: Date, default: null },
  starredBoards: [{ type: Schema.Types.ObjectId, ref: 'Board' }],
  recentBoards: [{ type: Schema.Types.ObjectId, ref: 'Board' }],
  pushTokens: [{ type: String }],

  // Fields for Google Login
  googleId: { type: String, unique: true, sparse: true },
  registeredWithGoogle: { type: Boolean, default: false },

  slack: slackSchema,

  // Online Status Fields
  online: { type: Boolean, default: false },
  lastActive: { type: Date, default: null }

});

userSchema.pre('validate', function (next) {
  if (this.isNew || this.isModified('email')) {
    this.username = this.email.split('@')[0];
  }
  next();
});

userSchema.methods.checkPremiumAccess = async function () {
  const premiumWorkspace = await Workspace.findOne({
    $or: [
      { 'members.user': this._id },
      { guests: this._id }
    ],
    subscription: { $ne: null }
  }).select('_id');

  return !!premiumWorkspace;
};

userSchema.methods.getPremiumData = async function() {
  const isPremium = await this.checkPremiumAccess();
  return {
    ...this.toObject(),
    isPremiumMember: isPremium,
    canSeeOnlineStatus: isPremium,
  };
};

const User = mongoose.model('User', userSchema);

module.exports = User;

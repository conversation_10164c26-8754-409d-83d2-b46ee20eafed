const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const activitySchema = new Schema({
    card: { type: Schema.Types.Mixed, ref: 'Card', required: true },
    initiator: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    actionType: { type: String, required: true },
    details: { type: String },
    actionTypeId: { type: Schema.Types.Mixed }, //it could be comment id, checklist id, etc.
    createdAt: { type: Date, default: Date.now }
});

const Activity = mongoose.model('Activity', activitySchema);

module.exports = Activity;

const mongoose = require('mongoose');
const { Schema } = mongoose;

const subscriptionSchema = new Schema({
    //Stripe schemma
    workspaceId: { type: Schema.Types.ObjectId, ref: 'Workspace', required: true },
    status: { type: String, enum: ['active', 'canceled', 'past_due', 'incomplete', 'incomplete_expired', 'trialing', 'unpaid'], default: 'incomplete' },
    planType: { type: String, enum: ['Free', 'Standard', 'Premium', 'Enterprise'], default: 'Free' },
    billingCycle: { type: String, enum: ['monthly', 'annually'], default: 'annually' },
    priceId: String,
    quantity: { type: Number, default: 1 },
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
    cancelAtPeriodEnd: { type: Boolean, default: false },
    stripeCustomerId: String,
    stripeSubscriptionId: String,
    lastPaymentDate: Date,
    nextPaymentDate: Date,
    canceledAt: Date,
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },

    //workspace member schema
    reservedSeats: { type: Number, default: 0 },
    pendingSeats: { type: Number, default: 0 },
    totalSeats: { type: Number, default: 0 },
    seatHistory: [{
        action: { type: String, enum: ['added', 'removed', 'paid', 'reserved', 'fixed'] },
        seats: { type: Number },
        timestamp: { type: Date, default: Date.now },
        userId: { type: String },
        reason: { type: String }
    }]
});

// Update timestamps and debug logging
subscriptionSchema.pre('save', function (next) {
    this.updatedAt = new Date();

    // Debug log for reservedSeats changes
    if (this.isModified('reservedSeats') || this.isModified('pendingSeats') || this.isModified('totalSeats')) {
        // Get stack trace to see what's calling this
        const stack = new Error().stack;
        const callerLine = stack.split('\n')[3]; // Get the calling function

        console.log(`[RESERVED_SEATS_DEBUG] Subscription Model Pre-Save - Seat changes detected:`, {
            subscriptionId: this._id,
            workspaceId: this.workspaceId,
            isNew: this.isNew,
            callerInfo: callerLine,
            modifiedFields: {
                reservedSeats: this.isModified('reservedSeats'),
                pendingSeats: this.isModified('pendingSeats'),
                totalSeats: this.isModified('totalSeats'),
                status: this.isModified('status'),
                planType: this.isModified('planType')
            },
            currentValues: {
                reservedSeats: this.reservedSeats,
                reservedSeatsType: typeof this.reservedSeats,
                pendingSeats: this.pendingSeats,
                totalSeats: this.totalSeats,
                status: this.status,
                planType: this.planType,
                quantity: this.quantity
            },
            lastSeatHistoryEntry: this.seatHistory && this.seatHistory.length > 0 ?
                this.seatHistory[this.seatHistory.length - 1] : null
        });

        // If reservedSeats is being modified and it's a suspicious value, log more details
        if (this.isModified('reservedSeats') && this.reservedSeats > 1000) {
            console.log(`[RESERVED_SEATS_DEBUG] SUSPICIOUS - Large reservedSeats value detected:`, {
                subscriptionId: this._id,
                reservedSeats: this.reservedSeats,
                reservedSeatsType: typeof this.reservedSeats,
                fullStackTrace: stack
            });
        }
    }

    next();
});

// Indexes
subscriptionSchema.index({ workspaceId: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ stripeCustomerId: 1 });
subscriptionSchema.index({ stripeSubscriptionId: 1 });

module.exports = mongoose.models.Subscription || mongoose.model('Subscription', subscriptionSchema);

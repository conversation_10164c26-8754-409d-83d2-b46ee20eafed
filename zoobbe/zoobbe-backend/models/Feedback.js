const mongoose = require('mongoose');

const feedbackSchema = new mongoose.Schema({
    title: { type: String, required: true, trim: true, maxlength: 255 },
    url: { type: String, required: true, trim: true },
    description: { type: String, required: true, trim: true },
    type: {
        type: String,
        required: true,
        enum: [
            'bug', 'feature-request', 'ui-suggestion', 'performance', 'accessibility',
            'content', 'security', 'integration', 'documentation', 'workflow',
            'compatibility', 'localization', 'usability', 'support', 'other',
        ]
    },
    upVotes: { type: Number, default: 0 },
    downVotes: { type: Number, default: 0 },
    status: { type: String, enum: ['open', 'in-progress', 'resolved', 'closed'], default: 'open' },
    createdAt: { type: Date, default: Date.now },
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
});

module.exports = mongoose.model('Feedback', feedbackSchema);

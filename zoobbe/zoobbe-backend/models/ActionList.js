const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const actionListSchema = new Schema({
  title: { type: String, required: true },
  cards: [{ type: Schema.Types.ObjectId, ref: 'Card' }],
  board: { type: Schema.Types.ObjectId, ref: 'Board' },
  boardShortId: { type: Schema.Types.Mixed, ref: 'Board' },
  importId: { type: String, required: false },
  archived: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  order: { type: Number, required: true }, // New field for ordering
  type: { type: String, default: 'ACTIONLIST' },
  watchers: [{ type: Schema.Types.ObjectId, ref: 'User' }],

});

module.exports = mongoose.model('ActionList', actionListSchema);

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const notificationSchema = new Schema({
    member: { type: Schema.Types.ObjectId, ref: 'User' },
    initiator: { type: Schema.Types.ObjectId, ref: 'User' },
    type: { type: String },
    targetType: { type: String },
    targetId: { type: Schema.Types.Mixed, required: true },
    message: { type: String },
    read: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now }
});


module.exports = mongoose.model('Notification', notificationSchema);

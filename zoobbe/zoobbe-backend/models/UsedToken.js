const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const usedTokenSchema = new Schema({
    token: { type: String, required: true, unique: true }, // Store the used token
    usedAt: { type: Date, default: Date.now },
});

// Automatically delete records after 7 days
usedTokenSchema.index({ usedAt: 1 }, { expireAfterSeconds: 7 * 24 * 60 * 60 });

module.exports = mongoose.model('UsedToken', usedTokenSchema);
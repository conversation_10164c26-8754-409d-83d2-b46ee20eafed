const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const backgroundSchema = new Schema({
    fileHash: { type: String, required: true },
    fileName: { type: String, required: true },
    sizes: {
        original: { type: String, required: true },
        thumbnail: { type: String, required: false },
        medium: { type: String, required: false },
        large: { type: String, required: false },
    },
    coverColors: { type: [String], required: false }, // Array of colors in string format, e.g., ['rgb(0, 0, 0)', ...]
    createdAt: { type: Date, default: Date.now },
});

module.exports = mongoose.model('Background', backgroundSchema);

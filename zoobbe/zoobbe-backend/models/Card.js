const mongoose = require('mongoose');
const Board = require('../models/Board');

// Import nanoid asynchronously using dynamic import in CommonJS format
let nanoid;
(async () => {
  const nanoidModule = await import('nanoid');
  nanoid = nanoidModule.customAlphabet('1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 8);
})();


const Schema = mongoose.Schema;

const labelSchema = new Schema({
  text: { type: String, required: false },
  color: { type: String, required: false },
  createdAt: { type: Date, default: Date.now }
});

const commentSchema = new Schema({
  comment: { type: String, required: false },
  member: { type: Schema.Types.ObjectId, ref: 'User', required: false },
  time: { type: Date, default: Date.now },
  reacts: [{ type: String }] // Modify as needed for reaction types
});

const checklistItemSchema = new Schema({
  title: { type: String, required: false },
  checked: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now }
});

const checklistSchema = new Schema({
  title: { type: String, required: false },
  items: [checklistItemSchema],
  createdAt: { type: Date, default: Date.now }
});

const attachmentSchema = new Schema({
  name: { type: String, required: false },
  url: { type: String, required: false },
  signedUrl: { type: String, required: false },
  uploadedAt: { type: Date, default: Date.now },
  fileHash: { type: String, required: false, default: null },
  fileType: { type: String, required: false, default: null },
  cardId: { type: Schema.Types.Mixed, ref: 'Card', required: false },
  expiresAt: { type: Date, required: false }

});

const activitySchema = new Schema({
  card: { type: Schema.Types.ObjectId, ref: 'Card', required: true },
  initiator: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  actionType: { type: String, required: true }, // e.g., "commented", "joined", "added member", "added checklist"
  details: { type: String }, // Optional: additional context or details about the action
  createdAt: { type: Date, default: Date.now },
});


const cardSchema = new Schema({
  title: { type: String, required: false },
  description: { type: String },
  actionList: { type: Schema.Types.ObjectId, ref: 'ActionList' },
  actionListTitle: { type: String, ref: 'ActionList' },
  board: { type: String, required: true, index: true }, // Add index for better performance
  importId: { type: String, required: false },
  boardTitle: { type: String, ref: 'Board' },
  boardLink: { type: String, ref: 'Board' },
  archived: { type: Boolean, default: false, index: true }, // Add index for better performance
  isArchivedWithList: { type: Boolean, default: false },
  isActivityDetails: { type: Boolean, default: false },
  users: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  order: { type: Number, required: true },
  createdAt: { type: Date, default: Date.now },
  comments: [commentSchema],
  priority: { type: String, default: 'Normal' },
  checklists: [checklistSchema],
  attachments: [attachmentSchema],
  watchers: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  labels: [labelSchema],
  activities: [{ type: Schema.Types.ObjectId, ref: 'Activity' }],
  cover: {
    name: { type: String, required: false },
    url: { type: String, required: false },
    cardId: { type: Schema.Types.Mixed, ref: 'Card', required: false },
    coverColor: { type: [String], required: false },
    attachmentId: { type: String, required: false },
    expiresAt: { type: Date, required: false }
  },
  dueDate: {
    date: { type: Date, required: false },
    startDate: { type: Date },
    dueTime: { type: String },
    reminder: { type: String, default: 'None' },
    completed: { type: Boolean, default: false },
    status: { type: String, default: '' },
  },
  shortId: { type: String, unique: true },
  shortLink: { type: String },
  permalink: { type: String },
  cardNumber: { type: Number },
  type: { type: String, default: 'CARD' }

}, {
  // Add compound index for board+archived to speed up card counting
  indexes: [
    { board: 1, archived: 1 }
  ]
});

// Static method to count documents by board shortId
cardSchema.statics.countByBoardShortId = async function (boardShortId) {
  return this.countDocuments({ board: boardShortId });
};

cardSchema.pre('save', async function (next) {
  try {
    if (!this.shortId) {
      this.shortId = nanoid();
    }

    if (this.isNew && this.board) {
      const board = await Board.findOne({ shortId: this.board });

      if (board) {
        // Get the next available card number from the board
        this.cardNumber = await board.getNextCardNumber();
      } else {
        this.cardNumber = 1; // Default cardNumber if board not found
      }
    } else if (this.isNew) {
      this.cardNumber = 1;
    }

    this.shortLink = `/c/${this.shortId}`;
    this.permalink = `/c/${this.shortId}/${this.cardNumber}-${this.title
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase()
      }`;

    next();
  } catch (error) {
    return next(error);
  }
});


module.exports = mongoose.model('Card', cardSchema);

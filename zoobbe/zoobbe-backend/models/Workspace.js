const mongoose = require('mongoose');
const Counter = require('./counter');
const Schema = mongoose.Schema;

// Import nanoid asynchronously using dynamic import in CommonJS format
let nanoid;
(async () => {
  const nanoidModule = await import('nanoid');
  nanoid = nanoidModule.customAlphabet('1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 8);
})();

// Define your workspace schema
const workspaceSchema = new Schema({
  name: { type: String, required: true },
  shortName: { type: String },
  ownerId: { type: String, required: true, index: true }, // Add index for better performance
  boards: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Board' }],
  members: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true }, // Add index for better performance
    role: { type: String, default: 'admin' },
    accessLevel: { type: String, enum: ['full', 'limited', 'view-only'], default: 'limited' },
  }],
  guests: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true }], // Add index for better performance
  type: { type: String },
  description: { type: String },
  createdAt: { type: Date, default: Date.now },
  cardsCount: { type: Number, default: 0 },
  shortId: { type: String, unique: true, index: true }, // Add index for better performance
  shortLink: { type: String },
  permalink: { type: String },
  joinLink: { type: String, default: null },
  subscription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  type: { type: String, default: 'WORKSPACE' }
}, {
  // Add compound indexes for common query patterns
  indexes: [
    { 'members.user': 1 }, // Index for finding workspaces by member
    { guests: 1 }, // Index for finding workspaces by guest
    { ownerId: 1, createdAt: -1 } // Index for finding workspaces by owner, sorted by creation date
  ]
});


workspaceSchema.pre('save', async function (next) {
  try {
    if (!this.shortId) {
      this.shortId = nanoid(8);
    }

    this.shortLink = `/w/${this.shortId}`;
    this.permalink = `/w/${this.shortId}/${this.
      name?.replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase()
      }`;

    if (!this.shortName) {
      let shortNameCandidate = this.name.replace(/\s+/g, '').toLowerCase();
      let workspace = await mongoose.model('Workspace').findOne({ shortName: shortNameCandidate });
      if (workspace) {
        const counter = await Counter.findByIdAndUpdate(
          { _id: 'shortName' },
          { $inc: { seq: 1 } },
          { new: true, upsert: true }
        );
        shortNameCandidate = `${this.name.replace(/\s+/g, '').toLowerCase()}${counter.seq}`;
      }
      this.shortName = shortNameCandidate;
    }

    next();
  } catch (error) {
    return next(error);
  }
});

// Export the model only if it hasn't been compiled yet
module.exports = mongoose.models.Workspace || mongoose.model('Workspace', workspaceSchema);

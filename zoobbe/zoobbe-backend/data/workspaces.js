// data/workspaces.js or data/workspaces.json
const workspaces = [
  {
    "id": "2342",
    "name": "Workspaces 1",
    "boards": [
      {
        "id": 23423,
        "title": "Zoobbe, Inc.",
        "actionLists": [
          {
            "id": 1,
            "name": "<PERSON> <PERSON>",
            "cards": [
              {
                "id": 101,
                "title": "Implement Login Functionality for Website Redesign",
                "description": "Develop and integrate secure login system using OAuth 2.0 for new website UI/UX overhaul.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 102,
                "title": "Prepare Technical Documentation for API Integration",
                "description": "Create comprehensive API documentation including endpoints, payloads, and authentication methods for backend team.",
                "users": []
              }
            ]
          },
          {
            "id": 2,
            "name": "In Progress",
            "cards": [
              {
                "id": 201,
                "title": "Refactor Legacy Codebase for Improved Performance",
                "description": "Optimize existing code structure, identify and eliminate bottlenecks for faster execution and enhanced scalability.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 202,
                "title": "Implement Automated Testing Suite for QA Team",
                "description": "Set up automated testing framework to ensure code quality and functionality meet project requirements.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              }
            ]
          },
          {
            "id": 3,
            "name": "Done",
            "cards": [
              {
                "id": 301,
                "title": "Deploy New Feature Set to Production Environment",
                "description": "Coordinate deployment of latest feature set to live servers, ensuring minimal downtime and smooth rollout.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 302,
                "title": "Conduct User Acceptance Testing (UAT) for New Features",
                "description": "Engage with stakeholders to validate new features against user expectations and ensure functionality meets requirements.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "id": 23424,
        "title": "Facebook, Inc.",
        "actionLists": [
          {
            "id": 1,
            "name": "To Do",
            "cards": [
              {
                "id": 101,
                "title": "Implement Login Functionality for Website Redesign",
                "description": "Develop and integrate secure login system using OAuth 2.0 for new website UI/UX overhaul.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 102,
                "title": "Prepare Technical Documentation for API Integration",
                "description": "Create comprehensive API documentation including endpoints, payloads, and authentication methods for backend team.",
                "users": []
              }
            ]
          },
          {
            "id": 2,
            "name": "In Progress",
            "cards": [
              {
                "id": 201,
                "title": "Refactor Legacy Codebase for Improved Performance",
                "description": "Optimize existing code structure, identify and eliminate bottlenecks for faster execution and enhanced scalability.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 202,
                "title": "Implement Automated Testing Suite for QA Team",
                "description": "Set up automated testing framework to ensure code quality and functionality meet project requirements.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              }
            ]
          },
          {
            "id": 3,
            "name": "Done",
            "cards": [
              {
                "id": 301,
                "title": "Deploy New Feature Set to Production Environment",
                "description": "Coordinate deployment of latest feature set to live servers, ensuring minimal downtime and smooth rollout.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 302,
                "title": "Conduct User Acceptance Testing (UAT) for New Features",
                "description": "Engage with stakeholders to validate new features against user expectations and ensure functionality meets requirements.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "id": 234624,
        "title": "Google, Inc.",
        "actionLists": [
          {
            "id": 1,
            "name": "To Do",
            "cards": [
              {
                "id": 101,
                "title": "Implement Login Functionality for Website Redesign",
                "description": "Develop and integrate secure login system using OAuth 2.0 for new website UI/UX overhaul.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 102,
                "title": "Prepare Technical Documentation for API Integration",
                "description": "Create comprehensive API documentation including endpoints, payloads, and authentication methods for backend team.",
                "users": []
              }
            ]
          },
          {
            "id": 2,
            "name": "In Progress",
            "cards": [
              {
                "id": 201,
                "title": "Refactor Legacy Codebase for Improved Performance",
                "description": "Optimize existing code structure, identify and eliminate bottlenecks for faster execution and enhanced scalability.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 202,
                "title": "Implement Automated Testing Suite for QA Team",
                "description": "Set up automated testing framework to ensure code quality and functionality meet project requirements.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              }
            ]
          },
          {
            "id": 3,
            "name": "Done",
            "cards": [
              {
                "id": 301,
                "title": "Deploy New Feature Set to Production Environment",
                "description": "Coordinate deployment of latest feature set to live servers, ensuring minimal downtime and smooth rollout.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              },
              {
                "id": 302,
                "title": "Conduct User Acceptance Testing (UAT) for New Features",
                "description": "Engage with stakeholders to validate new features against user expectations and ensure functionality meets requirements.",
                "users": [
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/6332a834fcd2a90107bee76e/70a3e515372b3d7874102eb1962165d9/50.png"
                  },
                  {
                    "avatar": "https://trello-members.s3.amazonaws.com/5dc169ea940fc473bab7d56f/703d938855e9d548c68fe9f92ab1f52f/170.png"
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]

module.exports = workspaces;

// mailer.js
const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: true,
  auth: {
    user: process.env.SMTP_USERNAME, // your email
    pass: process.env.EMAIL_PASS, // your email password
  },
});

const sendVerificationEmail = (to, verificationToken, name) => {
  const verifyLink = `${process.env.SITE_URL}/verify?token=${verificationToken}`;

  const mailOptions = {
    from: `Zoobbe <${process.env.EMAIL_USER}>`,
    to,
    subject: '<PERSON><PERSON>, Inc - Verify Your Email',
    html: `
      <div class="container" style="width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border: 1px solid #dddddd; border-radius: 8px;">
        <p style="font-family: Arial, sans-serif; color: #333333; line-height: 1.6;">Hi ${name},</p>
        <p style="font-family: Arial, sans-serif; color: #333333; line-height: 1.6;">Thank you for signing up with Zoobbe! Please verify your email by clicking the link below:</p>
        <p><a href="${verifyLink}" style="color: #4a90e2; text-decoration: none; font-weight: bold;">Verify Email</a></p>
        <p style="font-family: Arial, sans-serif; color: #333333; line-height: 1.6;">If you didn’t create this account, you can ignore this email.</p>
        <p style="font-family: Arial, sans-serif; color: #333333; line-height: 1.6;">Best,<br>The Zoobbe Team</p>
        <div class="footer" style="font-size: 0.875em; color: #888888; margin-top: 20px; text-align: center;">&copy; Zoobbe, Inc.</div>
      </div>
    `,
  };

  return transporter.sendMail(mailOptions);
};




const sendResetPasswordEmail = async (email, token, name) => {
  const resetLink = `${process.env.SITE_URL}/reset?token=${token}`;


  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: true,
    auth: {
      user: process.env.SMTP_USERNAME,
      pass: process.env.EMAIL_PASS,
    },
  });

  const mailOptions = {
    from: `Zoobbe <${process.env.EMAIL_USER}>`,
    to: email,
    subject: 'Password Reset Request',
    html: `
        <div style="width: 100%; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px; border-radius: 8px; font-family: Arial, sans-serif; color: var(--scrolbar-thumb-background-color);">
          <h2 style="color: #4a90e2; text-align: center;">Zoobbe Password Reset</h2>
          <p style="font-size: 16px;">Hi ${name},</p>
          <p style="font-size: 16px; line-height: 1.6;">
            You requested a password reset. Click the button below to reset your password:
          </p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${resetLink}" style="display: inline-block; background-color: #4a90e2; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-size: 16px;">
              Reset Password
            </a>
          </div>
          <p style="font-size: 14px; color: #777;">
            If you didn’t request this, please ignore this email.
          </p>
          <hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0;">
          <p style="font-size: 12px; color: #aaa; text-align: center;">
            &copy; ${new Date().getFullYear()} Zoobbe, Inc. All rights reserved.
          </p>
        </div>
      `
  };

 const info = await transporter.sendMail(mailOptions);

};


const boardInvitationTemplate = (name, inviteLink, message) => {
  const isWorkspace = inviteLink.includes('/w/');
  const type = isWorkspace ? 'workspace' : 'board';

  return `<html>
            <body style="width: 520px; margin: auto; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; font-size: 14px; color: #172B4D;">
              <table style="width: 100%; background: #FAFBFC; margin: 12px 0; padding: 40px 40px 25px; border-radius: 10px; border-spacing: 0; text-align: left;">
                <tbody>
                  <tr>
                    <td>
                      <img width="85" src="https://zoobbe.com/logo.png" alt="Zoobbe Logo">
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <h2 style="font-size: 28px; font-weight: 700; line-height: 33px; margin: 20px 0 16px; letter-spacing: -0.5px;">
                        You've been invited to join the "${name}" ${type} on Zoobbe!
                      </h2>
                      <p style="line-height: 21px;">
                        Join this ${type} on Zoobbe to collaborate, manage projects, and reach new productivity peaks.
                      </p>
                      <p style="margin: 24px 0; line-height: 32px;">
                        <a href="${inviteLink}" style="border-radius: 3px; background: #0052CC; color: #FFFFFF; font-weight: 500; line-height: 20px; text-decoration: none; padding: 8px 15px;">Accept Invitation</a>
                      </p>
                      <div style="margin: 24px 0; padding: 24px; background: #FFFFFF; border-radius: 10px;">
                        <div style="margin-bottom: 12px; display: flex; align-items: center;">
                          <img style="border-radius: 50%; margin-right: 8px;" width="24" height="24" src="https://zoobbe.com/logo.png" alt="User Avatar">
                          <span style="font-weight: 600;">A note from the team:</span>
                        </div>
                        <div>${message}</div>
                      </div>
                      <p style="line-height: 21px; margin-top: 24px;">
                        <b>What is Zoobbe?</b> Imagine a whiteboard filled with lists of sticky notes, each note as a task. Now imagine that each sticky note has photos, attachments, documents, due dates, and more.<br><br> Now imagine that you can take that whiteboard anywhere on your smartphone, and access it from any computer through the web. That's Zoobbe! <a href="http://zoobbe.com" style="color: #5E6C84;">Learn more</a>
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style="width: 405px; border-top: solid 2px #DFE1E6; margin: 48px auto 0; text-align: center;">
                <div style="margin-top: 22px;">
                  <img width="147" src="https://zoobbe.com/logo.png" alt="Zoobbe Logo">
                </div>
              </div>
            </body>
          </html>`;
}

const boardJoinedTemplate = (name, inviteLink, message) => {

  const isWorkspace = inviteLink.includes('/w/');
  const type = isWorkspace ? 'workspace' : 'board';

  return `<html>
            <body style="width: 520px; margin: auto; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; font-size: 14px; color: #172B4D;">
              <table style="width: 100%; background: #FAFBFC; margin: 12px 0; padding: 40px 40px 25px; border-radius: 10px; border-spacing: 0; text-align: left;">
                <tbody>
                  <tr>
                    <td>
                      <img width="85" src="https://zoobbe.com/logo.png" alt="Zoobbe Logo">
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <h2 style="font-size: 28px; font-weight: 700; line-height: 33px; margin: 20px 0 16px; letter-spacing: -0.5px;">
                        You've been invited to join the "${name}" ${type} on Zoobbe!
                      </h2>
                      <p style="line-height: 21px;">
                        Join this ${type} on Zoobbe to collaborate, manage projects, and reach new productivity peaks.
                      </p>
                      <p style="margin: 24px 0; line-height: 32px;">
                        <a href="${inviteLink}" style="border-radius: 3px; background: #0052CC; color: #FFFFFF; font-weight: 500; line-height: 20px; text-decoration: none; padding: 8px 15px;">Go to ${type}</a>
                      </p>
                      <div style="margin: 24px 0; padding: 24px; background: #FFFFFF; border-radius: 10px;">
                        <div style="margin-bottom: 12px; display: flex; align-items: center;">
                          <img style="border-radius: 50%; margin-right: 8px;" width="24" height="24" src="https://zoobbe.com/logo.png" alt="User Avatar">
                          <span style="font-weight: 600;">A note from the team:</span>
                        </div>
                        <div>${message}</div>
                      </div>
                      <p style="line-height: 21px; margin-top: 24px;">
                        <b>What is Zoobbe?</b> Imagine a whiteboard filled with lists of sticky notes, each note as a task. Now imagine that each sticky note has photos, attachments, documents, due dates, and more.<br><br> Now imagine that you can take that whiteboard anywhere on your smartphone, and access it from any computer through the web. That's Zoobbe! <a href="http://zoobbe.com" style="color: #5E6C84;">Learn more</a>
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style="width: 405px; border-top: solid 2px #DFE1E6; margin: 48px auto 0; text-align: center;">
                <div style="margin-top: 22px;">
                  <img width="147" src="https://zoobbe.com/logo.png" alt="Zoobbe Logo">
                </div>
              </div>
            </body>
          </html>`;
}


const sendInvitationEmail = ({
  to,
  shortId,
  inviteToken,
  name,
  message,
  invitePath = 'b',
  memberName = 'Zoobbe',
}) => {
  const inviteLink = `${process.env.SITE_URL}/invite/${invitePath}/${shortId}?token=${inviteToken}`;

  const mailOptions = {
    from: `${memberName} <${process.env.EMAIL_USER}>`,
    to,
    subject: `Invitation to join ${name} on Zoobbe`,
    html: boardInvitationTemplate(name, inviteLink, message),
  };

  return transporter.sendMail(mailOptions);
};



const sendJoinedEmail = (to, shortId, name, message, invitePath = 'b', memberName = 'Zoobbe') => {
  let boards = '';

  if (invitePath === 'w') {
    boards = '/boards';
  }

  const inviteLink = `${process.env.SITE_URL}/${invitePath}/${shortId}${boards}`;


  const mailOptions = {
    from: `${memberName} <${process.env.EMAIL_USER}>`,
    to,
    subject: `Invitation to join ${name} on Zoobbe`,
    html: boardJoinedTemplate(name, inviteLink, message)
  };


  return transporter.sendMail(mailOptions);
};

const sendRemoveFromEmail = (to, name, invitePath, memberName, shortLink = '#') => {
  const type = invitePath === 'w' ? 'workspace' : 'board';

  const mailOptions = {
    from: `Zoobbe <${process.env.EMAIL_USER}>`,
    to,
    subject: `${memberName} removed you from the ${type} ${name}`,
    html: `<html>
            <body style="width: 520px; margin: auto; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; font-size: 14px; color: #172B4D;">
              <table style="width: 100%; background: #FAFBFC; margin: 12px 0; padding: 40px 40px 25px; border-radius: 10px; border-spacing: 0; text-align: left;">
                <tbody>
                  <tr>
                    <td>
                      <img width="85" src="https://zoobbe.com/logo.png" alt="Zoobbe Logo">
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <h1 style="font-size: 28px; font-weight: normal; line-height: 33px; margin: 20px 0 16px; letter-spacing: -0.5px;">
                        Here's the letest....
                      </h1>
                      <p style="line-height: 21px;">
                      <strong>${memberName}</strong> removed you from the ${type} <a href="https://zoobbe.com${shortLink}">${name}</a>
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style="width: 405px; border-top: solid 2px #DFE1E6; margin: 48px auto 0; text-align: center;">
                <div style="margin-top: 22px;">
                  <img width="147" src="https://zoobbe.com/logo.png" alt="Zoobbe Logo">
                </div>
              </div>
            </body>
          </html>`
  };

  return transporter.sendMail(mailOptions);
};


module.exports = {
  sendVerificationEmail,
  sendResetPasswordEmail,
  sendInvitationEmail,
  sendJoinedEmail,
  sendRemoveFromEmail
};

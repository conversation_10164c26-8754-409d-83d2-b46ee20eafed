const admin = require("firebase-admin");

const serviceAccount = JSON.parse(process.env.FIREBASE_CREDENTIALS);

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
});

const sendPushNotification = async ({ tokens, title, body, url }) => {
    if (!Array.isArray(tokens)) {
        tokens = [tokens]; // Ensure tokens is an array
    }

    const message = {
        notification: { title, body },
        data: { url }, // Attach the card URL
        tokens,
    };

    try {
        const response = await admin.messaging().sendEachForMulticast(message);
    } catch (error) {
        console.error("Error sending notification:", error);
    }
};

module.exports = { sendPushNotification };

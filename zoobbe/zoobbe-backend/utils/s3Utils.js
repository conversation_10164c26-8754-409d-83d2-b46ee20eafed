const { S3Client, PutO<PERSON>Command, GetObjectCommand, DeleteObjectCommand, CopyObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const { Upload } = require('@aws-sdk/lib-storage');

const sharp = require('sharp');

// S3 Client setup (reuseable configuration)
const s3 = new S3Client({
    region: process.env.BUCKET_REGION,
    credentials: {
        accessKeyId: process.env.AWS_S3_ACCESS_KEY,
        secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
    },
});

// Reusable function to upload a file to S3
const uploadFileToS3 = async ({ file, folderName = '', isSharp = true, bucketName = 'zoobbe-card-attachments', width = 180, height, fit = 'cover', req }) => {

    const fileKey = folderName ? `${folderName}/${file.originalname}` : file.originalname;
    let buffer = file.buffer;

    // Resize image if needed
    if (file.mimetype.startsWith('image/') && isSharp) {
        const metadata = await sharp(file.buffer).metadata();
        if (!height) {
            height = Math.round((metadata.height / metadata.width) * width);
        }
        buffer = await sharp(file.buffer).resize(width, height, { fit }).toBuffer();
    }

    const params = {
        Bucket: bucketName,
        Key: fileKey,
        Body: buffer,
        ContentType: file.mimetype,
    };

    try {
        const totalSize = buffer.length;
        let uploadedBytes = 0;
        const startTime = Date.now();

        const upload = new Upload({
            client: s3,
            params,
            queueSize: 3, // Adjust concurrency
        });

        upload.on('httpUploadProgress', (progress) => {
            uploadedBytes = progress.loaded;
            const percentage = ((uploadedBytes / totalSize) * 100).toFixed(2);

            // Estimate remaining time
            const elapsedTime = (Date.now() - startTime) / 1000; // in seconds
            const speed = uploadedBytes / elapsedTime; // bytes per second
            const remainingTime = ((totalSize - uploadedBytes) / speed).toFixed(2); // in seconds

            console.log({speed, percentage, remainingTime});

            const io = req.app.get('socketio');

            // Emit progress update via WebSocket
            io.emit('uploadProgress', {
                percentage,
                remainingTime: remainingTime > 0 ? `${remainingTime}s` : 'Completed',
            });
        });

        await upload.done();

        // Generate custom file URL
        const fileUrl = `https://cloud.zoobbe.com/${fileKey}`;

        return fileUrl;
    } catch (err) {
        console.error('S3 Upload Error:', err);
        throw err;
    }
};

const uploadUrlToS3 = async ({ url, folderName = '', bucketName = 'zoobbe-card-attachments', fileName = '', isSharp = true, width = 180, height, fit = 'cover' }) => {
    if (!url) {
        throw new Error('Image URL is required');
    }

    // Fetch the image from the URL
    const imageResponse = await fetch(url);
    if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image from URL: ${url}`);
    }

    // Convert the image to a buffer
    const arrayBuffer = await imageResponse.arrayBuffer();
    let buffer = Buffer.from(arrayBuffer);
    const mimeType = imageResponse.headers.get('content-type') || 'image/jpeg';

    // Generate a unique file name if not provided
    fileName = fileName || `image-${Date.now()}.jpg`;

    // Process the image with Sharp if required
    if (mimeType.startsWith('image/') && isSharp) {
        const metadata = await sharp(buffer).metadata();

        if (!height) {
            height = Math.round((metadata.height / metadata.width) * width);
        }

        buffer = await sharp(buffer).resize(width, height, { fit }).toBuffer();
    }

    // Prepare the S3 parameters
    const fileKey = folderName ? `${folderName}/${fileName}` : fileName;
    const params = {
        Bucket: bucketName,
        Key: fileKey,
        Body: buffer,
        ContentType: mimeType,
    };

    try {
        const command = new PutObjectCommand(params);
        await s3.send(command);

        const fileUrl = `https://cloud.zoobbe.com/${fileKey}`;

        return fileUrl;
    } catch (err) {
        console.error('S3 Upload Error:', err);
        throw err;
    }
};



// Reusable function to get a signed URL for a file in S3
const getFileFromS3 = async (key, folderName = '', expiresIn = 10, bucketName = 'zoobbe-card-attachments') => {
    // Construct the S3 file key (path), skipping folderName if it's empty
    const fileKey = folderName ? `${folderName}/${key}` : key;

    const params = {
        Bucket: bucketName,
        Key: fileKey, // S3 file path (key)
    };

    try {
        const command = new GetObjectCommand(params);
        // Generate a signed URL for the object
        const url = await getSignedUrl(s3, command, { expiresIn });

        return url; // Return the signed URL
    } catch (err) {
        console.error('S3 Get File Error:', err);
        throw err;
    }
};

const getFileUrlFromS3 = ({ bucketName, region, key }) => {
    if (!bucketName || !region || !key) {
        throw new Error('Missing required parameters: bucketName, region, or key');
    }
    return `https://cloud.zoobbe.com/${key}`;
};


// Reusable function to delete a file from S3
const deleteFileFromS3 = async (key, folderName = '', bucketName = 'zoobbe-card-attachments') => {
    // Construct the S3 file key (path), skipping folderName if it's empty
    const fileKey = folderName ? `${folderName}/${key}` : key;

    const params = {
        Bucket: bucketName,
        Key: fileKey, // File key in S3
    };

    try {
        const command = new DeleteObjectCommand(params);
        await s3.send(command);

        console.log(`File ${fileKey} deleted successfully from S3.`);
    } catch (err) {
        console.error('S3 Delete File Error:', err);
        throw err;
    }
};

// Function to rename (move) a file in S3 by copying to a new key and deleting the old one
const updateFileInS3 = async (oldFileKey, newFileKey, folderName = '', bucketName = 'zoobbe-card-attachments') => {
    // Construct the S3 keys (paths), skipping folderName if it's empty
    const oldKey = folderName ? `${folderName}/${oldFileKey}` : oldFileKey;
    const newKey = folderName ? `${folderName}/${newFileKey}` : newFileKey;

    try {
        // Copy the file to the new key (renaming it)
        const copyParams = {
            Bucket: bucketName,
            CopySource: `${bucketName}/${oldKey}`, // Source file path
            Key: newKey, // Destination file path (new name)
        };
        const copyCommand = new CopyObjectCommand(copyParams);
        await s3.send(copyCommand);

        // Delete the old file
        await deleteFileFromS3(oldFileKey, folderName);

        console.log(`File renamed from ${oldKey} to ${newKey}`);
    } catch (err) {
        console.error('S3 Rename File Error:', err);
        throw err;
    }
};

// Helper to check if a file exists in S3
const isFileExistsInS3 = async (key, bucket) => {
    try {
        await s3.send(new HeadObjectCommand({ Bucket: bucket, Key: key }));
        return true; // File exists
    } catch (error) {
        if (error.name === 'NotFound') {
            return false; // File doesn't exist
        }
        // throw error; // Propagate other errors
    }
};


module.exports = {
    uploadFileToS3,
    uploadUrlToS3,
    getFileFromS3,
    getFileUrlFromS3,
    deleteFileFromS3,
    updateFileInS3,
    isFileExistsInS3
};

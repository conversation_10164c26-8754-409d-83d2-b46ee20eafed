// redis.js
const Redis = require('ioredis');

// Create a new Redis client
const redis = new Redis(process.env.REDIS_CACHE_HOST); // Connects to 127.0.0.1:6379 by default

// Read cache enabled status from environment variable
const cacheEnabled = false;

// Set a cache value
async function setCache(key, value, expirationInSeconds = 3600) {
    if (!cacheEnabled) return; // Skip caching if disabled

    try {
        await redis.set(key, value, 'EX', expirationInSeconds);
        console.log(`Cache set for key: ${key}`);
    } catch (error) {
        console.error('Error setting cache:', error);
    }
}

// Get a cache value
async function getCache(key) {
    if (!cacheEnabled) return null; // Return null if caching is disabled

    try {
        const value = await redis.get(key);
        if (value) {
            console.log(`Cache retrieved for key: ${key}, value: ${value}`);
            return value;
        } else {
            console.log(`Cache miss for key: ${key}`);
            return null; // Key not found
        }
    } catch (error) {
        console.error('Error getting cache:', error);
        return null; // Return null in case of error
    }
}

// Delete a cache key
const deleteCache = async (key) => {
    if (!cacheEnabled) return; // Skip deletion if caching is disabled

    try {
        await redis.del(key);
        console.log(`Cache deleted for key: ${key}`);
    } catch (error) {
        console.error('Error deleting cache:', error);
    }
};

// Export the functions
module.exports = {
    setCache,
    getCache,
    deleteCache
};

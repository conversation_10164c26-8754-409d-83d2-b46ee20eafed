const Card = require('../models/Card');
const ActionList = require('../models/ActionList');
const User = require('../models/User');
const Board = require('../models/Board');
const Notification = require('../models/Notification');
const Activity = require('../models/Activity');

const { createActivity, buildCardFilter, emitUserAction, sendPushNotificationHandler, generateFileHash, getFileType, FILE_EXPIRATION_TIME } = require('../utils/helper');
const { uploadFileToS3, getFileFromS3, deleteFileFromS3, updateFileInS3 } = require('../utils/s3Utils');


const path = require('path');
const fs = require('fs');

const { S3Client, PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3'); // Import S3 client
const { deleteCache } = require('../redis');
const ColorThief = require('colorthief'); // Import the updated package
const { ActionTypes } = require('../sockets/ActionTypes');
const { getSignedUrl } = require('@aws-sdk/cloudfront-signer');

// const { s3 } = require('../server');

const sendNotification = async ({
  initiatorId,
  recipients = [],
  targetType = 'Card',
  targetId,
  actionType,
  message,
  io
}) => {
  // Create notifications for the specified members
  const notifications = await Notification.insertMany(
    recipients.map(memberId => ({
      member: memberId,
      initiator: initiatorId,
      type: actionType,
      targetType: targetType,
      targetId: targetId,
      message: message,
    }))
  );

  // Populate the initiator data for notifications
  const populatedNotifications = await Notification.populate(notifications, {
    path: 'initiator',
    select: 'name username email profilePicture'
  });

  // Emit the notification to each member
  populatedNotifications.forEach(notification => {
    io.to(notification.member.toString()).emit('newNotification', notification);
  });
};

const emitIOCardAction = async (req, boardId, actionType, payload) => {
  const io = req.app.get('socketio');

  const board = await Board.findOne({ shortId: boardId }).populate({
    path: 'members.user',
    select: '_id username',
  });

  if (!board) {
    console.log('Board not found');
    return;
  }

  board.members.forEach(member => {
    emitUserAction(io, member?.user?._id?.toString(), actionType, payload);
  });
};


// Fetch all cards
const getCards = async (req, res) => {
  try {
    // Filter out archived cards
    const cards = await Card.find({ archived: false }).sort({ order: 1 });
    res.status(200).json(cards);
  } catch (error) {
    console.error('Error fetching cards:', error);
    res.status(500).json({ message: error.message });
  }
};

const getArchivedCards = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { page = 1, limit = 20, search = "" } = req.query; // Default page=1, limit=20

    // Convert page and limit to numbers
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // Verify if the board exists
    const board = await Board.findOne({ shortId: boardId });
    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Search filter
    const searchFilter = search
      ? { title: { $regex: `\\b${search.split(" ").join("\\b.*\\b")}\\b`, $options: "i" } }
      : {};

    // Fetch archived cards with pagination
    const archivedCards = await Card.find({ board: boardId, archived: true, ...searchFilter })
      .sort({ updatedAt: -1 }) // Sorting by most recently updated
      .skip(skip)
      .limit(limitNumber).populate([
        { path: 'actionList', select: '_id' },
        { path: 'order' },
        { path: 'shortId' },
        { path: 'type' },
        { path: 'users', select: 'name email username profilePicture online isPremiumMember canSeeOnlineStatus' },
        { path: 'comments.member', select: '_id' },
        // { path: 'checklists.items', select: 'title' },
        { path: 'watchers', select: '_id' },
        { path: 'labels', select: 'text color' },
        { path: 'activities', select: '_id' },
        { path: 'cover', select: 'url coverColor' },
        { path: 'dueDate' },
      ]);

    // Count total archived cards
    const totalArchivedCards = await Card.countDocuments({ board: boardId, archived: true, ...searchFilter });

    res.status(200).json({
      cards: archivedCards,
      hasMore: skip + archivedCards.length < totalArchivedCards, // Check if more pages exist
      cardsCount: totalArchivedCards
    });
  } catch (error) {
    console.error('Error fetching archived cards:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};


// Fetch cards by member ID
const getCardsByMemberId = async (req, res) => {
  const { memberId } = req.params;

  try {
    // Fetch cards where the member is a user
    const cards = await Card.find({ users: memberId })
      .populate('actionList', 'title')
      .populate('board', 'title link')
      .select('title permalink actionListTitle dueDate boardLink boardTitle labels')
      .exec();

    // Transform the labels to include the required fields (if labels are subdocuments)
    const transformedCards = cards.map(card => ({
      ...card.toObject(),
      labels: card.labels.map(label => ({
        text: label.text,
        color: label.color,
      })),
    }));

    res.status(200).json(transformedCards);
  } catch (error) {
    console.error('Error fetching cards by member ID:', error);
    res.status(500).json({ message: error.message });
  }
};

const getCardsByMemberUserName = async (req, res) => {
  const { userName } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10; // Default limit

  try {
    const user = await User.findOne({ username: userName });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const cards = await Card.find({ users: user._id })
      .populate('actionList', 'title')
      .populate('board', 'title link')
      .select('title permalink actionListTitle dueDate boardLink boardTitle labels')
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    const transformedCards = cards.map(card => ({
      ...card.toObject(),
      labels: card.labels.map(label => ({
        text: label.text,
        color: label.color,
      })),
    }));

    res.status(200).json({ cards: transformedCards, hasMore: cards.length === limit });
  } catch (error) {
    console.error('Error fetching cards by username:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};



const getCardsByActionListId = async (req, res) => {
  const { actionListId } = req.params;
  const userId = req.user.id;
  const { skip = 0, limit = 0 } = req.query; // Default skip=0, limit=0 (no limit)

  try {
    // Fetch user filters
    const user = await User.findById(userId).select('filters').lean();
    if (!user) return res.status(404).json({ message: 'User not found' });

    const filters = user.filters || {};
    const cardFilter = { actionList: actionListId, archived: false, ...buildCardFilter(filters) };
    const actionList = await ActionList.findById(actionListId);

    // Count total cards for pagination info
    const totalCards = await Card.countDocuments(cardFilter);

    // Create query with pagination if limit is provided
    let cardsQuery = Card.find(cardFilter).sort({ order: 1 });

    // Apply pagination if limit is provided and greater than 0
    if (limit > 0) {
      cardsQuery = cardsQuery.skip(parseInt(skip)).limit(parseInt(limit));
    }

    // Execute query with population
    const cards = await cardsQuery.populate([
        { path: 'actionList', select: '_id' },
        { path: 'order' },
        { path: 'shortId' },
        { path: 'type' },
        { path: 'users', select: 'name email username profilePicture online isPremiumMember canSeeOnlineStatus' },
        { path: 'comments.member', select: '_id' },
        // { path: 'checklists.items', select: 'title' },
        { path: 'watchers', select: '_id' },
        { path: 'labels', select: 'text color' },
        { path: 'activities', select: '_id' },
        { path: 'cover', select: 'url coverColor' },
        { path: 'dueDate' },
        { path: 'priority' },
      ])
      .select({
        title: 1,
        permalink: 1,
        description: { $substrCP: ['$description', 0, 1] },
        attachments: { $map: { input: '$attachments', as: 'a', in: '$$a._id' } }, // Return only attachment IDs
        checklists: {
          $map: {
            input: '$checklists',
            as: 'checklist',
            in: {
              title: '$$checklist.title',
              items: {
                $map: {
                  input: '$$checklist.items',
                  as: 'item',
                  in: { checked: '$$item.checked' }
                }
              }
            }
          }
        }
      })
      .lean();

    // Refresh expired cover URLs
    const expiredCoverPromises = cards.map((card) => {
      const cover = card.cover;
      if (cover && cover.expiresAt && new Date() >= new Date(cover.expiresAt)) {
        return getFileFromS3(`cards/${card.shortId}/${cover.name}`).then(async (newUrl) => {
          cover.url = newUrl;
          cover.expiresAt = new Date(Date.now() + FILE_EXPIRATION_TIME); // Set expiresAt 1 hour from now

          // Save updated cover to the database
          await Card.findByIdAndUpdate(card._id, {
            'cover.url': newUrl,
            'cover.expiresAt': cover.expiresAt,
          });
        });
      }
    });

    await Promise.all(expiredCoverPromises);

    // Calculate if there are more cards to load
    const hasMore = parseInt(skip) + cards.length < totalCards;

    res.status(200).json({
      cards,
      actionList: actionList.title,
      hasMore,
      totalCards
    });
  } catch (error) {
    console.error('Error fetching cards:', error);
    res.status(500).json({ message: error.message });
  }
};



const getArchiveCards = async (req, res) => {
  try {
    // Filter out archived cards
    const cards = await Card.find({ archived: true }).sort({ order: 1 });
    res.status(200).json(cards);
  } catch (error) {
    console.error('Error fetching cards:', error);
    res.status(500).json({ message: error.message });
  }
};

// Fetch a card by ID
const getCardById = async (req, res) => {
  const { cardId } = req.params;

  try {
    // Find the card by cardId and populate related fields
    const card = await Card.findOne({ shortId: cardId })
      .populate({
        path: 'users',
        select: 'name email username profilePicture online isPremiumMember canSeeOnlineStatus'
      })
      .populate({
        path: 'actionList',
        select: 'title', // Include only the title of the ActionList
      });

    if (!card) {
      console.log('Card not found with cardId:', cardId); // Log if the card is not found
      return res.status(404).json({ message: 'Card not found' });
    }

    res.status(200).json(card);
  } catch (error) {
    console.error('Error fetching card by cardId:', error); // Log any errors
    res.status(500).json({ message: error.message });
  }
};



const createCard = async (req, res) => {
  const { actionListId, title, description, position, users } = req.body;

  try {
    // Check if the action list exists and populate the board field
    const actionList = await ActionList.findById(actionListId).populate('board');
    if (!actionList) {
      return res.status(404).json({ message: 'Action list not found' });
    }

    // Ensure that the board field is populated
    if (!actionList.board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Derive the board ID and other necessary data
    const boardId = actionList.board.shortId;
    const boardTitle = actionList.board.title;
    const boardLink = actionList.board.permalink;
    const actionListTitle = actionList.title;

    let newCardOrder;

    // If position is 'top', add the card at the start
    if (position === 'top') {
      newCardOrder = 0;

      // Increment the order of all existing cards
      await Card.updateMany(
        { actionList: actionListId },
        { $inc: { order: 1 } }
      );
    } else {
      // Default to adding the card at the end
      newCardOrder = actionList.cards.length;
    }

    // Create a new card instance
    const newCard = new Card({
      title,
      description,
      users,
      actionList: actionListId,
      actionListTitle: actionListTitle,
      board: boardId,
      boardTitle: boardTitle,
      boardLink: boardLink,
      order: newCardOrder,
    });

    // Save the new card
    await newCard.save();

    // Update the action list with the new card
    if (position === 'top') {
      actionList.cards.unshift(newCard._id); // Add to the start
    } else {
      actionList.cards.push(newCard._id); // Add to the end
    }
    await actionList.save();

    const cardsCount = await Card.countDocuments({ board: boardId, archived: false });
    await Board.findOneAndUpdate(
      { shortId: boardId },
      { $set: { cardsCount } },
      { new: true }
    );

    console.log({ cardsCount });

    res.status(201).json({ message: 'Card created successfully', card: newCard });
  } catch (error) {
    console.error('Error creating card:', error);
    res.status(500).json({ message: error.message });
  }
};


const copyCard = async (req, res) => {
  const { cardId } = req.params;
  const { isMembers, order, targetListId } = req.body;

  try {
    // Find the original card
    const originalCard = await Card.findOne({ shortId: cardId }).populate('actionList');
    if (!originalCard) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Find the target action list
    const targetActionList = await ActionList.findById(targetListId);
    if (!targetActionList) {
      return res.status(404).json({ message: 'Target list not found' });
    }

    // Create a new card object with the same properties as the original
    const newCard = new Card({
      title: `${originalCard.title} (Copy)`,
      description: originalCard.description,
      users: isMembers ? originalCard.users : [],
      actionList: targetActionList._id,
      actionListTitle: targetActionList.title,
      board: originalCard.board,
      boardTitle: originalCard.boardTitle,
      boardLink: originalCard.boardLink,
      order: order,
      labels: originalCard.labels,
      checklists: originalCard.checklists,
      attachments: originalCard.attachments,
      dueDate: originalCard.dueDate,
      // Don't copy comments, activities, or watchers
    });


    // Save the new card
    await newCard.save();

    // Update the target action list with the new card
    targetActionList.cards.push(newCard._id);
    await targetActionList.save();

    // Invalidate the cache for the board and create the activity concurrently
    const userId = req.user.id;
    const cacheKey = `board:${userId}:${originalCard.board}`;
    await Promise.all([
      deleteCache(cacheKey),
      createActivity(newCard.shortId, req.user.id, 'DUPLICATED_CARD', originalCard.shortId, 'duplicated this card.')
    ]);


    await Board.findOneAndUpdate(
      { shortId: originalCard.board },
      { $set: { cardsCount: await Card.countDocuments({ board: originalCard.board, archived: false }) } },
      { new: true }
    );

    res.status(201).json({ message: 'Card duplicated successfully', card: newCard });
  } catch (error) {
    console.error('Error duplicating card:', error);
    res.status(500).json({ message: error.message });
  }
};

// Delete a card
const deleteCard = async (req, res) => {
  const { cardId } = req.params; // cardId is the shortId

  try {
    // Find the card by shortId to get its ObjectId (_id)
    const card = await Card.findOneAndDelete({ shortId: cardId });

    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Remove the card using its ObjectId (_id) from the action list
    await ActionList.updateOne(
      { cards: card._id }, // use card._id here
      { $pull: { cards: card._id } }
    );

    await Board.findOneAndUpdate(
      { shortId: card.board },
      { $set: { cardsCount: await Card.countDocuments({ board: card.board, archived: false }) } },
      { new: true }
    );

    res.status(200).json({ message: 'Card deleted successfully' });
  } catch (error) {
    console.error('Error deleting card:', error);
    res.status(500).json({ message: error.message });
  }
};

// Update Card
const updateCard = async (req, res) => {
  const { cardId } = req.params;
  const { title, description, mentionedIds } = req.body;
  const reqUser = req.user;

  // Build the update object dynamically based on provided fields
  const updateFields = {};
  if (title !== undefined) updateFields.title = title;
  if (description !== undefined) updateFields.description = description;

  try {
    // Find and update the card asynchronously
    const updatedCardPromise = Card.findOneAndUpdate(
      { shortId: cardId },
      { $set: updateFields }, // Use $set to update only provided fields
      { new: true } // Return the updated document
    ).populate({
      path: 'actionList',
      select: '_id title board',
    });

    // Handle mentions asynchronously
    let notificationPromise = Promise.resolve();
    let pushNotificationPromise = Promise.resolve();

    if (mentionedIds?.length > 0) {
      const initiatorName = reqUser.username || reqUser.name || "Someone";

      notificationPromise = sendNotification({
        recipients: mentionedIds,
        initiatorId: reqUser.id,
        targetType: 'Card',
        targetId: cardId,
        actionType: 'mention',
        message: `${initiatorName} mentioned you in a card update`,
        io: req.app.get('socketio'),
      });

      pushNotificationPromise = Promise.all(
        mentionedIds.map((memberId) =>
          sendPushNotificationHandler({
            memberId,
            type: "MENTION_IN_CARD",
            data: { cardId, username: req.user.username },
            initiator: req.user.name || "Someone",
          })
        )
      );

      // Push Notification for Mentions
      await Promise.all(mentionedIds.map(async (memberId) => {
        await sendPushNotificationHandler({
          memberId,
          type: "MENTION_IN_CARD",
          data: { cardId, username: req.user.username },
          initiator: req.user.name || "Someone",
        });

        await User.findByIdAndUpdate(memberId, { $inc: { newNotificationsCount: 1 } });
      }));
    }

    // Wait for all async tasks in parallel
    const updatedCard = await updatedCardPromise;
    if (!updatedCard) {
      return res.status(404).json({ message: 'Card not found' });
    }

    await Promise.all([notificationPromise, pushNotificationPromise]);

    // Emit WebSocket Event for Card Update
    emitIOCardAction(req, updatedCard.board, ActionTypes.CARD_UPDATED, { boardId: updatedCard.board, card: updatedCard, reqUser });

    res.json(updatedCard); // Return the updated card with populated actionList
  } catch (error) {
    console.error('Error updating card:', error);
    res.status(500).json({ message: error.message });
  }
};



// Archive a card
const archiveCard = async (req, res) => {
  const { cardId } = req.params;
  const { archived } = req.body;
  const reqUser = req.user;

  try {
    // Update the card's archived status
    const updatedCard = await Card.findOneAndUpdate(
      { shortId: cardId },
      { archived: archived },
      { new: true }
    )
      .populate([
        { path: 'actionList', select: '_id' },
        { path: 'order' },
        { path: 'shortId' },
        { path: 'type' },
        { path: 'users', select: 'name email username profilePicture online isPremiumMember canSeeOnlineStatus' },
        { path: 'comments.member', select: '_id' },
        // { path: 'checklists.items', select: 'title' },
        { path: 'watchers', select: '_id' },
        { path: 'labels', select: 'text color' },
        { path: 'activities', select: '_id' },
        { path: 'cover', select: 'url coverColor' },
        { path: 'dueDate' },
      ])
      .select({
        title: 1,
        permalink: 1,
        description: { $substrCP: ['$description', 0, 1] },
        attachments: { $map: { input: '$attachments', as: 'a', in: '$$a._id' } }, // Return only attachment IDs
        checklists: {
          $map: {
            input: '$checklists',
            as: 'checklist',
            in: {
              title: '$$checklist.title',
              items: {
                $map: {
                  input: '$$checklist.items',
                  as: 'item',
                  in: { checked: '$$item.checked' }
                }
              }
            }
          }
        }
      })
      .lean();

    if (!updatedCard) {
      return res.status(404).json({ message: "Card not found" });
    }

    // Fetch action list if needed (for unarchiving)
    let actionList = null;
    if (!archived) {
      actionList = await ActionList.findById(updatedCard.actionList._id);
      if (actionList?.archived) {
        await ActionList.findByIdAndUpdate(actionList._id, { archived: false });
      }
    }

    const initiatorName = reqUser.username || reqUser.name || "Someone";
    const notificationMessage = archived
      ? `${initiatorName} archived this card`
      : `${initiatorName} unarchived this card`;

    // Notify watchers about the archive/unarchive action
    const watchers = updatedCard.watchers.map((w) => w._id.toString());
    const notificationPromise = sendNotification({
      recipients: watchers,
      initiatorId: reqUser.id,
      targetType: "Card",
      targetId: cardId,
      actionType: "card_archived",
      message: notificationMessage,
      io: req.app.get("socketio"),
    });

    // Push notification for watchers
    const pushNotificationPromise = Promise.all(
      watchers.map((memberId) =>
        sendPushNotificationHandler({
          memberId,
          type: archived ? "CARD_ARCHIVED" : "CARD_UNARCHIVED",
          data: { cardId, username: req.user.username },
          initiator: req.user.name || "Someone",
        })
      )
    );

    // Create activity log
    const activityPromise = createActivity(
      cardId,
      reqUser.id,
      "ARCHIVED",
      cardId,
      archived ? "archived this card." : "unarchived this card."
    );

    // Emit event for real-time update
    emitIOCardAction(req, updatedCard.board, ActionTypes.CARD_ARCHIVED, {
      boardId: updatedCard.board,
      reqUser,
    });

    // Run notifications and activity logging in parallel
    await Promise.all([notificationPromise, pushNotificationPromise, activityPromise]);

    await Board.findOneAndUpdate(
      { shortId: updatedCard.board },
      { $set: { cardsCount: await Card.countDocuments({ board: updatedCard.board, archived: false }) } },
      { new: true }
    );

    res.status(200).json({
      message: "Card archive status updated successfully",
      updatedCard,
      actionList,
    });
  } catch (error) {
    console.error("Error archiving card:", error);
    res.status(500).json({ message: error.message });
  }
};



const getCardMembers = async (req, res) => {
  const { cardId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId }).populate({
      path: 'users',
      select: '-passwordHash'  // Exclude the passwordHash field
    });

    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    const members = card.users;
    res.status(200).json(members);
  } catch (error) {
    res.status(500).json({ message: error.message, error });
  }
};


// Add member to card
const addMemberToCard = async (req, res) => {
  const { cardId } = req.params;
  const loggedInUser = req.user.id;
  const { memberId } = req.body;
  const reqUser = req.user.id;

  try {
    // Fetch card, action list, and board in a single query with lean()
    const card = await Card.findOne({ shortId: cardId })
      .populate({
        path: 'actionList',
        populate: {
          path: 'board',
          select: 'members', // Only fetch the members field from the board
        },
      })
      .lean();

    // Early exits for missing entities
    if (!card) return res.status(404).json({ message: 'Card not found' });
    if (!card.actionList) return res.status(404).json({ message: 'Action list not found' });
    if (!card.actionList.board) return res.status(404).json({ message: 'Board not found' });

    // Check if the member exists and is part of the board
    const user = await User.findById(memberId).lean();
    if (!user) return res.status(404).json({ message: 'User not found' });

    let isBoardMember = card.actionList.board.members.some(member => member.user?.equals(user._id));

    // If not a board member, add them to the board first
    if (!isBoardMember) {
      await Board.updateOne(
        { shortId: card.board },
        { $push: { members: { user: user._id, role: 'member' } } } // Default role: 'member'
      );
      isBoardMember = true;
    }

    // Check if user is already a card member
    if (card.users.some(member => member.equals(user._id))) {
      return res.status(400).json({ message: 'User is already a member of the card' });
    }

    // Add user to card members and save
    card.users.push(user._id);
    await Card.updateOne({ _id: card._id }, { users: card.users });

    // Invalidate cache for the board
    const cacheKey = `board:${req.user.id}:${card.board}`;
    await deleteCache(cacheKey); // Ensure this function is defined in your redis.js

    // Determine the action type and details
    const actionType = loggedInUser === user._id.toString() ? 'JOINED' : 'ADDED_MEMBER';
    const details = loggedInUser === user._id.toString()
      ? 'joined this card.'
      : `added ${user.name} to this card.`;

    // **Send Response Immediately**
    res.status(200).json({ message: 'Member added to card' });

    // **Execute Notifications Asynchronously**
    (async () => {
      try {
        if (req.user.id !== memberId) {
          await Promise.all([
            sendNotification({
              recipients: [memberId],
              initiatorId: req.user.id,
              targetType: 'Card',
              targetId: cardId,
              actionType: actionType,
              message: "added you to this",
              io: req.app.get('socketio'),
            }),

            sendPushNotificationHandler({
              memberId,
              type: "ADD_MEMBER_TO_CARD",
              data: { cardId, username: req.user.username },
              initiator: req.user.name || "Someone",
            }),

            User.findByIdAndUpdate(memberId, { $inc: { newNotificationsCount: 1 } }),

            createActivity(cardId, loggedInUser, actionType, user._id, details)
          ]);
        }

        emitIOCardAction(req, card.board, ActionTypes.MEMBER_ADDED, {
          boardId: card.board, card, reqUser: loggedInUser
        });

      } catch (error) {
        console.error('Async notification error:', error);
      }
    })();

  } catch (error) {
    console.error('Error adding member to card:', error);
    res.status(500).json({ message: error.message });
  }
};


const removeMemberFromCard = async (req, res) => {
  const { cardId } = req.params;
  const { memberId } = req.body;
  const loggedInUser = req.user.id;
  const reqUser = req.user.id;

  try {
    // Fetch card, action list, board, and user in a single query
    const [card, user] = await Promise.all([
      Card.findOne({ shortId: cardId })
        .populate({
          path: 'actionList',
          populate: {
            path: 'board',
            select: 'members', // Only fetch the members field from the board
          },
        }),
      User.findById(memberId)
    ]);

    // Early exits for missing entities
    if (!card) return res.status(404).json({ message: 'Card not found' });
    if (!card.actionList) return res.status(404).json({ message: 'Action list not found' });
    if (!card.actionList.board) return res.status(404).json({ message: 'Board not found' });
    if (!user) return res.status(404).json({ message: 'User not found' });

    const isBoardMember = card.actionList.board.members.some(member => member.user?.equals(user._id));
    if (!isBoardMember) return res.status(400).json({ message: 'User is not a member of the board' });

    // Check if the user is part of the card members
    if (!card.users.some(member => member.equals(user._id))) {
      return res.status(400).json({ message: 'User is not a member of the card' });
    }

    // Remove user from card members
    const updatedCard = await Card.findOneAndUpdate(
      { shortId: cardId },
      { $pull: { users: user._id } },
      { new: true, runValidators: true }
    );

    // Determine the action type and details based on the logged-in user
    const actionType = loggedInUser === user._id.toString() ? 'LEFT' : 'REMOVED_MEMBER';
    const details = loggedInUser === user._id.toString()
      ? 'left this card.'
      : `removed ${user.name} from this card.`;

    // **Send Response Immediately**
    res.status(200).json({ message: 'Member removed from card' });

    // **Execute Notifications Asynchronously**
    (async () => {
      try {
        if (req.user.id !== memberId) {
          await Promise.all([
            sendNotification({
              recipients: [memberId],
              initiatorId: req.user.id,
              targetType: 'Card',
              targetId: cardId,
              actionType: actionType,
              message: "removed you from this",
              io: req.app.get('socketio')
            }),

            sendPushNotificationHandler({
              memberId,
              type: "REMOVE_MEMBER_FROM_CARD",
              data: { cardId, username: req.user.username },
              initiator: req.user.name || "Someone",
            }),

            User.findByIdAndUpdate(memberId, { $inc: { newNotificationsCount: 1 } }),

            createActivity(cardId, loggedInUser, actionType, user._id, details)
          ]);
        }

        emitIOCardAction(req, updatedCard.board, ActionTypes.MEMBER_REMOVED, {
          boardId: updatedCard.board, card: updatedCard, reqUser: loggedInUser
        });

      } catch (error) {
        console.error('Async notification error:', error);
      }
    })();

  } catch (error) {
    console.error('Error removing member from card:', error);
    res.status(500).json({ message: error.message });
  }
};



const addCommentToCard = async (req, res) => {
  const { cardId } = req.params;
  const { comment, member, reacts, mentionedIds } = req.body;
  const reqUser = req.user;

  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).json({ message: "Card not found" });
    }

    const newComment = { comment, member, reacts };
    card.comments.push(newComment);
    await card.save();

    const addedComment = card.comments[card.comments.length - 1];
    const commentId = addedComment._id;

    await card.populate("comments.member", "name username email profilePicture bio");

    const cardWatchers = card.watchers.map((watcher) => watcher.toString());
    const mentionedIdsSet = new Set(mentionedIds.map((id) => id.toString()));
    const nonMentionedWatchers = cardWatchers.filter((id) => !mentionedIdsSet.has(id));

    // Send Response Quickly ✅
    res.status(201).json({ message: "Comment added to card", commentId });



    // Run Notifications in the Background 🔥
    (async () => {
      // Site Notification for Mentions
      if (mentionedIds?.length > 0) {
        sendNotification({
          recipients: mentionedIds,
          initiatorId: reqUser.id,
          targetType: "Card",
          targetId: cardId,
          actionType: "mention",
          message: "You've been mentioned in a comment",
          io: req.app.get("socketio"),
        });

        // Push Notification for Mentions
        await Promise.all(mentionedIds.map(async (memberId) => {
          await sendPushNotificationHandler({
            memberId,
            type: "MENTION_IN_COMMENT",
            data: { cardId, username: req.user.username },
            initiator: req.user.name || "Someone",
          });

          await User.findByIdAndUpdate(memberId, { $inc: { newNotificationsCount: 1 } });
        }));
      }

      // Site Notification for Watchers
      if (nonMentionedWatchers.length > 0) {
        sendNotification({
          recipients: nonMentionedWatchers,
          initiatorId: reqUser.id,
          targetType: "Card",
          targetId: cardId,
          actionType: "comment",
          message: `New comment on card: ${newComment.comment}`,
          io: req.app.get("socketio"),
        });

        // Push Notification for Watchers
        nonMentionedWatchers.forEach((memberId) => {
          sendPushNotificationHandler({
            memberId,
            type: "COMMENT_ON_CARD",
            data: { cardId, username: req.user.username },
            initiator: req.user.name || "Someone",
          });
        });
      }

      // Log Activity
      createActivity(cardId, reqUser.id, "ADDED_COMMENT", commentId, newComment.comment);
      emitIOCardAction(req, card.board, ActionTypes.COMMENT_ADDED, { boardId: card.board, reqUser });
    })();
  } catch (error) {
    console.error("Error adding comment:", error);
    res.status(500).json({ message: error.message });
  }
};



// Fetch comments for a card
const getCardComments = async (req, res) => {
  const { cardId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId }).populate({
      path: 'comments.member',
      select: 'name username email profilePicture bio'
    });
    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Sort comments in descending order by time
    card.comments.sort((a, b) => b.time - a.time);

    res.status(200).json(card.comments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ message: error.message });
  }
};


// Update a comment on a card
const updateComment = async (req, res) => {
  const { cardId, commentId } = req.params;
  const { comment, activityId, initiatorId, mentionedIds } = req.body;
  const userId = req.user.id;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Find the comment by its ID
    const existingComment = card.comments.find(c => c._id.toString() === commentId);
    if (!existingComment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Update the comment content
    existingComment.comment = comment;

    // Update related activity
    if (activityId && (userId === initiatorId)) {
      const activity = await Activity.findById(activityId);
      if (activity && activity.card.toString() === cardId) {
        activity.details = comment;
        await activity.save();
      }
    }

    await card.save();

    // Populate the member details
    await card.populate('comments.member', 'name username email profilePicture bio');

    // If mentionedIds are provided, send notifications
    if (mentionedIds && mentionedIds.length > 0) {

      try {
        await sendNotification({
          recipients: mentionedIds,
          initiatorId: req.user.id,
          targetType: 'Card',
          targetId: cardId,
          actionType: 'mention',
          message: "You have been mentioned in an updated comment.",
          io: req.app.get('socketio'), // Pass the Socket.IO instance
        });

        // Push Notification for Mentions
        await Promise.all(mentionedIds.map(async (memberId) => {
          await sendPushNotificationHandler({
            memberId,
            type: "MENTION_IN_COMMENT",
            data: { cardId, username: req.user.username },
            initiator: req.user.name || "Someone",
          });


          await User.findByIdAndUpdate(memberId, { $inc: { newNotificationsCount: 1 } });
        }));



      } catch (error) {
        console.error(`Notification attempt failed:`, error);
      }
    }


    emitIOCardAction(req, card.board, ActionTypes.COMMENT_UPDATED, { boardId: card.board, reqUser });


    res.status(200).json({ message: 'Comment updated successfully.' });
  } catch (error) {
    console.error('Error updating comment:', error);
    res.status(500).json({ message: error.message });
  }
};


// Delete a comment from a card
const deleteComment = async (req, res) => {
  const { cardId, commentId } = req.params;
  const reqUser = req.user.id;
  const { activityId } = req.body;

  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Find the index of the comment to delete
    const commentIndex = card.comments.findIndex(c => c._id.toString() === commentId);
    if (commentIndex === -1) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Remove the comment from the array
    card.comments.splice(commentIndex, 1);
    await card.save();

    // Remove the associated activity using activityId
    await Activity.findByIdAndDelete(activityId);

    // Populate the member details
    await card.populate('comments.member', 'name username email profilePicture bio');

    // const io = req.app.get('socketio');

    // const board = await Board.findOne({ shortId: card.board }).populate({
    //   path: 'members.user', // Populate the 'user' field inside 'members'
    //   select: '_id username', // Select only necessary fields
    // });

    // if (!board) {
    //   console.log('Board not found');
    //   return;
    // }

    // board.members.forEach(member => {
    //   emitUserAction(io, member?.user?._id?.toString(), ActionTypes.COMMENT_DELETED, { boardId: card.board });
    // });

    emitIOCardAction(req, card.board, ActionTypes.COMMENT_DELETED, { boardId: card.board, reqUser });


    res.status(200).json(card);
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({ message: error.message });
  }
};

const addChecklistToCard = async (req, res) => {
  const { cardId } = req.params;
  const { title } = req.body;
  const reqUser = req.user.id;



  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) return res.status(404).json({ message: 'Card not found' });

    const newChecklist = { title, checklists: [] };
    card.checklists.push(newChecklist);
    await card.save();

    // Log the activity
    await createActivity(cardId, req.user.id, 'ADDED_CHECKLIST', newChecklist._id, 'added a new checklist');


    emitIOCardAction(req, card.board, ActionTypes.CHECKLIST_ADDED, { boardId: card.board, reqUser });


    res.status(201).json({ checklist: card.checklists[card.checklists.length - 1] });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
const getChecklistsFromCard = async (req, res) => {
  const { cardId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    res.status(200).json({ checklists: card.checklists });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching checklist items', error });
  }
};

const updateChecklist = async (req, res) => {
  const { cardId, checklistId } = req.params;
  const { title } = req.body;

  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) return res.status(404).json({ message: 'Card not found' });

    const checklist = card.checklists.id(checklistId);
    if (!checklist) return res.status(404).json({ message: 'Checklist not found' });

    checklist.title = title;
    await card.save();

    // const io = req.app.get('socketio');

    // const board = await Board.findOne({ shortId: updatedCard.board }).populate({
    //   path: 'members.user', // Populate the 'user' field inside 'members'
    //   select: '_id username', // Select only necessary fields
    // });

    // if (!board) {
    //   console.log('Board not found');
    //   return;
    // }

    // board.members.forEach(member => {
    //   emitUserAction(io, member?.user?._id?.toString(), ActionTypes.CHECKLIST_UPDATED, { boardId: card.board });
    // });

    emitIOCardAction(req, card.board, ActionTypes.CHECKLIST_UPDATED, { boardId: card.board, reqUser });


    res.status(200).json('Checklist update successfully.');
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteChecklist = async (req, res) => {
  const { cardId, checklistId } = req.params;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) return res.status(404).json({ message: 'Card not found' });

    card.checklists.pull({ _id: checklistId });
    await card.save();

    // const io = req.app.get('socketio');

    // const board = await Board.findOne({ shortId: updatedCard.board }).populate({
    //   path: 'members.user', // Populate the 'user' field inside 'members'
    //   select: '_id username', // Select only necessary fields
    // });

    // if (!board) {
    //   console.log('Board not found');
    //   return;
    // }

    // board.members.forEach(member => {
    //   emitUserAction(io, member?.user?._id?.toString(), ActionTypes.CHECKLIST_DELETED, { boardId: card.board });
    // });

    emitIOCardAction(req, card.board, ActionTypes.CHECKLIST_DELETED, { boardId: card.board, reqUser });

    res.status(200).json(card);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


const addChecklistItem = async (req, res) => {
  const { cardId, checklistId } = req.params;
  const { title } = req.body;
  const reqUser = req.user.id;

  try {
    // Atomic update: Push item in one operation
    const updatedCard = await Card.findOneAndUpdate(
      { shortId: cardId, "checklists._id": checklistId }, // Find card with checklist
      {
        $push: { "checklists.$.items": { title, checked: false } } // Add new item
      },
      { new: true, runValidators: true } // Return updated card
    );

    if (!updatedCard) return res.status(404).json({ message: "Card or checklist not found" });

    // Emit event after successful update
    emitIOCardAction(req, updatedCard.board, ActionTypes.CHECKLIST_ITEM_ADDED, {
      boardId: updatedCard.board, reqUser
    });

    res.status(201).json(updatedCard);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


const updateChecklistItem = async (req, res) => {
  const { cardId, checklistId, itemId } = req.params;
  const { title, checked } = req.body;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) return res.status(404).json({ message: 'Card not found' });

    const checklist = card.checklists.id(checklistId);
    if (!checklist) return res.status(404).json({ message: 'Checklist not found' });

    const item = checklist.items.id(itemId);
    if (!item) return res.status(404).json({ message: 'Checklist item not found' });

    item.title = title;
    item.checked = checked;
    await card.save();

    if (checked) {
      // Create activity when marked
      await createActivity(
        cardId,
        req.user.id,
        'MARKED_CHECK_ITEM',
        item._id,
        `completed <b>${title}</b> on this card.`,
      );
    } else {
      const query = {
        shortId: cardId,
        initiator: req.user.id,
        actionType: 'MARKED_CHECK_ITEM',
        details: `completed <b>${title}</b> on this card.`
      };


      const activity = await Activity.findOne(query);

      if (activity) {
        await Activity.deleteOne(query);
        console.log('Activity deleted');
      } else {
        console.log('No matching activity found');
      }
    }

    emitIOCardAction(req, card.board, ActionTypes.CHECKLIST_ITEM_UPDATED, { boardId: card.board, reqUser });

    res.status(200).json(card);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteChecklistItem = async (req, res) => {
  const { cardId, checklistId, itemId } = req.params;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) return res.status(404).json({ message: 'Card not found' });

    const checklist = card.checklists.id(checklistId);
    if (!checklist) return res.status(404).json({ message: 'Checklist not found' });

    const item = checklist.items.id(itemId);
    if (!item) return res.status(404).json({ message: 'Checklist item not found' });

    checklist.items.pull({ _id: itemId });
    await card.save();

    emitIOCardAction(req, card.board, ActionTypes.CHECKLIST_ITEM_DELETED, { boardId: card.board, reqUser });

    res.status(200).json(card);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const uploadAttachment = async (req, res) => {
  const { cardId } = req.params;
  const userId = req.user.id;
  const file = req.file;
  const type = req.body.type;
  const reqUser = req.user.id;

  if (!file) {
    return res.status(400).send({ error: 'No file uploaded' });
  }

  const hash = generateFileHash(file.buffer);

  try {
    // Upload file to S3 and get the base file URL (not signed)
    const fileUrl = await uploadFileToS3({
      file,
      folderName: `cards/${cardId}/${hash}`,
      isSharp: false,
      width: 600,
      fit: 'cover',
      req
    });

    // const signedFileUrl = await getFileFromS3(`cards/${cardId}/${file.originalname}`);

    const signedFileUrl = getSignedUrl({
      url: `https://cloud.zoobbe.com/cards/${cardId}/${hash}/${file.originalname}`,
      dateLessThan: new Date(Date.now() + FILE_EXPIRATION_TIME),
      privateKey: process.env.CLOUDFRONT_PRIVATE_KEY.split(String.raw`\n`).join('\n'),
      keyPairId: process.env.CLOUDFRONT_KEY_PAIR_ID,
    });

    const expiresAt = new Date(Date.now() + FILE_EXPIRATION_TIME); // Set expiration 1 hour from now

    // Create attachment object with both url and signedUrl, and expiration time
    const fileType = getFileType(file.mimetype);

    const attachment = {
      name: file.originalname,
      url: signedFileUrl, // Base URL (publicly accessible)
      signedUrl: signedFileUrl, // Signed URL (temporary access)
      fileHash: hash,
      fileType, // Add file type
      expiresAt, // Expiration time for the signed URL
      cardId,
    };


    // Find the card by its ID
    const card = await Card.findOne({ shortId: cardId });

    if (!card) {
      return res.status(404).send({ error: 'Card not found' });
    }

    // Add the attachment to the card's attachments array
    card.attachments.push(attachment);

    // Save the card after adding the attachment
    await card.save(); // Ensure the card is saved with the new attachment

    // Set the cover image and extract colors if it's an image
    if (file.mimetype.startsWith('image/') && (!card.cover.url || type == 'COVER')) {
      const palette = await ColorThief.getPalette(file.buffer, 5);

      // Now update the cover with attachment details
      card.cover = {
        name: file.originalname,
        url: signedFileUrl, // Use signed URL for the cover image
        expiresAt, // Set expiration for the cover image
        cardId,
        coverColor: palette.map(color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`),
        attachmentId: card.attachments[card.attachments.length - 1]._id // Use the last attachment ID
      };

      await card.save(); // Save again to update the cover with the new attachment ID
    }

    // Create activity log for attachment
    await createActivity(
      cardId,
      userId,
      'ATTACHED',
      attachment._id,
      `attached <a href="${attachment.signedUrl}" target="_blank">${attachment.name}</a> to this card.`
    );

    emitIOCardAction(req, card.board, ActionTypes.ATTACHMENT_ADDED, { boardId: card.board, reqUser });

    // Send response with updated attachments
    res.status(200).send(card.attachments);

  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};



const getAttachments = async (req, res) => {
  try {
    // Find all cards and select only the attachments field
    const cards = await Card.find({}, 'attachments');

    // Reduce to combine all attachments into a single array
    const attachments = cards.reduce((acc, card) => acc.concat(card.attachments), []);

    res.status(200).send({ attachments });
  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};


const getAttachmentsByCardId = async (req, res) => {
  const { cardId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId });

    if (!card) {
      return res.status(404).send({ error: 'Card not found' });
    }

    const now = new Date();

    // Loop through attachments and get signed URLs only if expired
    const signedAttachments = await Promise.all(
      card.attachments.map(async (attachment) => {
        let { signedUrl, expiresAt } = attachment;

        // If signed URL is missing or expired, generate a new one
        if (!signedUrl || !expiresAt || Date.parse(expiresAt) < Date.now()) {
          const newExpiresAt = new Date(Date.now() + FILE_EXPIRATION_TIME); // 24 hours

          signedUrl = getSignedUrl({
            url: `${process.env.CLOUD_CDN_URL}/cards/${cardId}/${attachment.fileHash}/${attachment.name}`,
            dateLessThan: newExpiresAt,
            privateKey: process.env.CLOUDFRONT_PRIVATE_KEY.split(String.raw`\n`).join('\n'),
            keyPairId: process.env.CLOUDFRONT_KEY_PAIR_ID,
          });

          // Update the database with the new signed URL and expiration
          attachment.signedUrl = signedUrl;
          attachment.expiresAt = newExpiresAt;

          console.log('expiresAt: ' + expiresAt);

        }

        console.log(Date.parse(expiresAt) < Date.now(), Date.parse(expiresAt), Date.now());

        return {
          ...attachment._doc,
          url: signedUrl, // Return the signed URL
        };
      })
    );

    // Save changes if any URLs were updated
    await card.save();

    res.status(200).send({ attachments: signedAttachments });
  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};



const updateAttachment = async (req, res) => {
  const { cardId } = req.params;
  const { attachmentId, newName } = req.body;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });

    if (!card) {
      return res.status(404).send({ error: 'Card not found' });
    }

    const attachment = card.attachments.id(attachmentId);

    if (!attachment) {
      return res.status(404).send({ error: 'Attachment not found' });
    }

    const oldFileKey = `cards/${cardId}/${attachment.name}`; // The old key (S3 path) of the file in S3

    if (newName) {
      attachment.name = newName;

      // Construct the new file key based on the new name
      const newFileKey = `cards/${cardId}/${newName}`;

      // Rename the file in S3
      await updateFileInS3(oldFileKey, newFileKey);

      // Update the S3 key in the attachment metadata
      const signedUrl = await getFileFromS3(newFileKey)

      attachment.url = signedUrl;
    }

    await card.save();

    emitIOCardAction(req, card.board, ActionTypes.ATTACHMENT_UPDATED, { boardId: card.board, reqUser });

    res.status(200).send({ attachment });
  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};


const getAttachment = async (req, res) => {
  const { cardId, attachmentId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId });

    if (!card) {
      return res.status(404).send({ error: 'Card not found' });
    }

    const attachment = card.attachments.id(attachmentId);

    if (!attachment) {
      return res.status(404).send({ error: 'Attachment not found' });
    }

    const now = new Date();

    // If the signed URL is still valid, return it
    if (attachment.signedUrl && attachment.expiresAt && attachment.expiresAt > now) {
      return res.status(200).send({
        attachment: {
          id: attachment._id,
          name: attachment.name,
          url: attachment.signedUrl,
        }
      });
    }

    // Generate a signed URL to access the attachment from S3
    const fileKey = `cards/${cardId}/${attachment.fileHash}/${attachment.name}`;
    const signedUrl = await getFileFromS3(fileKey);

    // Return the attachment info, including the signed URL
    res.status(200).send({
      attachment: {
        id: attachment._id,
        name: attachment.name,
        url: signedUrl
      }
    });

  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};

const getStaticAttachment = async (req, res) => {
  const { cardId, attachmentId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).send({ error: "Card not found" });
    }

    const attachment = card.attachments.id(attachmentId);
    if (!attachment) {
      return res.status(404).send({ error: "Attachment not found" });
    }

    const now = new Date();

    // If the signed URL is valid, return it with cache headers
    if (attachment.signedUrl && attachment.expiresAt && attachment.expiresAt > now) {
      res.set("Cache-Control", "public, max-age=86400, immutable"); // Cache for 1 day
      return res.redirect(attachment.signedUrl);
    }

    // Generate a new signed URL
    const fileKey = `cards/${cardId}/${attachment.fileHash}/${attachment.name}`;
    const signedUrl = await getFileFromS3(fileKey);

    // Update attachment with the new signed URL and expiration
    attachment.signedUrl = signedUrl;
    attachment.expiresAt = new Date(Date.now()) + FILE_EXPIRATION_TIME;
    await card.save();

    res.set("Cache-Control", "public, max-age=86400, immutable"); // Cache for 1 day
    return res.redirect(signedUrl);

  } catch (error) {
    return res.status(500).send({ error: error.message });
  }
};



const deleteAttachment = async (req, res) => {
  const { cardId } = req.params;
  const { attachmentId } = req.body;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });

    if (!card) {
      return res.status(404).send({ error: 'Card not found' });
    }

    const attachment = card.attachments.id(attachmentId);

    if (!attachment) {
      return res.status(404).send({ error: 'Attachment not found' });
    }

    if (card.cover && card.cover.attachmentId === attachmentId) {
      card.cover = {}; // or set it to null if you prefer
    }

    const fileKey = `cards/${cardId}/${attachment.name}`;

    card.attachments.pull({ _id: attachmentId });

    await card.save();

    await deleteFileFromS3(fileKey);

    emitIOCardAction(req, card.board, ActionTypes.ATTACHMENT_DELETED, { boardId: card.board, reqUser });


    res.status(200).send({ message: 'Attachment deleted successfully from S3' });
  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};



const getLabelsFromCard = async (req, res) => {
  const { cardId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId })
      .populate({
        path: 'labels',
        select: 'text color', // Select the fields you want
      });

    if (!card) {
      return res.status(404).send({ error: 'Card not found' });
    }

    const enabledLabels = card.labels;

    res.status(200).send({ labels: enabledLabels });
  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};


const addLabelToCard = async (req, res) => {
  try {
    const { cardId, labelId } = req.params;
    const { boardId } = req.body;
    const reqUser = req.user.id;


    const board = await Board.findOne({ shortId: boardId });

    const card = await Card.findOne({ shortId: cardId });

    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    const label = board.labels.id(labelId);

    if (!label) {
      return res.status(404).json({ message: 'Label not found' });
    }

    // Add the label to the card's labels array
    card.labels.push({
      _id: label._id,
      text: label.text,
      color: label.color,
      enabled: label.enabled
    });

    // Save the updated card
    await card.save();

    emitIOCardAction(req, card.board, ActionTypes.LABEL_ADDED, { boardId: card.board, reqUser });

    res.status(200).json('Label added successfully');
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


const deleteLabelFromCard = async (req, res) => {
  try {
    const { cardId, labelId } = req.params;
    const reqUser = req.user.id;


    const updatedCard = await Card.findOneAndUpdate(
      { shortId: cardId },
      { $pull: { labels: { _id: labelId } } }, // Pull the label by its _id
      { new: true }
    );


    if (!updatedCard) {
      return res.status(404).json({ message: 'Card not found' });
    }

    emitIOCardAction(req, updatedCard.board, ActionTypes.LABEL_DELETED, { boardId: updatedCard.board, reqUser });

    res.status(200).json(updatedCard); // Return the updated card
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};



const addDueDate = async (req, res) => {
  const { cardId } = req.params;
  const { date, startDate, dueTime, reminder, completed } = req.body;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).send('Card not found');
    }

    card.dueDate = {
      date,
      startDate,
      dueTime,
      reminder,
      completed
    };

    await card.save();

    emitIOCardAction(req, card.board, ActionTypes.DUEDATE_ADDED, { boardId: card.board, reqUser });

    res.status(201).json(card);
  } catch (err) {
    res.status(500).send(err.message);
  }
};

const getDueDate = async (req, res) => {
  const { cardId } = req.params;

  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).send('Card not found');
    }

    res.status(200).json(card.dueDate);
  } catch (err) {
    res.status(500).send(err.message);
  }
};


const updateDueDate = async (req, res) => {
  const { cardId } = req.params;
  const { date, startDate, dueTime, reminder, status } = req.body;
  const reqUser = req.user.id;


  try {
    const card = await Card.findOne({ shortId: cardId });
    if (!card) {
      return res.status(404).send('Card not found');
    }

    // Only update the fields that are provided in the request
    if (date) card.dueDate.date = date;
    if (startDate) card.dueDate.startDate = startDate;
    if (dueTime) card.dueDate.dueTime = dueTime;
    if (reminder) card.dueDate.reminder = reminder;
    if (status) card.dueDate.status = status;

    await card.save();

    emitIOCardAction(req, card.board, ActionTypes.DUEDATE_UPDATED, { boardId: card.board, reqUser });

    res.status(200).json(card);
  } catch (err) {
    res.status(500).send(err.message);
  }
};

const removeDueDate = async (req, res) => {
  const { cardId } = req.params;
  const reqUser = req.user.id;

  try {
    const updatedCard = await Card.findOneAndUpdate(
      { shortId: cardId },
      { $unset: { dueDate: "" } }, // Use $unset to remove the dueDate field
      { new: true }
    );

    if (!updatedCard) {
      return res.status(404).send('Card not found');
    }

    emitIOCardAction(req, updateCard.board, ActionTypes.DUEDATE_REMOVED, { boardId: updateCard.board, reqUser });

    res.status(200).json(updatedCard);
  } catch (err) {
    res.status(500).send(err.message);
  }
};


// Get activities of a card
const getActivities = async (req, res) => {
  const { cardId } = req.params;
  const { isActivityDetails } = req.query; // Get from query parameters

  try {
    // Find the card by shortId
    const card = await Card.findOne({ shortId: cardId }).populate({
      path: 'activities',
      populate: {
        path: 'initiator',
        select: 'name profilePicture'
      },
      options: {
        sort: { createdAt: -1 }
      }
    });

    // If card not found, return 404
    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    if (isActivityDetails !== undefined) {
      card.isActivityDetails = isActivityDetails === 'true'; // Convert string to boolean
      await card.save();
    }

    let activities;
    if (card.isActivityDetails) {
      activities = card.activities;
    } else {
      activities = card.activities.filter(activity => activity.actionType === 'ADDED_COMMENT');
    }

    // Respond with the activities
    res.status(200).json(activities);
  } catch (error) {
    console.error('Error fetching activities:', error);
    res.status(500).json({ message: error.message });
  }
};

// Update priority of a card
const addPriority = async (req, res) => {
  try {
    const { cardId } = req.params;
    const { priority } = req.body;
    const reqUser = req.user.id;

    // Validate priority
    const validPriorities = ['Low', 'Normal', 'High', 'Urgent'];
    if (!validPriorities.includes(priority)) {
      return res.status(400).json({ message: 'Invalid priority value' });
    }

    const card = await Card.findOneAndUpdate(
      { shortId: cardId },
      { priority },
      { new: true }
    );

    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    // Emit socket event for real-time updates
    emitIOCardAction(req, card.board, ActionTypes.PRIORITY_UPDATED, {
      cardId,
      priority,
      boardId: card.board,
      reqUser
    });

    res.status(200).json({ message: 'Priority updated successfully', card });
  } catch (error) {
    res.status(500).json({ message: 'Internal server error', error });
  }
};

// Get priority of a card
const getPriority = async (req, res) => {
  try {
    const { cardId } = req.params;
    const card = await Card.findOne({ shortId: cardId });

    if (!card) {
      return res.status(404).json({ message: 'Card not found' });
    }

    res.status(200).json({ priority: card.priority });
  } catch (error) {
    res.status(500).json({ message: 'Internal server error', error });
  }
};



module.exports = {
  getCards,
  getArchivedCards,
  getCardsByMemberId,
  getCardsByMemberUserName,
  getArchiveCards,
  getCardsByActionListId,
  getCardById,
  createCard,
  copyCard,
  deleteCard,
  updateCard,
  archiveCard,
  addMemberToCard,
  removeMemberFromCard,
  getCardMembers,
  addCommentToCard,
  getCardComments,
  updateComment,
  deleteComment,
  addChecklistToCard,
  getChecklistsFromCard,
  updateChecklist,
  deleteChecklist,
  addChecklistItem,
  updateChecklistItem,
  deleteChecklistItem,
  getAttachments,
  getAttachmentsByCardId,
  uploadAttachment,
  getAttachment,
  getStaticAttachment,
  updateAttachment,
  deleteAttachment,
  getLabelsFromCard,
  addLabelToCard,
  deleteLabelFromCard,
  addDueDate,
  getDueDate,
  updateDueDate,
  removeDueDate,
  getActivities,
  addPriority,
  getPriority
};
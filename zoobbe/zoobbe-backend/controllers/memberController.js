const Workspace = require('../models/Workspace');
const Board = require('../models/Board');
const Card = require('../models/Card');

const getMembers = async (req, res) => {
    const { type, id } = req.params;

    try {
        let members;
        switch (type) {
            case 'workspace':
                const workspace = await Workspace
                    .findOne({ shortId: id })
                    .populate('members.user', 'name email username profilePicture bio online isPremiumMember canSeeOnlineStatus');

                if (workspace) {
                    const boards = await Board.find({ workspace: workspace._id }, 'members');

                    members = workspace.members
                        .filter(member => member.user) // Exclude members with null `user`
                        .map(member => {
                            const boardCount = boards.filter(board => {
                                return (
                                    board.members.some(boardMember =>
                                        boardMember.user.toString() === member.user._id.toString()
                                    )
                                )
                            }

                            ).length;

                            return {
                                ...member.user.toObject(),
                                role: member.role,
                                boardCount
                            };
                        });
                } else {
                    members = [];
                }
                break;

            case 'guests':
                const guestsWorkspace = await Workspace
                    .findOne({ shortId: id })
                    .populate('guests', 'name email username profilePicture bio online isPremiumMember canSeeOnlineStatus');

                if (guestsWorkspace) {
                    const boards = await Board.find({ workspace: guestsWorkspace._id }, 'members');

                    members = guestsWorkspace.guests
                        .filter(guest => guest) // Exclude null guests
                        .map(guest => {
                            const boardCount = boards.filter(board =>
                                board.members.some(boardMember =>
                                    boardMember.user.toString() === guest._id.toString()
                                )
                            ).length;

                            return {
                                ...guest.toObject(),
                                role: 'guest',
                                boardCount
                            };
                        });
                } else {
                    members = [];
                }
                break;

            case 'board':
                const board = await Board
                    .findOne({ shortId: id })
                    .populate('members.user', 'name email username profilePicture bio online isPremiumMember canSeeOnlineStatus');

                members = board
                    ? board.members
                        .filter(member => member.user) // Exclude members with null `user`
                        .map(member => ({
                            ...member.user.toObject(),
                            role: member.role,
                            workspaceRole: member.workspaceRole,
                            status: member.status
                        }))
                    : [];
                break;

            case 'card':
                const card = await Card
                    .findOne({ shortId: id })
                    .populate('users', 'name email username profilePicture bio online isPremiumMember canSeeOnlineStatus');

                members = card
                    ? card.users.filter(user => user) // Exclude null users
                    : [];
                break;

            default:
                return res.status(400).json({ message: 'Invalid type' });
        }

        res.status(200).json(members);
    } catch (error) {
        console.error('Error fetching members:', error);
        res.status(500).json({ message: error.message });
    }
};


const getBoards = async (req, res) => {
    try {
        const { workspaceId, id: memberId } = req.params;

        // Validate input
        if (!workspaceId || !memberId) {
            return res.status(400).json({ message: 'Workspace ID or Member ID is missing' });
        }

        // Find the workspace
        const workspace = await Workspace.findOne({ shortId: workspaceId });

        if (!workspace) {
            return res.status(404).json({ message: 'Workspace not found' });
        }

        // Check if the member is part of the workspace (in members or guests)
        const isMember = workspace.members.some(
            (member) => member.user.toString() === memberId
        );

        const isGuest = workspace.guests.some(
            (guest) => guest.toString() === memberId
        );

        if (!isMember && !isGuest) {
            return res.status(403).json({ message: 'User is not a member or guest of this workspace' });
        }

        // Find boards in the workspace where the member is explicitly in the board's members list
        const boards = await Board.find({
            workspace: workspace._id,
            'members.user': memberId, // Ensure the member is in the board's members
        }).select('_id title permalink cover');

        // Return the filtered boards
        return res.status(200).json({ boards });
    } catch (error) {
        console.error('Error fetching boards:', error);
        return res.status(500).json({ message: error.message });
    }
};




module.exports = { getMembers, getBoards };

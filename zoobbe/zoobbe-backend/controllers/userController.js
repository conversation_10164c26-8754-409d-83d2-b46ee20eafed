const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Board = require('../models/Board');
const Activity = require('../models/Activity');
const Workspace = require('../models/Workspace');
const ActionList = require('../models/ActionList');


// Import nanoid asynchronously using dynamic import in CommonJS format
let nanoid;
(async () => {
  const nanoidModule = await import('nanoid');
  nanoid = nanoidModule.customAlphabet('1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 6);
})();

const { uploadFileToS3, getFileFromS3 } = require('../utils/s3Utils');
const { sendVerificationEmail, sendResetPasswordEmail } = require('../mailer');
const Card = require('../models/Card');
const { setCache, getCache } = require('../redis');
const { verifyGoogleToken, getGoogleUserInfo, generateStringHash, FILE_EXPIRATION_TIME } = require('../utils/helper');

const generateAccessToken = (user) => {
  return jwt.sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '180d' });
};

const generateRefreshToken = (user) => {
  return jwt.sign({ id: user._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '7d' });
};


const createDefaultWorkspace = async (user) => {
  try {
    // Step 1: Create the default workspace
    const workspace = new Workspace({
      name: 'Zoobbe Workspace',
      ownerId: user._id,
      members: [{ user: user._id, role: 'admin' }],
    });
    await workspace.save();

    const cover = {
      name: "photo-1730227569930-38d88b97b479.webp",
      url: "https://cloud.zoobbe.com/backgrounds/2560x1707/e027fd8492554f835979fca7c9aa52f457e47f0520dbc6460c6dc0ebc7fbd862/photo-1730227569930-38d88b97b479.webp",
      sizes: {
        original: "https://cloud.zoobbe.com/backgrounds/2560x1707/e027fd8492554f835979fca7c9aa52f457e47f0520dbc6460c6dc0ebc7fbd862/photo-1730227569930-38d88b97b479.webp",
        thumbnail: "https://cloud.zoobbe.com/backgrounds/150x100/e027fd8492554f835979fca7c9aa52f457e47f0520dbc6460c6dc0ebc7fbd862/photo-1730227569930-38d88b97b479.webp",
        medium: "https://cloud.zoobbe.com/backgrounds/640x426/e027fd8492554f835979fca7c9aa52f457e47f0520dbc6460c6dc0ebc7fbd862/photo-1730227569930-38d88b97b479.webp",
        large: "https://cloud.zoobbe.com/backgrounds/1280x853/e027fd8492554f835979fca7c9aa52f457e47f0520dbc6460c6dc0ebc7fbd862/photo-1730227569930-38d88b97b479.webp",
      },
    };

    // Step 2: Create the board
    const board = new Board({
      title: 'My Zoobbe Board',
      workspace: workspace._id,
      wShortId: workspace.shortId,
      members: [{ user: user._id, role: 'admin', status: 'accepted' }],
      cover,
      coverColor: [
        "rgb(26, 52, 78)",
        "rgb(217, 183, 145)",
        "rgb(133, 123, 121)",
        "rgb(122, 142, 161)",
        "rgb(146, 192, 206)"
      ],
      visibility: 'Private',
    });
    await board.save();

    // Function to create an action list with cards
    const createActionListWithCards = async (title, order, cardTitles) => {
      const actionList = new ActionList({
        title,
        board: board._id,
        order,
      });
      await actionList.save();

      const cards = cardTitles.map((cardTitle, index) => ({
        title: cardTitle,
        actionList: actionList._id,
        actionListTitle: actionList.title,
        board: board.shortId,
        order: index,
        shortId: nanoid(8),
        boardTitle: board.title,
        boardLink: board.shortLink,
      }));

      const createdCards = await Promise.all(cards.map(async (cardData) => {
        const card = new Card(cardData);
        await card.save();
        return card;
      }));

      actionList.cards = createdCards.map((card) => card._id);
      await actionList.save();

      return actionList._id;
    };

    // Step 3: Create action lists and cards
    const actionListIds = await Promise.all([
      createActionListWithCards('💼 To Do', 1, [
        'Plan your project roadmap',
        'Finalize project requirements',
      ]),
      createActionListWithCards('🕰️ In Progress', 2, [
        'Write project documentation',
        'Coordinate with the design team',
      ]),
      createActionListWithCards('✅ Completed', 3, [
        'Project kickoff meeting',
      ]),
    ]);

    // Step 4: Update the board with the action lists
    board.actionLists = actionListIds;
    await board.save();

    // Step 5: Update workspace and user with the board reference
    workspace.boards = [board._id];
    await workspace.save();

    user.workspaces = [workspace._id];
    await user.save();

    console.log('Default workspace creation completed successfully.');
  } catch (error) {
    console.error('Error creating default workspace:', error.message);
    throw new Error('Could not create default workspace.');
  }
};
const registerUser = async (req, res) => {
  const { name, email, password, recaptchaToken } = req.body;

  if (!name || !email || !password || !recaptchaToken) {
    return res.status(400).json({ message: "Name, email, password, and Turnstile token are required." });
  }

  try {
    // Verify Turnstile token with Cloudflare
    const turnstileResponse = await fetch("https://challenges.cloudflare.com/turnstile/v0/siteverify", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        secret: process.env.CLOUDFLARE_RECAPTCHA_SECRET_KEY, // Store in .env
        response: recaptchaToken,
      }),
    });

    const turnstileData = await turnstileResponse.json();

    if (!turnstileData.success) {
      return res.status(400).json({ message: "Turnstile verification failed. Are you a bot?" });
    }

    const existingUser = await User.findOne({ email });

    if (existingUser) {
      if (existingUser.verified) {
        return res.status(400).json({
          message: 'Email is already registered. Please <a href="/login">log in</a>.',
          registered: true,
        });
      } else {
        return res.status(400).json({
          message: "Email is already registered but not verified. Please check your email for a verification link or request a new one.",
        });
      }
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = new User({
      name,
      email,
      passwordHash: hashedPassword,
      verified: false,
      isDefaultPasswordSet: true,
      isPremiumMember: false,
      canSeeOnlineStatus: false,
    });
    await newUser.save();

    // Create default workspace
    await createDefaultWorkspace(newUser);

    // Send verification email asynchronously
    setImmediate(async () => {
      try {
        const verificationToken = jwt.sign({ id: newUser._id }, process.env.JWT_SECRET, { expiresIn: "1d" });
        await sendVerificationEmail(email, verificationToken, name);
      } catch (error) {
        console.error("Error sending verification email:", error);
      }
    });

    res.status(201).json({ message: "User registered successfully. Verification email sent." });
  } catch (error) {
    console.error("Error registering user:", error);
    res.status(500).json({ message: error.message });
  }
};



const authWithGoogle = async (req, res) => {
  const { token, action } = req.body;

  try {
    const googleUser = await getGoogleUserInfo(token);

    let user = await User.findOne({ googleId: googleUser.sub });

    if (!user) {
      user = new User({
        name: googleUser.name,
        email: googleUser.email,
        googleId: googleUser.sub,
        profilePicture: googleUser.picture,
        registeredWithGoogle: true,
        verified: true,
        isPremiumMember: false,
        canSeeOnlineStatus: false,
      });

      await user.save();
      await createDefaultWorkspace(user);
    }

    const accessToken = jwt.sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '180d' });
    const refreshToken = jwt.sign({ id: user._id }, process.env.JWT_REFRESH_SECRET, { expiresIn: '1y' });

    res.cookie('accessToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'Strict',
      maxAge: 180 * 24 * 60 * 60 * 1000, // 180 days
    });

    res.cookie('refreshToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'Strict',
      maxAge: 365 * 24 * 60 * 60 * 1000, // 365 days
    });

    res.status(200).json({
      message: action === 'signup' ? 'Signup successful, Redirecting...' : 'Login successful, Redirecting...',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        username: user.username,
        profilePicture: user.profilePicture,
      },
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};


const sendRecoverEmail = async (req, res) => {
  const { email } = req.body;

  try {
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({ message: 'User not found.' });
    }

    // Generate a new reset token and set expiration (e.g., 1 hour from now)
    const resetToken = jwt.sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
    const resetTokenExpiry = Date.now() + 60 * 60 * 1000; // 1 hour in milliseconds

    // Save the token and expiration to the user's record
    user.resetToken = resetToken;
    user.resetTokenExpiry = resetTokenExpiry;
    await user.save();

    // Send the reset password email
    await sendResetPasswordEmail(email, resetToken, user.name);

    res.status(200).json({ message: 'Recovery email sent. Please check your inbox.' });
  } catch (error) {
    console.error('Error sending recovery email:', error);
    res.status(500).json({ message: 'Failed to send recovery email.' });
  }
};


const resetPassword = async (req, res) => {
  const { token, newPassword } = req.body;

  try {
    // Decode the token to get the user ID
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.id;

    // Find the user and validate the token and expiration time
    const user = await User.findById(userId);

    if (!user || user.resetToken !== token || user.resetTokenExpiry < Date.now()) {
      return res.status(400).json({ message: 'Invalid or expired token.' });
    }

    // Hash the new password and update the user's password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.passwordHash = hashedPassword;

    // Invalidate the reset token
    user.resetToken = null;
    user.resetTokenExpiry = null;
    await user.save();

    res.status(200).json({ message: 'Password reset successfully.' });
  } catch (error) {
    console.error('Error resetting password:', error);
    res.status(500).json({ message: 'Invalid or expired token.' });
  }
};


const setPassword = async (req, res) => {
  const { newPassword, email } = req.body;

  // Validate new password
  if (!newPassword) {
    return res.status(400).json({ message: 'New password is required.' });
  }

  if (newPassword.length < 8) {
    return res.status(400).json({ message: 'Password must be at least 8 characters long.' });
  }

  try {
    // Find the user by email
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({ message: 'User not found.' });
    }

    // If the password is already set, no need to update
    if (user.isDefaultPasswordSet) {
      return res.status(400).json({ message: 'Password is already set.' });
    }

    // Hash the new password using bcrypt
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update user's password and set the isDefaultPasswordSet flag to true
    user.passwordHash = hashedPassword;
    user.isDefaultPasswordSet = true;
    await user.save();

    // Respond with success
    res.status(200).json({ message: 'Password updated successfully.' });
  } catch (error) {
    console.error('Error setting password:', error.message);
    res.status(500).json({ message: 'Failed to set password.' });
  }
};




const resendVerificationEmail = async (req, res) => {
  const { email } = req.body;

  try {
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({ message: 'User not found.' });
    }

    if (user.verified) {
      return res.status(400).json({ message: 'Email is already verified. You can log in.' });
    }

    const verificationToken = jwt.sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '1d' });
    await sendVerificationEmail(email, verificationToken, user.name);

    return res.status(200).json({ message: 'Verification email resent. Please check your inbox.' });

  } catch (error) {
    console.error('Error resending verification email:', error);
    return res.status(500).json({ message: error.message });
  }
};


const loginUser = async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    console.log('Email and password are required.');
    return res.status(400).json({ message: 'Email and password are required.' });
  }

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    const isMatch = await bcrypt.compare(password, user.passwordHash);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);

    // ✅ Store tokens in HttpOnly cookies
    res.cookie('accessToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'Strict',
      maxAge: 1 * 60 * 1000, // 15 minutes
    });

    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'Strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    res.status(200).json({ message: 'Login successful' }); // ✅ No tokens in response
  } catch (error) {
    console.error("Error logging in user:", error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};


const verifyEmail = async (req, res) => {
  const { token } = req.body;

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id);

    if (!user) {
      return res.status(400).json({ message: 'User not found' });
    }

    // If user is already verified, log them in
    if (user.verified) {
      const accessToken = generateAccessToken(user);
      const refreshToken = generateRefreshToken(user);
      return res.status(200).json({ accessToken, refreshToken });
    }

    // Mark user as verified
    user.verified = true;
    await user.save();

    // Generate tokens
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);

    // Return tokens (same as login)
    res.status(200).json({ accessToken, refreshToken });

  } catch (error) {
    console.error('Verification error:', error);
    res.status(400).json({ message: 'Invalid or expired token' });
  }
};



const refreshToken = async (req, res) => {
  const refreshToken = req.cookies.refreshToken; // ✅ Use HttpOnly cookie

  if (!refreshToken) {
    return res.status(401).json({ message: "Refresh token missing" });
  }



  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

    console.log('Refresh token', decoded);


    const user = await User.findById(decoded.id);

    if (!user) {
      return res.status(403).json({ message: "Invalid refresh token" });
    }

    // ✅ Generate a new access token
    const newAccessToken = generateAccessToken(user);

    // ✅ Optionally, generate a new refresh token (token rotation)
    const newRefreshToken = generateRefreshToken(user);

    res.cookie("accessToken", newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "Lax",
      maxAge: 1 * 24 * 60 * 1000, // 1 day

    });

    res.cookie("refreshToken", newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "Lax",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    res.status(200).json({ message: "Token refreshed" });

  } catch (err) {
    console.error("Refresh token error:", err);
    return res.status(403).json({ message: "Invalid or expired refresh token" });
  }
};


const getUsers = async (req, res) => {
  try {
    const users = await User.find().select('-passwordHash -resetToken -resetTokenExpiry -pushTokens -slack.botToken -slack.refreshToken -slack.expiresAt');
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: error.message });
  }
};

const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select(
      '-passwordHash -resetToken -resetTokenExpiry -pushTokens -slack.botToken -slack.refreshToken -slack.expiresAt'
    );

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const userData = await user.getPremiumData();
    res.json({ user: userData });
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({ message: error.message });
  }
};


const getUserByUserName = async (req, res) => {
  const { userName } = req.params;
  try {
    const user = await User.findOne({ username: userName }).select('-passwordHash -resetToken -resetTokenExpiry -pushTokens -slack.botToken -slack.refreshToken -slack.expiresAt');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.json({ user });
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({ message: error.message });
  }
};


const getActivities = async (req, res) => {
  const userId = req.user.id;

  try {
    const activities = await Activity.find({ initiator: userId })
      .select('card details createdAt') // Select only 'details' and 'createdAt'
      .populate({
        path: 'initiator', // Populate the initiator (User) information
        select: 'name profilePicture' // Select only 'name' and 'profilePicture' from the initiator
      })
      .sort({ createdAt: -1 }); // Sort by createdAt in descending order

    if (!activities || activities.length === 0) {
      return res.status(404).json({ message: 'Activities not found' });
    }

    res.status(200).json(activities);
  } catch (error) {
    console.error('Error fetching activities:', error);
    res.status(500).json({ message: error.message });
  }
};

const getUserActivities = async (req, res) => {
  const { userName } = req.params;
  const { page = 1, limit = 10 } = req.query; // Get page and limit from query params

  try {
    if (!userName) {
      return res.status(400).json({ message: 'Username is required' });
    }

    const user = await User.findOne({ username: userName });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const activities = await Activity.find({ initiator: user._id })
      .select('card details createdAt')
      .populate({ path: 'initiator', select: 'name profilePicture' })
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit) // Skip items based on page
      .limit(parseInt(limit)); // Limit the number of items per request

    const totalCount = await Activity.countDocuments({ initiator: user._id });

    res.status(200).json({
      activities,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: parseInt(page),
    });
  } catch (error) {
    console.error('Error fetching activities:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};



// Helper function to build card filter based on user preferences
const buildCardFilter = (userFilters) => {
  const cardFilter = { archived: false };

  if (userFilters.keyword) {
    const keywordRegex = new RegExp(userFilters.keyword, 'i');
    cardFilter.$or = [
      { title: keywordRegex },
      { description: keywordRegex },
      { 'users.name': keywordRegex },
      { 'users.username': keywordRegex },
      { 'labels.text': keywordRegex },
    ];
  }

  if (userFilters.noMembers) {
    cardFilter.users = { $size: 0 };
  } else if (userFilters.selectedMembers?.length) {
    cardFilter.users = { $in: userFilters.selectedMembers };
  }

  if (userFilters.noDates) {
    cardFilter['dueDate.date'] = { $exists: false };
  } else if (userFilters.overdue) {
    cardFilter['dueDate.date'] = { $lt: new Date(), $ne: null };
  } else if (userFilters.dueNextDay) {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);
    cardFilter['dueDate.date'] = {
      $gte: new Date(today.toISOString().split('T')[0]),
      $lt: new Date(tomorrow.toISOString().split('T')[0]),
    };
  }

  if (userFilters.noLabels) {
    cardFilter['labels.enabled'] = { $ne: true };
  } else if (userFilters.selectedLabels?.length) {
    cardFilter['labels._id'] = { $in: userFilters.selectedLabels };
  }

  return cardFilter;
};

// Controller function to update filters and fetch board data
const filters = async (req, res) => {
  const userId = req.user.id;
  const { filters, boardId } = req.body;

  try {
    // Update user filters
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: { filters } },
      { new: true, runValidators: true }
    ).lean();

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    const userFilters = updatedUser.filters || {};
    const cardFilter = buildCardFilter(userFilters);

    const cacheKey = `board:${userId}:${boardId}:${JSON.stringify(userFilters)}`;
    const cachedBoard = await getCache(cacheKey);

    if (cachedBoard) {
      return res.status(200).json(JSON.parse(cachedBoard));
    }

    // Fetch board data with necessary fields only
    const board = await Board.findOne({ shortId: boardId })
      .populate({
        path: 'actionLists',
        match: { archived: false },
        options: { sort: { order: 1 } },
        populate: {
          path: 'cards',
          select: {
            title: 1,
            permalink: 1,
            description: { $substrCP: ['$description', 0, 1] },
            attachments: { $map: { input: '$attachments', as: 'a', in: '$$a._id' } }, // Return only attachment IDs
            checklists: {
              $map: {
                input: '$checklists',
                as: 'checklist',
                in: {
                  title: '$$checklist.title',
                  items: {
                    $map: {
                      input: '$$checklist.items',
                      as: 'item',
                      in: { checked: '$$item.checked' }
                    }
                  }
                }
              }
            }
          },
          match: cardFilter,
          options: { sort: { order: 1 } },
          populate: [
            { path: 'actionList', select: '_id' },
            { path: 'order' },
            { path: 'shortId' },
            { path: 'type' },
            { path: 'users', select: 'name email username profilePicture online isPremiumMember canSeeOnlineStatus' },
            { path: 'comments.member', select: '_id' },
            { path: 'watchers', select: '_id' },
            { path: 'labels', select: 'text color' },
            { path: 'activities', select: '_id' },
            { path: 'cover', select: 'url coverColor' },
            { path: 'dueDate' },
          ],
        },
      })
      .select('-members -labels')
      .lean();

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Update expired covers in parallel
    const expiredCoverPromises = board.actionLists.flatMap(actionList =>
      actionList.cards.map(card => {
        const cover = card.cover;
        if (cover && cover.expiresAt && new Date() >= new Date(cover.expiresAt)) {
          return getFileFromS3(`cards/${card.shortId}/${cover.name}`).then(async (newUrl) => {
            cover.url = newUrl;
            cover.expiresAt = new Date(Date.now() + FILE_EXPIRATION_TIME);
            await Card.findByIdAndUpdate(card._id, { "cover.url": newUrl, "cover.expiresAt": cover.expiresAt });
          });
        }
        return null;
      }).filter(Boolean)
    );

    await Promise.all(expiredCoverPromises);

    // Cache the board data
    // await setCache(cacheKey, JSON.stringify(board), 3600);

    res.status(200).json({
      message: 'Filters updated successfully',
      filters: updatedUser.filters,
      board,
    });

  } catch (error) {
    console.error('Error updating filters:', error);
    res.status(500).json({ message: 'Error updating filters', error });
  }
};



// Controller function to get filters
const getFilters = async (req, res) => {
  const userId = req.user.id; // Assuming you have user authentication and the user ID is available in req.user
  const { boardId } = req.query; // Assuming you have user authentication and the user ID is available in req.user

  try {
    // Find the user by ID
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get the user's filters
    const userFilters = user.filters || {};
    const cardFilter = buildCardFilter(userFilters);

    // Include boardId in the filter criteria
    cardFilter.board = boardId;

    // Count the number of cards that match the filter
    const cardCount = await Card.countDocuments(cardFilter);

    // Respond with the user's filters and card count
    res.status(200).json({ filters: user.filters, cardCount });
  } catch (error) {
    console.error('Error retrieving filters:', error);
    res.status(500).json({ message: 'Error retrieving filters', error });
  }
};

const getSearchResults = async (req, res) => {
  try {
    const { query } = req.query;

    if (!query) {
      return res.status(400).json({ message: 'Search query is required.' });
    }

    // Perform case-insensitive search for boards and cards
    const boards = await Board.find({
      title: { $regex: query, $options: 'i' }, // Search boards by title
    }).select('title shortLink permalink createdAt labels members');

    const cards = await Card.find({
      title: { $regex: query, $options: 'i' }, // Search cards by title
    }).select('title shortLink permalink createdAt labels actionList board');

    // Respond with grouped results
    res.status(200).json({
      success: true,
      data: {
        boards,
        cards,
      },
      message: `Found ${boards.length} boards and ${cards.length} cards matching the query.`,
    });
  } catch (error) {
    console.error('Error fetching search results:', error);
    res.status(500).json({ message: 'Server error while searching.', error });
  }
};


const uploadPicture = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Get the user's ID from the authenticated token
    const userId = req.user.id;
    const hash = generateStringHash(userId);

    const file = req.file;

    const folderName = `profile-pictures/${hash}`;  // S3 folder for profile pictures
    const fileUrl = await uploadFileToS3({
      file,
      folderName,
      bucketName: process.env.MEMBERS_BUCKET_NAME,
      width: 220,
      height: 220,
      req
    });


    // Find the user by ID and update their profile picture
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.profilePicture = fileUrl;  // Update the profile picture URL
    await user.save();  // Save changes to the database

    return res.status(200).json({ message: 'Profile picture uploaded successfully', fileUrl });
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    return res.status(500).json({ message: 'Error uploading profile picture', error });
  }
};

const updateUserProfile = async (req, res) => {
  try {
    const { bio, username, profilePicture } = req.body;  // Extract the profile data from the request body
    const userId = req.user.id;  // Get user ID from the JWT token

    // Find the user by ID
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update the user's bio, username, and profile picture if they exist in the request
    if (bio) {
      user.bio = bio;
    }
    if (username) {
      user.username = username;
    }
    if (profilePicture) {
      user.profilePicture = profilePicture;  // Use the S3 URL if updated
    }

    // Save the updated user data to the database
    await user.save();

    return res.status(200).json({ message: 'Profile updated successfully', user });
  } catch (error) {
    console.error('Error updating profile:', error);
    return res.status(500).json({ message: 'Error updating profile', error });
  }
};

const updateTheme = async (req, res) => {
  const { theme } = req.body;
  const userId = req.user.id;

  if (!['light', 'dark', 'system'].includes(theme)) {
    return res.status(400).json({ error: 'Invalid theme selected' });
  }

  try {
    const user = await User.findByIdAndUpdate(
      userId,
      { $set: { preferredTheme: theme } },
      { new: true }
    );
    res.json({ message: 'Theme updated successfully', preferredTheme: user.preferredTheme });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to update theme' });
  }
}


// Update starred boards

const updateStared = async (req, res) => {
  try {
    const userId = req.user.id; // Extract user ID from the token
    const { boardId, action } = req.body; // `action` should be 'add' or 'remove'

    if (!boardId || !['add', 'remove'].includes(action)) {
      return res.status(400).json({ message: 'Invalid request' });
    }

    const board = await Board.findOne({ shortId: boardId });

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Ensure starredBoards is an array
    if (!Array.isArray(user.starredBoards)) {
      user.starredBoards = [];
    }

    const _boardId = board._id;

    if (action === 'add') {
      // Add boardId to starred boards if not already added
      if (!user.starredBoards.includes(_boardId)) {
        user.starredBoards.push(_boardId);
      }
    } else if (action === 'remove') {
      // Remove boardId from starred boards
      user.starredBoards = user.starredBoards.filter((id) => id.toString() !== _boardId.toString());
    }

    await user.save();

    res.status(200).json({ message: `Board ${action === 'add' ? 'starred' : 'unstarred'} successfully` });
  } catch (error) {
    console.error('Error updating starred boards:', error);
    res.status(500).json({ message: 'Error updating starred boards' });
  }
};

const getStaredBoards = async (req, res) => {
  try {
    const userId = req.user.id; // Extract user ID from the token

    const user = await User.findById(userId).populate({
      path: 'starredBoards',
      select: '_id shortId title visibility permalink cover members',
      populate: {
        path: 'members.user',
        select: '_id name email username profilePicture online isPremiumMember canSeeOnlineStatus', // Select the fields you want to include for the user
      },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all starred boards
    const starredBoards = user.starredBoards;

    // Fetch card count for each board
    const boardsWithCardCount = await Promise.all(
      starredBoards.map(async (board) => {
        const cardCount = await Card.countDocuments({
          board: board.shortId, // Filter by board shortId
          archived: false, // Ensure the card is not archived
        });

        // Extract only user data from members, excluding null values or members without _id
        const members = board.members
          .filter(member => member.user && member.user._id)
          .map(member => member.user);

        return {
          ...board.toObject(), // Convert Mongoose document to plain object
          cardCount,
          members, // Replace members with the extracted user data
        };
      })
    );

    res.status(200).json(boardsWithCardCount);
  } catch (error) {
    console.error('Error fetching starred boards:', error);
    res.status(500).json({ message: 'Error fetching starred boards' });
  }
};

const getRecentBoards = async (req, res) => {
  try {
    const userId = req.user.id; // Extract user ID from the token
    const { limit } = req.query;

    const user = await User.findById(userId).populate({
      path: 'recentBoards',
      select: '_id shortId title visibility permalink cover members workspace',
      populate: [
        {
          path: 'members.user',
          select: '_id name email username profilePicture online isPremiumMember canSeeOnlineStatus', // Select the fields you want to include for the user
        },
        {
          path: 'workspace',
          select: '_id name', // Fetch the workspace `_id` and `name` (or `title` if applicable)
        },
      ],
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all recent boards
    let recentBoards = user.recentBoards;

    if (limit) {
      const parsedLimit = parseInt(limit, 10);
      if (!isNaN(parsedLimit)) {
        recentBoards = recentBoards.slice(0, parsedLimit);
      }
    }

    // Fetch card count for each board
    const boardsWithCardCount = await Promise.all(
      recentBoards.map(async (board) => {
        const cardCount = await Card.countDocuments({
          board: board.shortId, // Filter by board shortId
          archived: false, // Ensure the card is not archived
        });

        // Extract only user data from members, excluding null values or members without _id
        const members = board.members
          .filter(member => member.user && member.user._id)
          .map(member => member.user);

        return {
          ...board.toObject(), // Convert Mongoose document to plain object
          cardCount,
          members, // Replace members with the extracted user data
          workspace: board.workspace, // Include workspace details
        };
      })
    );

    res.status(200).json(boardsWithCardCount);
  } catch (error) {
    console.error('Error fetching recent boards:', error);
    res.status(500).json({ message: 'Error fetching recent boards' });
  }
};

// Get user settings
const getSettings = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select("settings slack");

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Exclude confidential Slack fields
    const { settings, slack } = user;
    const sanitizedSlack = slack
      ? {
        connected: slack.connected,
        userId: slack.userId,
        teamId: slack.teamId,
        channelId: slack.channelId,
      }
      : null;

    res.json({ settings, slack: sanitizedSlack });
  } catch (error) {
    console.error("Error fetching user settings:", error);
    res.status(500).json({ message: error.message });
  }
};



// Update user settings
const updateSettings = async (req, res) => {
  try {
    const {
      preferredTheme,
      preferredThemeColor,
      language,
      timezone,
      highContrast,
      notificationsEnabled,
      profileNotifications,
      pushNotifications,
      slackNotifications,
      emailNotifications
    } = req.body;

    const user = await User.findByIdAndUpdate(
      req.user.id,
      {
        $set: {
          "settings.preferredTheme": preferredTheme,
          "settings.themeColor": preferredThemeColor,
          "settings.language": language,
          "settings.timezone": timezone,
          "settings.highContrast": highContrast,
          "settings.notificationsEnabled": notificationsEnabled,
          "settings.profileNotifications": profileNotifications,
          "settings.pushNotifications": pushNotifications,
          "settings.slackNotifications": slackNotifications,
          "settings.emailNotifications": emailNotifications
        }
      },
      { new: true, select: 'settings' }
    );

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ message: 'Settings updated successfully', settings: user.settings });
  } catch (error) {
    console.error('Error updating user settings:', error);
    res.status(500).json({ message: error.message });
  }
};

const connectWithSlack = async (req, res) => {
  const { code } = req.body; // Extract code from request body

  if (!code) {
    return res.status(400).json({ error: "Authorization code is missing" });
  }

  try {
    // Exchange code for token
    const response = await fetch("https://slack.com/api/oauth.v2.access", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        code: code,
        redirect_uri: process.env.SLACK_REDIRECT_URI,
      }),
    });

    const data = await response.json();

    if (!data.ok || !data.access_token || !data.authed_user?.id) {
      return res.status(400).json({ error: data.error || "Slack authentication failed" });
    }

    // Extract relevant Slack data
    const botToken = data.access_token; // Bot token (xoxb-...)
    const refreshToken = data.refresh_token; // Refresh token
    const expiresIn = data.expires_in; // Token expiration time (in seconds)
    const userId = data.authed_user.id; // Slack User ID
    const teamId = data.team?.id || null; // Slack Team ID
    const connected = true;

    console.log({ data });


    // Find the user based on their authenticated session (assuming req.user exists)
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // ✅ Store Slack data in user model under `slack` field (Encrypt tokens before storing)
    user.slack = {
      connected,
      userId,
      teamId,
      botToken, // Ideally, encrypt this before storing
      refreshToken, // Save this for token refresh logic
      expiresAt: Date.now() + expiresIn * 1000, // Store expiration time
    };

    await user.save();

    res.json({
      success: true,
      message: "Slack connected successfully!",
      slack: {
        userId,
        teamId,
      }, // Do NOT expose tokens in response
    });
  } catch (error) {
    console.error("Slack OAuth Error:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

const disconnectFromSlack = async (req, res) => {
  try {
    const userId = req.user.id; // Get user ID from auth middleware

    // Update the user document to remove Slack connection details
    await User.findByIdAndUpdate(userId, {
      $set: {
        "slack.connected": false,
        "slack.userId": null,
        "slack.teamId": null,
        "slack.channelId": null,
        "slack.botToken": null,
        "slack.refreshToken": null,
        "slack.expiresAt": null
      }
    });

    res.json({ message: "Successfully disconnected from Slack" });
  } catch (error) {
    console.error("Error disconnecting Slack:", error);
    res.status(500).json({ message: "Failed to disconnect Slack" });
  }
};

const getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select(
      '-passwordHash -resetToken -resetTokenExpiry -pushTokens -slack.botToken -slack.refreshToken -slack.expiresAt'
    );

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const userData = await user.getPremiumData();
    res.json({ user: userData });
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  registerUser,
  resendVerificationEmail,
  loginUser,
  resetPassword,
  setPassword,
  sendRecoverEmail,
  verifyEmail,
  refreshToken,
  authWithGoogle,
  getUsers,
  getUserById,
  getUserByUserName,
  getActivities,
  getUserActivities,
  filters,
  getFilters,
  getSearchResults,
  uploadPicture,
  updateUserProfile,
  updateStared,
  updateTheme,
  getStaredBoards,
  getRecentBoards,
  updateSettings,
  getSettings,
  connectWithSlack,
  disconnectFromSlack,
  getMe,
};


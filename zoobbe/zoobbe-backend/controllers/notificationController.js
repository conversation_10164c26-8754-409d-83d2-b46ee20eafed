const Card = require('../models/Card');
const User = require('../models/User');
const Notification = require('../models/Notification');
const { sendPushNotification } = require('../utils/firebaseAdmin');
const { sendSlackNotification } = require('../utils/helper');

const watch = async (req, res) => {
    try {
        const card = await Card.findOne({ shortId: req.params.cardId });

        if (!card) {
            return res.status(404).json({ success: false, message: "Card not found" });
        }
        if (!req.user.id) {
            return res.status(404).json({ success: false, message: "User not found" });
        }

        // Check if the user is already a watcher
        if (!card.watchers.includes(req.user.id)) {
            card.watchers.push(req.user.id); // Add user to watchers list
            await card.save(); // Save the updated card

            // Emit notification to watchers
            const io = req.app.get('socketio');
            card.watchers.forEach(watcherId => {
                if (watcherId.toString() !== req.user.id) {
                    io.to(watcherId.toString()).emit('newNotification', {
                        type: 'watch',
                        message: `You are now watching the card: ${card.title}`,
                        initiator: req.user.id
                    });
                }
            });
        }

        res.json({ success: true, message: "You are now watching the card" });

    } catch (error) {
        res.status(500).json({ success: false, message: "Server error" });
    }
};

const unwatch = async (req, res) => {
    try {
        const cardId = req.params.cardId;
        const userId = req.user.id;

        const result = await Card.updateOne(
            { shortId: cardId },
            { $pull: { watchers: userId } }
        );

        if (result.nModified === 0) {
            return res.status(404).json({ success: false, message: "Card not found or user not a watcher" });
        }

        res.json({ success: true, message: "You have stopped watching the card" });

    } catch (error) {
        res.status(500).json({ success: false, message: "Server error" });
    }
};

const getNotifications = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query; // Default to page 1 and limit 10
        const skip = (page - 1) * limit;

        const notifications = await Notification.find({ member: req.user.id })
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit))
            .populate('initiator', 'name username email profilePicture');

        const totalNotifications = await Notification.countDocuments({ member: req.user.id });

        res.json({
            notifications,
            totalPages: Math.ceil(totalNotifications / limit),
            currentPage: page
        });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Server error' });
    }
};


// Increment the new notifications count
// Add or update the new notifications count
const addNewNotificationsCount = async (req, res) => {
    try {
        const userId = req.user.id; // Assuming userId is extracted from the token
        const { newNotificationsCount } = req.body;

        // Find the user and update their new notification count
        const user = await User.findByIdAndUpdate(
            userId,
            { newNotificationsCount }, // Set the count to the value sent from the frontend
            { new: true }
        );

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        return res.status(200).json({ newNotificationsCount: user.newNotificationsCount });
    } catch (error) {
        return res.status(500).json({ message: 'Error updating notifications count', error });
    }
};


// Get the current count of new notifications
const getNewNotificationsCount = async (req, res) => {
    try {
        const userId = req.user.id; // Assuming userId is extracted from the token

        const user = await User.findById(userId);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        return res.status(200).json({ newNotificationsCount: user.newNotificationsCount });
    } catch (error) {
        return res.status(500).json({ message: 'Error fetching notifications count', error });
    }
};

const sendPushNotificationHandler = async (req, res) => {
    try {
        const { memberId, type, data } = req.body;
        const { cardId } = data;

        const initiator = req.user?.name || "Someone"; // Default to "Someone" if name is missing
        if (!memberId || !type) {
            return res.status(400).json({ error: "Missing required fields: 'memberId' and 'type' are required." });
        }

        const card = await Card.findOne({ shortId: cardId });
        if (!card) {
            return res.status(404).json({ error: "Card not found." });
        }

        const user = await User.findById(memberId);
        if (!user) {
            return res.status(404).json({ error: "User not found." });
        }

        const tokens = user.pushTokens || [];
        const url = card.permalink || "https://zoobbe.com"; // Fallback URL

        const isSendSlackNotification = (user.slack.connected && user.settings.slackNotifications) || false;
        const isSendPushNotification = user.settings.pushNotifications || false;

        let title = "Zoobbe Notification";
        let simpleBody = "You have a new update."; // For push notifications
        let slackBody = simpleBody; // Default slack message

        const cardLink = `${process.env.SITE_URL}${card.shortLink}`;
        const initiatorProfile = `${process.env.SITE_URL}/u/${req.user?.username}/activity`;

        const notificationMessages = {
            ADD_MEMBER_TO_CARD: [
                "New Card Assigned",
                `${initiator} added you to a card on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> added you to the card *<${cardLink}|${card.title}>*.`
            ],
            REMOVE_MEMBER_FROM_CARD: [
                "Removed from Card",
                `${initiator} removed you from a card on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> removed you from the card *<${cardLink}|${card.title}>*.`
            ],
            COMMENT_ON_CARD: [
                "New Comment",
                `${initiator} left a comment on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> commented on *<${cardLink}|${card.title}>*.`
            ],
            MENTION_IN_COMMENT: [
                "You Were Mentioned",
                `${initiator} mentioned you in a comment on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> mentioned you in a comment on *<${cardLink}|${card.title}>*.`
            ],
            DUE_DATE_REMINDER: [
                "Card Due Soon",
                `Reminder: You have a card due soon on Zoobbe.`,
                `Reminder: Your card *<${cardLink}|${card.title}>* is due soon.`
            ],
            CARD_MOVED: [
                "Card Moved",
                `${initiator} moved a card on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> moved the card *<${cardLink}|${card.title}>*.`
            ],
            TASK_COMPLETED: [
                "Card Completed",
                `${initiator} marked a card as done on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> marked *<${cardLink}|${card.title}>* as done.`
            ],
            CARD_ARCHIVED: [
                "Card Updated",
                `${initiator} archived a card on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> archived *<${cardLink}|${card.title}>*.`
            ],
            CARD_UNARCHIVED: [
                "Card Updated",
                `${initiator} unarchived a card on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> unarchived *<${cardLink}|${card.title}>*.`
            ],

            NEW_ASSIGNMENT: [
                "New Assignment",
                `${initiator} assigned you a card on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> assigned you to *<${cardLink}|${card.title}>*.`
            ],
            PROJECT_INVITATION: [
                "Project Invitation",
                `${initiator} invited you to a project on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> invited you to a project on Zoobbe.`
            ],
            PROJECT_REMOVAL: [
                "Removed from Project",
                `${initiator} removed you from a project on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> removed you from a project.`
            ],
            NEW_MESSAGE: [
                "New Message",
                `${initiator} sent you a message on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> sent you a message.`
            ],
            REACTION_TO_COMMENT: [
                "Reaction on Comment",
                `${initiator} reacted to your comment on Zoobbe.`,
                `<${initiatorProfile}|${initiator}> reacted to your comment.`
            ]
        };

        if (notificationMessages[type]) {
            [title, simpleBody, slackBody] = notificationMessages[type];
        }

        // Send push notification (simple message)
        if (isSendPushNotification && tokens.length > 0) {
            await sendPushNotification({ tokens, title, body: simpleBody, url }).catch((err) => {
                console.error("Push notification failed:", err);
            });
        }

        // Send Slack notification (detailed message)
        if (isSendSlackNotification) {
            await sendSlackNotification(user, slackBody).catch((err) => {
                console.error("Slack notification failed:", err);
            });
        }

        return res.json({ message: "Notifications sent successfully." });
    } catch (error) {
        console.error("Push notification error:", error);
        res.status(500).json({ error: "Failed to send notifications. Please try again later." });
    }
};




const markAsAllRead = async (req, res) => {
    await Notification.updateMany({ member: req.user.id, read: false }, { $set: { read: true } }); // Updated to 'member'
    res.json({ success: true });
};

const markAsRead = async (req, res) => {
    const { notificationId } = req.params;

    if (!notificationId) {
        return res.status(400).json({ success: false, message: "Notification ID is required." });
    }

    await Notification.updateOne(
        { _id: notificationId, member: req.user.id },
        { $set: { read: true } }
    );

    res.json({ success: true });
};

const updateNotificationTokens = async (req, res) => {
    try {
        const userId = req.user.id;
        const { pushToken } = req.body;

        if (!pushToken) {
            return res.status(400).json({ error: "Push token is required" });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ error: "User not found" });
        }

        // Remove duplicate tokens & keep the 3 most recent ones
        let updatedTokens = [pushToken, ...user.pushTokens.filter(token => token !== pushToken)];
        updatedTokens = updatedTokens.slice(0, 3); // Keep only the latest 3 tokens

        await User.updateOne(
            { _id: userId },
            { $set: { pushTokens: updatedTokens } }
        );

        return res.status(200).json({ success: true, message: "Push token updated!" });
    } catch (error) {
        console.error("Error updating push token:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};


module.exports = {
    watch,
    unwatch,
    getNotifications,
    addNewNotificationsCount,
    getNewNotificationsCount,
    sendPushNotificationHandler,
    updateNotificationTokens,
    markAsAllRead,
    markAsRead
};

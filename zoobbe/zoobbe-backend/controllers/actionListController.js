const ActionList = require('../models/ActionList');
const Board = require('../models/Board');
const Card = require('../models/Card');

// Create a new action list
const createActionList = async (req, res) => {
  const { title, boardId } = req.body; // Use boardShortId here
  try {
    // Find the board by its shortId
    const board = await Board.findOne({ shortId: boardId });
    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    const existingListsCount = await ActionList.countDocuments({ board: board._id });

    const newActionList = new ActionList({
      title,
      board: board._id,
      boardShortId: board.shortId,
      order: existingListsCount
    });

    // Save the new ActionList
    await newActionList.save();

    // Update the board's actionLists with the new ActionList's ObjectId
    board.actionLists.push(newActionList._id);
    await board.save();

    res.status(201).json({ message: 'Action list created successfully', actionList: newActionList });
  } catch (error) {
    console.error('Error creating action list:', error);
    res.status(500).json({ message: error.message });
  }
};



const updateActionList = async (req, res) => {
  const { title } = req.body;
  const { actionListId } = req.params;


  try {
    const actionList = await ActionList.findByIdAndUpdate(actionListId, { title }, { new: true });
    if (!actionList) {
      return res.status(404).send({ message: 'List not found' });
    }
    res.send({ actionList });
  } catch (error) {
    res.status(500).send({ message: 'Server error: ' + error.message });
  }
};

const archiveActionListCards = async (req, res) => {
  try {
    const { actionListId } = req.params;

    // Find all cards in the given action list
    const cards = await Card.find({ actionList: actionListId });

    if (!cards.length) {
      return res.status(404).json({ message: "No cards found in this action list." });
    }

    // Archive each card (assuming there's an `archived` field)
    await Card.updateMany({ actionList: actionListId }, { archived: true });

    res.status(200).json({ message: "All cards in the action list have been archived." });
  } catch (error) {
    console.error("Error archiving action list cards:", error);
    res.status(500).json({ message: "Internal server error." });
  }
};


const watchActionList = async (req, res) => {
  try {
    const { actionListId } = req.params;
    const userId = req.user.id;

    const actionList = await ActionList.findById(actionListId);
    if (!actionList) {
      return res.status(404).json({ message: "Action list not found." });
    }

    const isWatching = actionList.watchers.includes(userId);

    if (isWatching) {
      actionList.watchers = actionList.watchers.filter(id => id.toString() !== userId);
    } else {
      actionList.watchers.push(userId);
    }

    await actionList.save();

    res.status(200).json({
      message: isWatching ? "Unwatched action list." : "Watching action list.",
      watchers: actionList.watchers
    });

  } catch (error) {
    res.status(500).json({ message: "Internal server error." });
  }
};

// Fetch all action lists
const getActionLists = async (req, res) => {
  try {
    const actionLists = await ActionList.find().populate('cards');
    res.status(200).json(actionLists);
  } catch (error) {
    console.error('Error fetching action lists:', error);
    res.status(500).json({ message: error.message });
  }
};

const getArchivedLists = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { page = 1, limit = 20, search = "" } = req.query; // Default page=1, limit=20

    // Convert page and limit to numbers
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // Verify if the board exists
    const board = await Board.findOne({ shortId: boardId });
    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Search filter
    const searchFilter = search
      ? { title: { $regex: `\\b${search.split(" ").join("\\b.*\\b")}\\b`, $options: "i" } }
      : {};

    // Fetch archived lists with pagination
    const archivedLists = await ActionList.find({ boardShortId: boardId, archived: true, ...searchFilter })
      .sort({ updatedAt: -1 }) // Sorting by most recently updated
      .skip(skip)
      .limit(limitNumber);

    // Count total archived lists
    const totalArchivedLists = await ActionList.countDocuments({ boardShortId: boardId, archived: true, ...searchFilter });

    res.status(200).json({
      lists: archivedLists,
      hasMore: skip + archivedLists.length < totalArchivedLists, // Check if more pages exist
      listsCount: totalArchivedLists
    });
  } catch (error) {
    console.error('Error fetching archived lists:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};


const getActionListById = async (req, res) => {
  const { actionListId } = req.params;
  try {
    // Find the action list by ID and populate the cards
    const actionList = await ActionList.findById(actionListId).populate({
      path: 'cards',
      match: { archived: false } // Filter to exclude archived cards
    });

    if (!actionList) {
      return res.status(404).json({ message: 'Action list not found' });
    }

    res.status(200).json({ actionList });
  } catch (error) {
    console.error('Error fetching action list by ID:', error);
    res.status(500).json({ message: error.message });
  }
};


// Fetch all action lists by Board ID
const getActionListByBoardId = async (req, res) => {
  const { boardId } = req.params;
  try {
    const actionLists = await ActionList.find({ board: boardId }).populate('cards');
    if (!actionLists || actionLists.length === 0) {
      return res.json({ actionLists: [], message: 'No action lists found for this board' });
    }
    res.status(200).json(actionLists);
  } catch (error) {
    console.error('Error fetching action lists by board ID:', error);
    res.status(500).json({ message: error.message });
  }
};


// Delete an action list and all its cards
const deleteActionList = async (req, res) => {
  const { actionListId } = req.params;

  try {
    const actionList = await ActionList.findById(actionListId);
    if (!actionList) {
      return res.status(404).json({ message: 'Action list not found' });
    }

    // Delete all cards belonging to this list
    await Card.deleteMany({ actionList: actionListId });

    // Delete the action list itself
    await ActionList.findByIdAndDelete(actionListId);

    res.status(200).json({ message: 'Action list and all associated cards deleted successfully' });
  } catch (error) {
    console.error('Error deleting action list:', error);
    res.status(500).json({ message: error.message });
  }
};

// Archive/Unarchive an action list
const archiveActionList = async (req, res) => {
  const { actionListId } = req.params;
  const { archivedStatus } = req.body; // true for archive, false for unarchive

  console.log(req.body);

  try {

    // Update the action list's archive status
    const actionList = await ActionList.findByIdAndUpdate(
      actionListId,
      { archived: archivedStatus },
      { new: true }
    ).populate({
      path: 'cards',
      match: { $or: [{ isArchivedWithList: true }] }, // Include only necessary cards
    });


    if (!actionList) {
      return res.status(404).json({ message: 'Action list not found' });
    }

    // Handle archiving and unarchiving logic
    if (archivedStatus) {
      // Archive all cards and set isArchivedWithList: true
      await Card.updateMany(
        { actionList: actionListId, archived: false },
        {
          $set: {
            archived: true,
            isArchivedWithList: true
          }
        }
      );
    } else {
      // Only unarchive cards that were archived with the list
      await Card.updateMany(
        { actionList: actionListId, isArchivedWithList: true },
        {
          $set: {
            archived: false,
            isArchivedWithList: false
          }
        }
      );
    }

    res.status(200).json({
      message: archivedStatus
        ? 'Action list archived successfully'
        : 'Action list unarchived successfully',
      actionList
    });
  } catch (error) {
    console.error('Error updating action list archive status:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

const copyActionList = async (req, res) => {
  const { actionListId } = req.params;
  const { boardId, newTitle, position } = req.body; // Allow custom position

  try {
    // Find the original action list
    const originalList = await ActionList.findById(actionListId).lean();
    if (!originalList) {
      return res.status(404).json({ message: 'Action list not found' });
    }

    // Create a copy of the action list
    const copiedList = await ActionList.create({
      title: newTitle || `${originalList.title} (Copy)`,
      board: boardId,
      cards: [],
      position: position !== undefined ? position : originalList.position + 1,
    });

    // Fetch all cards in the original list
    const originalCards = await Card.find({ actionList: actionListId }).lean();
    if (originalCards.length > 0) {
      // Map the original cards to a new format for bulk insertion
      const copiedCards = originalCards.map(card => ({
        ...card, // Copy all fields
        _id: undefined, // Let MongoDB generate a new ID
        actionList: copiedList._id, // Assign to the new list
        actionListTitle: copiedList.title,
        board: boardId, // Assign to the new board
        createdAt: new Date(), // Reset timestamps
      }));

      // Insert all copied cards in bulk
      const newCards = await Card.insertMany(copiedCards);

      // Update copied list with copied card IDs
      copiedList.cards = newCards.map(card => card._id);
      await copiedList.save();
    }

    // Update the board with the copied list
    await Board.findByIdAndUpdate(boardId, {
      $push: { actionLists: copiedList._id },
    });

    res.status(201).json({ message: 'List copied successfully', copiedList });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to copy list', error: error.message });
  }
};



module.exports = {
  createActionList,
  updateActionList,
  archiveActionListCards,
  watchActionList,
  getActionLists,
  getArchivedLists,
  getActionListById,
  getActionListByBoardId,
  deleteActionList,
  archiveActionList,
  copyActionList
};

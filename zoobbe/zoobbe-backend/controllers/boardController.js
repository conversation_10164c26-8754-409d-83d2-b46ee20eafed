const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const https = require('https');

const Board = require('../models/Board');
const Workspace = require('../models/Workspace');
const User = require('../models/User');
const { emitUserAction, buildCardFilter, generateInviteToken, createActivity, getUnsplashImageUrl, getUnsplashImageName, isValidUrl, emitIOCardAction, sendPushNotificationHandler, sendAppNotification, FILE_EXPIRATION_TIME, convertTrelloExportToZoobbe, SUBSCRIPTION_LIMITS } = require('../utils/helper');
const { ActionTypes } = require('../sockets/ActionTypes');
const ActionList = require('../models/ActionList');
const Card = require('../models/Card');
const { setCache, getCache } = require('../redis');
const { getFileFromS3, uploadFileToS3, uploadUrlToS3, isFileExistsInS3, getFileUrlFromS3 } = require('../utils/s3Utils');
const { sendInvitationEmail, sendJoinedEmail, sendRemoveFromEmail } = require('../mailer');
const ColorThief = require('colorthief'); // Import the updated package
const bcrypt = require('bcryptjs');
const UsedToken = require('../models/UsedToken');

const crypto = require('crypto');
const Background = require('../models/Background');
const Subscription = require('../models/Subscription');

const getBoards = async (req, res) => {
  const userId = req.user.id;
  const { workspaceId } = req.query;

  try {
    // If workspaceId is provided, fetch boards for that specific workspace
    if (workspaceId) {
      const workspace = await Workspace.findOne({
        shortId: workspaceId,
        $or: [
          { ownerId: userId },
          { 'members.user': userId }
        ]
      }).select('name shortId')
        .populate({
          path: 'boards',
          select: 'title shortId workspaceId cover permalink members cardsCount',
          match: { archived: false },
          options: {
            sort: { lastViewed: -1 } // Sort by most recently viewed
          }
        })
        .lean(); // Use lean() for better performance

      if (!workspace) {
        return res.status(404).json({ message: 'Workspace not found' });
      }

      // Process the boards
      const workspaceBoards = [];

      if (workspace.boards && workspace.boards.length > 0) {
        workspace.boards.forEach(board => {
          workspaceBoards.push({
            _id: board._id,
            shortId: board.shortId,
            title: board.title,
            workspaceName: workspace.name,
            workspaceShortId: workspace.shortId,
            cover: board.cover,
            permalink: board.permalink,
            members: board.members,
            cardsCount: board.cardsCount
          });
        });
      }

      // Return all boards for the workspace
      return res.status(200).json(workspaceBoards);
    }

    // If no workspaceId, fetch all boards across all workspaces (original behavior)
    const workspaces = await Workspace.find({
      $or: [
        { ownerId: userId },
        { 'members.user': userId }
      ]
    }).select('name shortId')
      .populate({
        path: 'boards',
        select: 'title shortId workspaceId cover permalink members cardsCount',
        match: { archived: false },
        options: {
          sort: { lastViewed: -1 } // Sort by most recently viewed
        }
      })
      .lean(); // Use lean() for better performance

    // Process the boards
    const workspaceBoards = [];

    workspaces.forEach(workspace => {
      if (workspace.boards && workspace.boards.length > 0) {
        workspace.boards.forEach(board => {
          workspaceBoards.push({
            _id: board._id,
            shortId: board.shortId,
            title: board.title,
            workspaceName: workspace.name,
            workspaceShortId: workspace.shortId,
            cover: board.cover,
            permalink: board.permalink,
            members: board.members,
            cardsCount: board.cardsCount
          });
        });
      }
    });

    // Return all boards
    res.status(200).json(workspaceBoards);
  } catch (error) {
    console.error('Error fetching boards:', error);
    res.status(500).json({ message: error.message });
  }
};


const getBoardLists = async (req, res) => {
  const { boardId } = req.params;
  const { includeCards = 'true' } = req.query; // Default to true for backward compatibility
  const shouldIncludeCards = includeCards === 'true';

  try {
    // Create populate configuration based on includeCards parameter
    const populateConfig = {
      path: 'actionLists',
      match: { archived: false }, // Filter out archived lists
      select: 'title cards order', // Include the order field for sorting
      options: { sort: { order: 1 } }, // Sort by order
    };

    // Only populate cards if includeCards is true
    if (shouldIncludeCards) {
      populateConfig.populate = {
        path: 'cards',
        select: 'shortId order', // Include order for sorting cards
        match: { archived: false }, // Filter out archived cards
        options: { sort: { order: 1 } }, // Sort cards by order
      };
    }

    const board = await Board.findOne({ shortId: boardId }).populate(populateConfig);

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    res.status(200).json(board.actionLists);
  } catch (error) {
    console.error('Error fetching action lists:', error);
    res.status(500).json({ message: error.message });
  }
};


const createBoard = async (req, res) => {
  const { title, workspaceId, shortId, coverUrl, visibility } = req.body;
  const userId = req.user.id; // Assuming you have the user's ID in req.user from the authentication middleware


  try {
    // Validate coverUrl if provided
    let cover = null;

    if (coverUrl) {
      if (!isValidUrl('unsplash', coverUrl) && !isValidUrl('aws', coverUrl) && !isValidUrl('cloud', coverUrl)) {
        return res.status(400).json({ message: 'Invalid cover URL provided.' });
      }

      const fetchImageBuffer = async (url) => {
        return await fetch(url)
          .then(res => res.arrayBuffer())
          .then(buf => Buffer.from(buf));
      };

      const generateCoverColors = async (imageBuffer) => {
        const palette = await ColorThief.getPalette(imageBuffer, 5);
        return palette.map(color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`);
      };

      if (isValidUrl('aws', coverUrl) || isValidUrl('cloud', coverUrl)) {
        const imageBuffer = await fetchImageBuffer(coverUrl);
        const coverColors = await generateCoverColors(imageBuffer);

        cover = {
          url: coverUrl,
          coverColor: coverColors,
          sizes: null,
        };
      }
      else if (isValidUrl('unsplash', coverUrl)) {
        const imageName = getUnsplashImageName(coverUrl);
        const processedUrl = getUnsplashImageUrl(coverUrl);
        const imageBuffer = await fetchImageBuffer(processedUrl);

        const fileHash = crypto.createHash('sha256').update(imageBuffer).digest('hex');
        const fileName = `${imageName}.webp`;
        const s3Key = `backgrounds/2560x1707/${fileHash}/${fileName}`;
        const bucketName = 'background-shared';

        let background = await Background.findOne({ fileHash });

        if (!background) {
          const folderName = `backgrounds/2560x1707/${fileHash}`;
          const fileUrl = await uploadUrlToS3({
            url: processedUrl,
            folderName,
            bucketName,
            fileName,
            isSharp: false,
            width: 2560,
            fit: 'cover',
          });

          const signedFileUrl = getFileUrlFromS3({
            bucketName,
            region: process.env.BUCKET_REGION,
            key: s3Key,
          });

          const coverColors = await generateCoverColors(imageBuffer);

          const sizes = {
            thumbnail: { width: 150, height: 100 },
            medium: { width: 640, height: 426 },
            large: { width: 1280, height: 853 },
          };

          const resizedUrls = await Promise.all(Object.entries(sizes).map(async ([sizeName, { width, height }]) => {
            const resizedUrl = await resizeAndUpload(
              processedUrl,
              fileHash,
              fileName,
              sizeName,
              width,
              height,
              'cover'
            );
            return { [sizeName]: resizedUrl };
          }));

          const sizesObject = resizedUrls.reduce((acc, size) => ({ ...acc, ...size }), { original: fileUrl });

          background = new Background({
            fileHash,
            fileName,
            sizes: sizesObject,
            coverColors,
          });

          await background.save();

          cover = {
            name: fileName,
            url: signedFileUrl,
            coverColor: coverColors,
            sizes: sizesObject,
          };
        } else {
          const signedFileUrl = getFileUrlFromS3({
            bucketName,
            region: process.env.BUCKET_REGION,
            key: s3Key,
          });
          cover = {
            name: fileName,
            url: signedFileUrl,
            coverColor: background.coverColors,
            sizes: background.sizes,
          };
        }
      }
    }

    const workspace = await Workspace.findById(workspaceId);
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Create the new board with the correct members structure
    const newBoard = new Board({
      title,
      workspace: workspaceId,
      wShortId: workspace.shortId,
      members: [{ user: userId, role: 'admin', status: 'accepted' }],
      cover, // Add the cover to the board if available
      visibility
    });

    await newBoard.save();

    // Add the new board to the workspace
    workspace.boards.push(newBoard._id);

    // Add the creator as a member of the workspace if not already a member
    if (!workspace.members.some(member => member.user.toString() === userId)) {
      workspace.members.push({ user: userId, role: 'member' });
    }

    await workspace.save();

    // Emit WebSocket event to the newly added member
    const io = req.app.get('socketio');
    workspace.members.forEach(member => {
      emitUserAction(io, member.user.toString(), ActionTypes.BOARD_CREATED, { workspaceId });
    });

    res.status(201).json({ message: 'Board created successfully', board: newBoard });
  } catch (error) {
    console.error('Error creating board:', error);
    res.status(500).json({ message: error.message });
  }
};




const getBoardById = async (req, res) => {
  const { boardId } = req.params;
  const userId = req.user.id;

  try {
    // Fetch user filters
    const user = await User.findById(userId).select('filters').lean();
    if (!user) return res.status(404).json({ message: 'User not found' });
    const filters = user.filters || {};

    const cacheKey = `board:${userId}:${boardId}:${JSON.stringify(filters)}`;
    const cachedBoard = await getCache(cacheKey);
    if (cachedBoard) return res.status(200).json(JSON.parse(cachedBoard));

    const cardFilter = { archived: false, ...buildCardFilter(filters) };



    // Fetch board data and populate necessary fields
    let board = await Board.findOne({ shortId: boardId })
      .populate({
        path: 'actionLists',
        match: { archived: false },
        options: { sort: { order: 1 } },
        select: 'title',
      })
      .select('-members -labels -attachments')
      .lean();

    if (!board) return res.status(404).json({ message: 'Board not found' });

    await Board.findOneAndUpdate(
      { shortId: boardId },
      { $set: { cardsCount: await Card.countDocuments({ board: boardId, archived: false }) } },
      { new: true }
    );

    // Return the board with updated cover URL
    res.status(200).json(board);
  } catch (error) {
    console.error('Error fetching board by ID:', error);
    res.status(500).json({ message: error.message });
  }
};



const updateBoard = async (req, res) => {
  const { boardId } = req.params;
  const { actionLists, title, visibility } = req.body; // Add other fields as needed

  try {
    const board = await Board.findOne({ shortId: boardId });

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Update title if provided
    if (title) {
      board.title = title;
    }

    // Update visibility if provided
    if (visibility) {
      board.visibility = visibility;
    }

    // Update action lists and card orders if provided
    if (actionLists && Array.isArray(actionLists)) {
      board.actionLists.forEach((existingList) => {
        const updatedList = actionLists.find((list) => list._id === existingList._id.toString());
        if (updatedList) {
          existingList.cards = updatedList.cards; // Update cards with the new order
        }
      });
    }

    // Save only the updated fields
    await board.save();

    res.status(200).json(board);
  } catch (error) {
    console.error('Error updating board:', error);
    res.status(500).json({ message: 'Error updating board' });
  }
};



const updateBoardOrder = async (req, res) => {
  const { boardId } = req.params;
  const { actionListOrders } = req.body; // Receive orders

  try {
    // Check if board exists
    const boardExists = await Board.exists({ shortId: boardId });
    if (!boardExists) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Perform bulk update for action list orders
    const bulkOps = actionListOrders.map(({ _id, order }) => ({
      updateOne: { filter: { _id }, update: { order } }
    }));

    await ActionList.bulkWrite(bulkOps);

    // Respond with success
    res.status(200).json({ message: 'Order updated successfully' });
  } catch (error) {
    console.error('Error updating board order:', error);
    res.status(500).json({ message: 'Error updating board order' });
  }
};

const updateCardsOrder = async (req, res) => {
  const { sourceListId, cardId, targetListId, targetPosition } = req.body;

  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Find the source list and its cards
    const sourceList = await ActionList.findOne({ cards: cardId })
      .session(session)
      .populate({
        path: 'cards',
        match: { archived: false }, // Filter out archived cards
        select: '_id', // Fetch only necessary fields
      })
      .select('cards title');

    if (!sourceList) {
      await session.abortTransaction();
      return res.status(404).json({ message: 'Source list not found' });
    }

    const isSameList = sourceList._id.equals(targetListId);

    // Remove the card ID from the source list array
    const updatedSourceCards = sourceList.cards.map(c => c._id.toString());
    updatedSourceCards.splice(updatedSourceCards.indexOf(cardId), 1);

    let targetListCards = updatedSourceCards;

    const targetList = await ActionList.findById(targetListId).session(session).select('cards title');


    if (!isSameList) {
      if (!targetList) {
        await session.abortTransaction();
        return res.status(404).json({ message: 'Target list not found' });
      }
      targetListCards = targetList.cards.map(id => id.toString());
    }

    // Insert the card at the target position in the target list
    targetListCards.splice(targetPosition, 0, cardId);

    // Perform update operations
    const updates = [
      ActionList.updateOne(
        { _id: sourceList._id },
        { cards: updatedSourceCards },
        { session }
      ),
    ];

    if (!isSameList) {
      updates.push(
        ActionList.updateOne(
          { _id: targetListId },
          { cards: targetListCards },
          { session }
        )
      );
      // Update the card's actionList field
      updates.push(
        Card.findByIdAndUpdate(cardId, { actionList: targetListId }, { session })
      );
    }

    await Promise.all(updates);

    // Prepare bulk updates for card orders
    const bulkUpdates = [
      ...updatedSourceCards.map((id, index) => ({
        updateOne: { filter: { _id: id }, update: { order: index } },
      })),
    ];

    if (!isSameList) {
      bulkUpdates.push(
        ...targetListCards.map((id, index) => ({
          updateOne: { filter: { _id: id }, update: { order: index } },
        }))
      );
    }

    // Perform bulk write for all cards
    await Card.bulkWrite(bulkUpdates, { session });
    await session.commitTransaction();

    const card = await Card.findOne({ _id: cardId });
    emitIOCardAction(req, card?.board, ActionTypes.CARD_MOVED, { boardId: card?.board, targetListId, targetPosition, card });

    res.status(200).json({ message: 'Card moved successfully' });

    // **ASYNC NOTIFICATIONS**
    (async () => {
      try {
        const initiatorName = req.user?.username || req.user?.name || "Someone";
        const sourceListTitle = sourceList.title;
        const targetListTitle = targetList.title;

        const notificationMessage = isSameList
          ? `${initiatorName} repositioned a card in ${sourceListTitle}`
          : `${initiatorName} moved a card from ${sourceListTitle} to ${targetListTitle || 'another list'}`;

        // Notify watchers
        const watchers = card?.watchers?.map(w => w._id.toString()) || [];
        await sendAppNotification({
          recipients: watchers,
          initiatorId: req.user.id,
          targetType: "Card",
          targetId: cardId,
          actionType: "card_moved",
          message: notificationMessage,
          io: req.app.get("socketio"),
        });

        // Push notifications
        await Promise.all(
          watchers.map((memberId) =>
            sendPushNotificationHandler({
              memberId,
              type: "CARD_MOVED",
              data: { cardId: card.shortId, sourceListTitle, targetListTitle, username: req.user.username },
              initiator: req.user.name || "Someone",
            })
          )
        );
      } catch (err) {
        console.error("Notification Error:", err);
      }
    })();
  } catch (error) {
    await session.abortTransaction();
    res.status(500).json({ message: 'Error moving card', error: error.message });
  } finally {
    session.endSession();
  }
};




// Delete a board
const deleteBoard = async (req, res) => {
  const { boardId } = req.params;

  try {
    const board = await Board.findByIdAndDelete(boardId);

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Optionally, you might want to delete or update related action lists and cards
    await ActionList.deleteMany({ board: boardId });

    res.status(200).json({ message: 'Board deleted successfully' });
  } catch (error) {
    console.error('Error deleting board:', error);
    res.status(500).json({ message: error.message });
  }
};

// Archive a board
const archiveBoard = async (req, res) => {
  const { boardId } = req.params;

  try {
    const board = await Board.findOneAndUpdate(
      { shortId: boardId },
      { archived: true },
      { new: true }
    );

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    res.status(200).json({ message: 'Board archived successfully', board });
  } catch (error) {
    console.error('Error archiving board:', error);
    res.status(500).json({ message: error.message });
  }
};// Update the lastViewed field for the board

const updateBoardVisibility = async (req, res) => {
  const { boardId } = req.params;
  const { visibility } = req.body;

  // Validate visibility input
  const validVisibilities = ['Private', 'Workspace', 'Organization', 'Public'];
  if (!validVisibilities.includes(visibility)) {
    return res.status(400).json({ message: 'Invalid visibility option' });
  }

  try {
    // Find and update the board's visibility
    const board = await Board.findOneAndUpdate(
      { shortId: boardId },
      { visibility },
      { new: true }
    );

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    res.status(200).json({ message: 'Visibility updated successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

const lastViewedBoard = async (req, res) => {
  const { boardId } = req.params;
  const userId = req.user.id; // Extract user ID from the token

  try {
    const board = await Board.findOneAndUpdate(
      { shortId: boardId },
      { lastViewed: new Date() }, // Set lastViewed to the current date
      { new: true }
    );

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const _boardId = board._id;

    // Ensure recentBoards is an array
    if (!Array.isArray(user.recentBoards)) {
      user.recentBoards = [];
    }

    // Remove the board if it already exists in the recentBoards array
    user.recentBoards = user.recentBoards.filter(id => !id.equals(_boardId));

    // Add the board to the front of the list
    user.recentBoards.unshift(_boardId);

    // Limit recentBoards to the last 10 viewed boards
    if (user.recentBoards.length > 10) {
      user.recentBoards = user.recentBoards.slice(0, 10);
    }

    await user.save();

    res.status(200).json({ message: 'Board last viewed updated successfully' });
  } catch (error) {
    console.error('Error updating last viewed date:', error);
    res.status(500).json({ message: error.message });
  }
};


// Get the most recently viewed boards
const getLastViewedBoard = async (req, res) => {
  try {
    const userId = req.user.id; // Extract user ID from the token

    const user = await User.findById(userId).populate({
      path: 'recentBoards',
      select: '_id shortId title visibility createdAt updatedAt cover members',
      populate: {
        path: 'members.user',
        select: '_id name email username profilePicture online isPremiumMember canSeeOnlineStatus', // Select the fields you want to include for the user
      },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const recentBoards = user.recentBoards;

    if (!recentBoards || recentBoards.length === 0) {
      return res.status(404).json({ message: 'No recently viewed boards found' });
    }

    // Fetch card count for each board
    const boardsWithCardCount = await Promise.all(
      recentBoards.map(async (board) => {
        const cardCount = await Card.countDocuments({
          board: board.shortId, // Filter by board shortId
          archived: false, // Ensure the card is not archived
        });

        // Extract only user data from members, excluding null values or members without _id
        const members = board.members
          .filter(member => member.user && member.user._id)
          .map(member => member.user);

        return {
          ...board.toObject(), // Convert Mongoose document to plain object
          cardCount,
          members, // Replace members with the extracted user data
        };
      })
    );

    res.status(200).json(boardsWithCardCount);
  } catch (error) {
    console.error('Error fetching recently viewed boards:', error);
    res.status(500).json({ message: error.message });
  }
};


const getLabelsFromBoard = async (req, res) => {
  const { boardId } = req.params;

  try {
    const board = await Board.findOne({ shortId: boardId });

    if (!board) {
      return res.status(404).send({ error: 'Card not found' });
    }

    const enabledLabels = board.labels;

    res.status(200).send({ labels: enabledLabels });
  } catch (error) {
    res.status(500).send({ error: error.message });
  }
};


const addLabelToBoard = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { text, color } = req.body;

    const board = await Board.findOne({ shortId: boardId });
    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    board.labels.push({ text, color });
    await board.save();

    res.status(201).json(board);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


const updateLabelOnBoard = async (req, res) => {
  try {
    const { boardId, labelId } = req.params;
    const { text, color } = req.body;

    // Find the board
    const board = await Board.findOne({ shortId: boardId });

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Find the label to update
    const label = board.labels.id(labelId);
    if (!label) {
      return res.status(404).json({ message: 'Label not found' });
    }

    // Update the label on the board
    label.text = text;
    label.color = color;
    await board.save();

    // Update the label in all associated cards
    await Card.updateMany(
      { board: board.shortId, 'labels._id': labelId },
      {
        $set: {
          'labels.$.text': text,
          'labels.$.color': color,
        },
      }
    );

    res.status(200).json({ message: 'Label updated successfully on board and cards', board });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


const deleteLabelFromBoard = async (req, res) => {
  try {
    const { boardId, labelId } = req.params;

    // Remove the label from the board
    const board = await Board.findOneAndUpdate(
      { shortId: boardId },
      { $pull: { labels: { _id: labelId } } }, // Pull the label by its _id
      { new: true }
    );

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Remove the label from all associated cards
    await Card.updateMany(
      { board: board.shortId },
      { $pull: { labels: { _id: labelId } } }
    );

    res.status(200).json({ message: 'Label deleted successfully from board and cards', board });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};



const getBoardMembers = async (req, res) => {
  const { boardId } = req.params;
  try {
    const board = await Board.findOne({ shortId: boardId })
      .populate({
        path: 'members.user', // Path to populate within each member
        select: 'name email username profilePicture bio online isPremiumMember canSeeOnlineStatus' // Specify the fields you want to include
      });

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Map the populated members to only include the necessary fields
    const populatedMembers = board.members.map(member => {

      if (member.user && member.user._id) {
        return {
          _id: member.user._id,
          name: member.user.name,
          email: member.user.email,
          username: member.user.username,
          online: member.user.online,
          profilePicture: member.user.profilePicture,
          bio: member.user.bio,
          isPremiumMember: member.user.isPremiumMember,
          canSeeOnlineStatus: member.user.canSeeOnlineStatus,
          role: member.role // Include role from workspace members array
        };
      }
    });

    res.status(200).json(populatedMembers);
  } catch (error) {
    console.error('Error fetching board members by ID:', error);
    res.status(500).json({ message: error.message });
  }
};

// helpers/addMembers.js
const getBoardAndWorkspace = async (boardId) => {
  const board = await Board.findOne({ shortId: boardId });
  if (!board) throw new Error('Board not found');

  const workspace = await Workspace.findById(board.workspace).populate('members.user');
  if (!workspace) throw new Error('Workspace not found');

  return { board, workspace };
};

const checkMemberLimit = (workspace, members) => {
  const currentPlan = workspace?.subscription?.planType || 'Free';
  const memberLimit = SUBSCRIPTION_LIMITS[currentPlan];

  const existingMemberIds = [
    ...workspace.members.map(m => m?.user?.toString()),
    ...workspace.guests?.map(g => g?.toString()) || []
  ];

  const newMembersCount = members.filter(({ memberId }) =>
    memberId && !existingMemberIds.includes(memberId)).length;

  if (existingMemberIds.length + newMembersCount > memberLimit) {
    throw new Error(`Your ${currentPlan} plan allows up to ${memberLimit} members. Upgrade your plan to add more.`);
  }

  return existingMemberIds;
};

const prepareNewMembers = (workspace, board, members, currentMemberName, message, boardId) => {
  const workspaceMemberRoles = new Map(
    workspace.members.map(member => [member?.user?._id.toString(), member?.role])
  );
  const existingBoardMembers = new Set(board.members.map(m => m.user.toString()));
  const existingWorkspaceGuests = new Set(workspace.guests.map(g => g.toString()));

  const newWorkspaceGuests = [];
  const newBoardMembers = [];
  const notificationTasks = [];

  let inviteSent = false;
  const invitedEmails = [];

  for (const { memberId, email, role } of members) {
    const workspaceRole = workspaceMemberRoles.get(memberId) || 'guest';
    const isGuest = workspaceRole === 'guest';

    const subscription = workspace.subscription;
    const reservedSeats = subscription?.reservedSeats || 0;
    const maxAllowed = SUBSCRIPTION_LIMITS[subscription?.planType || 'Free'];
    const currentTotalCount = workspace.members.length + workspace.guests.length;

    if (!memberId) {
      if (email) {
        const inviteToken = generateInviteToken({ email, shortId: boardId, role, expiresIn: '7d' });

        notificationTasks.push(() =>
          sendInvitationEmail({
            to: email,
            shortId: boardId,
            inviteToken,
            name: board.title,
            message,
            invitePath: 'b',
            memberName: currentMemberName
          })
        );

        inviteSent = true;
        invitedEmails.push(email);
      }
      continue;
    }


    const isPro = subscription && ['Standard', 'Premium', 'Enterprise'].includes(subscription?.planType) && subscription?.status === 'active';

    if (isGuest && !existingWorkspaceGuests.has(memberId)) {
      let accessLevel;
      if (isPro) {
        if (currentTotalCount < reservedSeats) {
          accessLevel = 'full';
        } else if (currentTotalCount < maxAllowed) {
          accessLevel = 'limited';
        } else {
          accessLevel = 'view-only';
        }
      } else {
        if (currentTotalCount >= maxAllowed) continue;
        accessLevel = 'limited';
      }

      newWorkspaceGuests.push({ _id: memberId, accessLevel });
      existingWorkspaceGuests.add(memberId);
    }




    const existingMember = board.members.find(bm => bm.user.toString() === memberId);
    if (!existingMember) {
      newBoardMembers.push({ user: memberId, role, workspaceRole, status: 'accepted' });
      notificationTasks.push(() =>
        sendJoinedEmail(email, boardId, board.title, message, 'b', currentMemberName)
      );
      existingBoardMembers.add(memberId);
    } else {
      if (existingMember.role !== role) existingMember.role = role;
      if (existingMember.workspaceRole !== workspaceRole) existingMember.workspaceRole = workspaceRole;
    }
  }

  return {
    newWorkspaceGuests,
    newBoardMembers,
    notificationTasks,
    inviteSent,
    invitedEmails
  };
};

const sendNotifications = (tasks) => {
  tasks.forEach(task => setTimeout(task, 0));
};

const addMemberToBoard = async (req, res) => {
  const { boardId, members, message } = req.body;
  const currentMemberName = req.user.name;

  if (!members || members.length === 0) {
    return res.status(400).json({ message: 'No members provided' });
  }

  try {
    const { board, workspace } = await getBoardAndWorkspace(boardId);
    const existingMemberIds = checkMemberLimit(workspace, members);

    const {
      newWorkspaceGuests,
      newBoardMembers,
      notificationTasks,
      inviteSent,
      invitedEmails
    } = prepareNewMembers(workspace, board, members, currentMemberName, message, boardId);

    if (inviteSent) {
      sendNotifications(notificationTasks);
      return res.status(200).json({ message: 'Invitations sent to new members.', invitedEmails });
    }

    await Promise.all([
      ...(newWorkspaceGuests.length ? [
        workspace.updateOne({ $addToSet: { guests: { $each: newWorkspaceGuests.map(g => g._id || g) } } })
      ] : []),
      ...(newBoardMembers.length ? [
        board.updateOne({ $push: { members: { $each: newBoardMembers } } })
      ] : [])
    ]);

    sendNotifications(notificationTasks);

    const populatedBoard = await Board.findOne({ shortId: boardId }).populate({
      path: 'members.user',
      select: 'username bio profilePicture name'
    });

    return res.status(200).json({
      message: newBoardMembers.length ? 'Members added to board.' : 'Members already exist on the board.',
      members: populatedBoard.members,
    });
  } catch (error) {
    console.error('Error adding members to board:', error);
    return res.status(500).json({ message: error.message });
  }
};



const removeMemberFromBoard = async (req, res) => {
  const { memberId } = req.body;
  const { boardId } = req.params; // Assuming boardId is in the route params

  try {
    // Find the board by its short ID
    const board = await Board.findOne({ shortId: boardId }).populate('workspace');
    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    const user = await User.findById(memberId);

    const workspaceId = board.workspace._id;

    // Check if the member is an admin
    const member = board.members.find((member) => member?.user?.toString() === memberId);

    if (!member) {
      return res.status(404).json({ message: 'Member not found on the board' });
    }

    // Count the number of current admins
    const adminCount = board.members.filter((member) => member.role === 'admin').length;

    // Prevent removal if the member is an admin and there are fewer than two admins remaining
    if (member.role === 'admin' && adminCount < 2) {
      return res.status(400).json({
        message: 'Cannot remove member. Boards must have at least one admin.',
      });
    }

    // Remove the member from the board's members list
    board.members = board.members.filter((member) => member?.user?.toString() !== memberId);

    // Ensure all remaining members meet the schema requirements
    board.members = board.members.filter((member) => member.user);

    await board.save();

    // Remove the member from all cards associated with this board
    await Card.updateMany({ board: board.shortId }, { $pull: { users: memberId } });

    // Check if the member is part of any other boards in the workspace
    const otherBoardsWithMember = await Board.find({
      'members.user': memberId,
      workspace: workspaceId,
    });

    if (otherBoardsWithMember.length === 0) {
      // Remove the member or guest from the workspace
      await Workspace.updateOne(
        { _id: workspaceId },
        {
          $pull: {
            guests: memberId, // Remove from guests array
          },
        }
      );
    }

    // Send a notification email
    sendRemoveFromEmail(user.email, board.title, 'b', req.user.name, board.shortLink);

    res.status(200).json({
      message:
        'Member removed from board, cards, and workspace (if no other boards have this member).',
    });
  } catch (error) {
    console.error('Error removing member from board and workspace:', error);
    res.status(500).json({ message: 'An error occurred while removing the member' });
  }
};


const updateMemberRole = async (req, res) => {
  try {
    // Destructure the inputs
    const { typeId, memberId, newRole } = req.body;

    // Validate inputs
    if (!typeId || !memberId || !newRole) {
      return res.status(400).json({ message: 'Invalid type ID, member ID, or role provided' });
    }

    // Find the board using typeId
    const board = await Board.findOne({ shortId: typeId });

    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Check if the member exists in the board
    const member = board.members.find((member) => member.user.toString() === memberId);

    if (!member) {
      return res.status(404).json({ message: 'Member not found in the board' });
    }

    // Count the number of current admins
    const adminCount = board.members.filter((member) => member.role === 'admin').length;

    // Prevent role change if the current member is an admin and the admin count is less than 2
    if (member.role === 'admin' && adminCount < 2 && newRole !== 'admin') {
      return res.status(400).json({
        message: 'Cannot change role. Boards must have at least one admin.',
      });
    }

    // Update the role
    member.role = newRole;
    await board.save();

    // Optionally emit an event
    const io = req.app.get('socketio');
    emitUserAction(io, memberId, ActionTypes.ROLE_UPDATED, { typeId, memberId, newRole });

    return res.status(200).json({
      message: 'Member role updated successfully',
      updatedMember: member,
    });
  } catch (error) {
    console.error('Error updating member role:', error);
    return res.status(500).json({ message: 'An internal server error occurred.' });
  }
};

// Invite via email
const sendInvitationRequest = async (req, res) => {
  const { boardId } = req.params;
  const { invitees, message } = req.body;
  const board = await Board.findOne({ shortId: boardId });
  const currentMemberName = req.user.name;

  if (!board) return res.status(404).json({ message: 'Board not found' });

  try {
    const invitePromises = invitees.map(async ({ email, role }) => {
      const inviteToken = jwt.sign(
        { id: req.user._id, boardId, email },
        process.env.JWT_SECRET,
        { expiresIn: '1d' }
      );

      // Check if the user is already on the board
      const user = await User.findOne({ email });
      if (user && !board.members.some(m => m.user.equals(user._id))) {
        board.members.push({ user: user._id, role, status: 'pending' });
      }

      await sendInvitationEmail({
        to: email,
        shortId: boardId,
        inviteToken,
        name: board.title,
        message,
        invitePath: 'b',
        memberName: currentMemberName
      });
    });

    await Promise.all(invitePromises);
    await board.save();

    res.status(200).json({ message: 'Invitations sent successfully' });
  } catch (error) {
    console.error('Error sending invitations:', error);
    res.status(500).json({ message: 'Failed to send invitations' });
  }
};


const acceptInvitationRequest = async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({ message: 'No token provided' });
  }

  try {
    // Decode the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if the token has already been used
    const isTokenUsed = await UsedToken.findOne({ token });
    if (isTokenUsed) {
      return res.status(400).json({ message: 'This token has already been used' });
    }

    // Mark the token as used
    await UsedToken.create({ token });

    const { shortId, email, role } = decoded;

    // Check if a user with this email exists
    let user = await User.findOne({ email });

    if (!user) {
      // Create a new user with a default password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('defaultPassword123', salt);

      user = await User.create({
        email,
        name: email.split('@')[0],
        username: email.split('@')[0],
        passwordHash: hashedPassword,
        verified: true, // Auto-verify since they're joining via invitation
      });
    }

    // Check if the board exists
    const board = await Board.findOne({ shortId: shortId });
    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Fetch the workspace associated with the board
    const workspace = await Workspace.findById(board.workspace);
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Add user to workspace if not already a member or guest
    const isWorkspaceMember = workspace.members.some(m => m.user.equals(user._id));
    const isWorkspaceGuest = workspace.guests.some(g => g.equals(user._id));

    if (!isWorkspaceMember && !isWorkspaceGuest) {
      await workspace.updateOne({ $addToSet: { guests: user._id } });
    }

    // Check if the user is already a board member
    const existingBoardMember = board.members.find(m => m.user.equals(user._id));
    if (existingBoardMember) {
      // Generate an access token for the user
      const accessToken = jwt.sign(
        { id: user._id },
        process.env.JWT_SECRET,
        { expiresIn: '1h' }
      );

      return res.status(200).json({
        message: 'You are already a member of this board',
        email,
        accessToken,
        isDefaultPasswordSet: user.isDefaultPasswordSet,

      });
    }

    // Add user to the board members
    await board.updateOne({ $push: { members: { user: user._id, role, status: 'accepted' } } });

    // Generate an access token for the user
    const accessToken = jwt.sign(
      { id: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    res.status(200).json({
      message: 'Successfully joined the board',
      email,
      accessToken,
      isDefaultPasswordSet: user.isDefaultPasswordSet,
    });
  } catch (error) {
    console.error('Error accepting invitation:', error.message);
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(400).json({ message: 'Invalid or expired token' });
    }
    res.status(500).json({ message: 'Failed to accept invitation' });
  }
};


// Generate or retrieve join link
const generateJoinLink = async (req, res) => {
  const { boardId } = req.params;

  try {
    // Fetch the board from the database
    const board = await Board.findOne({ shortId: boardId });
    if (!board) return res.status(404).json({ message: 'Board not found' });

    // Check if a join link already exists
    if (board.joinLink) {
      return res.status(200).json({ joinLink: board.joinLink });
    }

    // Generate a new join link without expiration
    const joinToken = jwt.sign({ boardId }, process.env.JWT_SECRET);
    const joinLink = `${process.env.SITE_URL}/invite/b/${boardId}?token=${joinToken}`;

    // Update the board with the new join link
    board.joinLink = joinLink;
    await board.save();

    res.status(200).json({ joinLink });
  } catch (error) {
    console.error('Error generating join link:', error);
    res.status(500).json({ message: 'Failed to generate join link' });
  }
};


// Delete join link
const deleteJoinLink = async (req, res) => {
  const { boardId } = req.params;

  try {
    const board = await Board.findOne({ shortId: boardId });
    if (!board) return res.status(404).json({ message: 'Board not found' });

    board.joinLink = null;
    await board.save();

    res.status(200).json({ message: 'Join link deleted' });
  } catch (error) {
    console.error('Error deleting join link:', error);
    res.status(500).json({ message: 'Failed to delete join link' });
  }
};

const joinByLink = async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({ message: 'No token provided' });
  }

  try {
    // Decode the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const { boardId, email } = decoded;

    // Verify the user making the request
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Ensure the token's email matches the user's email
    if (user.email !== email) {
      return res.status(400).json({ message: 'Email mismatch or invalid user' });
    }

    // Find the board and verify existence
    const board = await Board.findOne({ shortId: boardId });
    if (!board) {
      return res.status(404).json({ message: 'Board not found' });
    }

    // Fetch the workspace associated with the board
    const workspace = await Workspace.findById(board.workspace);
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Check if the user is already a workspace member or guest
    const isWorkspaceMember = workspace.members.some(m => m.user.equals(user._id));
    const isWorkspaceGuest = workspace.guests.some(g => g.equals(user._id));

    // Add the user as a workspace guest if not already a member or guest
    if (!isWorkspaceMember && !isWorkspaceGuest) {
      await workspace.updateOne({ $addToSet: { guests: user._id } });
    }

    // Check if the user is already a board member
    const existingBoardMember = board.members.find(m => m.user.equals(user._id));

    if (existingBoardMember) {
      return res.status(200).json({ message: 'You are already a member of this board' });
    }

    // Add the user to the board members
    await board.updateOne({ $push: { members: { user: user._id, role: 'member', status: 'accepted' } } });

    // Populate updated board members and return
    const populatedBoard = await Board.findOne({ shortId: boardId }).populate({
      path: 'members.user',
      select: 'username bio profilePicture name'
    });

    res.status(200).json({
      message: 'Successfully joined the board',
      members: populatedBoard.members,
    });
  } catch (error) {
    console.error('Error accepting invitation:', error.message);
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(400).json({ message: 'Invalid or expired token' });
    }
    res.status(500).json({ message: 'Failed to accept invitation' });
  }
};

const uploadBackground = async (req, res) => {
  const { boardId } = req.params;
  const file = req.file; // Get the uploaded file

  if (!file) {
    return res.status(400).send({ error: 'No file uploaded' });
  }

  try {
    const board = await Board.findOne({ shortId: boardId });
    if (!board) {
      return res.status(404).send({ error: 'Board not found' });
    }

    // Generate the hash using SHA-256
    const fileName = file.originalname; // Corrected to use originalname
    const fileHash = crypto.createHash('sha256').update(file.buffer).digest('hex');
    const s3Key = `backgrounds/2560x1707/${fileHash}/${fileName}`;
    const folderName = `backgrounds/2560x1707/${fileHash}`;
    const bucketName = 'background-shared';

    let background = await Background.findOne({ fileHash });

    let signedFileUrl;

    if (background) {
      // Use the existing background
      signedFileUrl = getFileUrlFromS3({ bucketName, region: process.env.BUCKET_REGION, key: s3Key });

    } else {
      // Upload the new background
      const fileUrl = await uploadFileToS3({
        file,
        folderName,
        bucketName,
        fileName,
        isSharp: false,
        width: 2560,
        fit: 'cover',
        req
      });

      signedFileUrl = getFileUrlFromS3({ bucketName, region: process.env.BUCKET_REGION, key: s3Key });

      // Extract color palette
      const palette = await ColorThief.getPalette(file.buffer, 5);
      const coverColors = palette.map(
        color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`
      );

      // Resize the image to different sizes
      const sizes = {
        thumbnail: { width: 150, height: 100 },
        medium: { width: 640, height: 426 },
        large: { width: 1280, height: 853 },
      };

      const resizedUrls = {};
      for (const [sizeName, { width, height }] of Object.entries(sizes)) {
        resizedUrls[sizeName] = await resizeAndUpload(
          file,
          fileHash,
          fileName,
          sizeName,
          width,
          height,
          'cover',
          'file'
        );
      }

      // Save the new background
      const backgroundData = {
        fileHash,
        fileName,
        sizes: {
          original: fileUrl,
          ...resizedUrls,
        },
        coverColors,
      };
      background = new Background(backgroundData);
      await background.save();
    }

    const expiresAt = new Date(Date.now() + FILE_EXPIRATION_TIME); // Set expiration 1 hour from now

    // Create attachment object with both url and signedUrl, and expiration time
    const attachment = {
      name: fileName,
      url: signedFileUrl, // Base URL (publicly accessible)
      signedUrl: signedFileUrl, // Signed URL (temporary access)
      expiresAt, // Expiration time for the signed URL
      boardId,
    };

    board.attachments.push(attachment);

    if (background?.coverColors?.length < 1) {
      // Extract color palette
      const palette = await ColorThief.getPalette(imageBuffer, 5);
      const coverColors = palette.map(
        color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`
      );

      background.coverColors = coverColors;

      await background.save({ fields: ['coverColors'] });
    }

    // Set the board cover
    board.cover = {
      name: fileName,
      url: signedFileUrl,
      coverColor: background.coverColors || board.cover?.coverColor || null,
      sizes: background.sizes, // Include the sizes from the Background model
    };


    await board.save();

    res.status(200).send(board.attachments);


  } catch (error) {
    console.error('Error uploading background:', error);
    res.status(500).send({ error: error.message });
  }
};

const uploadBackgroundFromUnsplash = async (req, res) => {
  let { url, boardId } = req.body;

  if (!url) {
    return res.status(400).send({ error: 'No URL provided' });
  }

  if (!isValidUrl('unsplash', url)) {
    return res.status(400).send({ error: 'No valid URL provided' });
  }

  const imageName = getUnsplashImageName(url);
  url = getUnsplashImageUrl(url);

  try {
    const imageBuffer = await fetch(url)
      .then(res => res.arrayBuffer())
      .then(buf => Buffer.from(buf));

    const fileHash = crypto.createHash('sha256').update(imageBuffer).digest('hex');
    const fileName = `${imageName}.webp`;
    const s3Key = `backgrounds/2560x1707/${fileHash}/${fileName}`;
    const bucketName = 'background-shared';
    const cloudFrontDomain = 'https://cloud.zoobbe.com';

    // Check if the background already exists
    let background = await Background.findOne({ fileHash });

    let signedFileUrl;

    if (!background) {
      // Upload the new background
      const folderName = `backgrounds/2560x1707/${fileHash}`;
      const fileUrl = await uploadUrlToS3({
        url,
        folderName,
        bucketName,
        fileName,
        isSharp: false,
        width: 2560,
        fit: 'cover',
      });

      signedFileUrl = `${cloudFrontDomain}/${s3Key}`;

      // Extract color palette
      const palette = await ColorThief.getPalette(imageBuffer, 5);
      const coverColors = palette.map(
        color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`
      );

      // Resize the image to different sizes
      const sizes = {
        thumbnail: { width: 150, height: 100 },
        medium: { width: 640, height: 426 },
        large: { width: 1280, height: 853 },
      };

      const resizedUrls = {};
      for (const [sizeName, { width, height }] of Object.entries(sizes)) {
        const dimension = `${width}x${height}`;
        const resizedS3Key = `backgrounds/${dimension}/${fileHash}/${fileName}`;
        await resizeAndUpload(
          url,
          fileHash,
          fileName,
          sizeName,
          width,
          height,
          'cover'
        );
        resizedUrls[sizeName] = `${cloudFrontDomain}/${resizedS3Key}`;
      }

      // Save the new background
      const backgroundData = {
        fileHash,
        fileName,
        sizes: {
          original: fileUrl,
          ...resizedUrls,
        },
        coverColors,
      };
      background = new Background(backgroundData);
      await background.save();
    } else {
      // Use the existing background
      signedFileUrl = `${cloudFrontDomain}/${s3Key}`;
    }

    // Add missing color palette to the background
    if (background.coverColors.length < 1) {
      const palette = await ColorThief.getPalette(imageBuffer, 5);
      const coverColors = palette.map(
        color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`
      );

      background.coverColors = coverColors;
      await background.save({ fields: ['coverColors'] });
    }

    if (boardId) {
      // Set the board cover
      const board = await Board.findOne({ shortId: boardId });
      if (!board) {
        return res.status(404).send({ error: 'Board not found of mine' });
      }

      board.cover = {
        name: fileName,
        url: signedFileUrl,
        coverColor: background.coverColors || board.cover?.coverColor || null,
        sizes: background.sizes, // Include the sizes from the Background model
      };

      await board.save();
      return res.status(200).send({ cover: board.cover });
    } else {
      // Return the background details when no boardId is provided
      return res.status(200).send({
        message: 'Background uploaded successfully',
        background: {
          url: signedFileUrl,
          coverColors: background.coverColors,
          sizes: background.sizes,
        },
      });
    }
  } catch (error) {
    console.error('Error uploading background:', error);
    res.status(500).send({ error: error.message });
  }
};



const resizeAndUpload = async (url, fileHash, fileName, sizeName, width, height, fit = 'cover', type = 'url') => {
  const folderName = `backgrounds/${width}x${height}/${fileHash}`;

  if (type === 'url') {
    return await uploadUrlToS3({
      url,
      folderName,
      bucketName: 'background-shared',
      fileName: fileName,
      isSharp: true,
      width,
      fit,
    });
  }
  else {
    return await uploadFileToS3({
      file: url,
      folderName,
      bucketName: 'background-shared',
      fileName: fileName,
      isSharp: true,
      width,
      fit,
      req
    });
  }

};


const updateBackground = async (req, res) => {
  const { boardId } = req.params;

  const { img } = req.body;

  if (!img.signedUrl) {
    return res.status(400).send({ error: 'No URL provided' });
  }

  try {

    // Extract the color palette from the uploaded image
    const imageBuffer = await fetch(img.signedUrl).then(res => res.arrayBuffer()).then(buf => Buffer.from(buf));
    const palette = await ColorThief.getPalette(imageBuffer, 5);
    const coverColors = palette.map(color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`);

    // Find the board by its ID
    const board = await Board.findOne({ shortId: boardId });

    if (!board) {
      return res.status(404).send({ error: 'Board not found' });
    }

    // Set the cover image and colors
    board.cover = {
      name: img.name,
      url: img.signedUrl, // Use signed URL for the cover image
      coverColor: coverColors,
      attachmentId: img._id, // Use the last attachment ID
    };

    // Save the board with the new attachment and cover
    await board.save();

    // Send response with updated attachments
    res.status(200).send(board.cover);

  } catch (error) {
    console.error('Error uploading background:', error);
    res.status(500).send({ error: error.message });
  }
};




const getUnsplashPhotos = async (req, res) => {

  const { UNSPLASH_ACCESS_KEY } = process.env;
  const { page = 1, limit = 30 } = req.query;


  if (!UNSPLASH_ACCESS_KEY) {
    return res.status(500).json({ success: false, message: 'Unsplash access key not configured' });
  }

  const count = 10; // Number of photos to fetch
  const url = `https://api.unsplash.com/collections/317099/photos?per_page=${limit}&order_by=latest&page=${page}&client_id=${UNSPLASH_ACCESS_KEY}`;

  https.get(url, (response) => {
    let data = '';

    // Collect data chunks
    response.on('data', (chunk) => {
      data += chunk;
    });

    // Handle the complete response
    response.on('end', () => {
      try {
        const photos = JSON.parse(data);

        res.status(200).json({ success: true, photos });
      } catch (error) {
        console.error('Error parsing Unsplash response:', error.message);
        res.status(500).json({ success: false, message: 'Failed to parse Unsplash photos' });
      }
    });
  }).on('error', (error) => {
    console.error('Error with HTTPS request:', error.message);
    res.status(500).json({ success: false, message: 'Failed to fetch Unsplash photos' });
  });
};

const getBackgroundPhotos = async (req, res) => {
  try {
    const { boardId } = req.params;


    // Validate input
    if (!boardId) {
      return res.status(400).json({ error: "Board ID is required" });
    }

    // Find the board and populate only the attachments field
    const board = await Board.findOne({ shortId: boardId }).select('attachments shortId');

    if (!board) {
      return res.status(404).json({ error: "Board not found" });
    }

    // Signed URL expiration time
    const signedUrlExpiration = 3600; // 1 hour in seconds

    // Iterate over attachments and check for expired signed URLs
    const updatedAttachments = await Promise.all(
      board.attachments.map(async (attachment) => {
        // Update the attachment with the new signed URL and expiration time
        attachment.signedUrl = attachment.url;

        return attachment;
      })
    );

    // Update only the attachments field without triggering the pre-save middleware for shortId
    board.attachments = updatedAttachments;

    // Instead of saving the entire board, update only the attachments field to prevent shortId change
    await board.updateOne({ $set: { attachments: updatedAttachments } });

    return res.status(200).json({ backgrounds: board.attachments });
  } catch (error) {
    console.error("Error fetching background photos:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const importTrelloBoards = async (req, res) => {
  try {
    const { boards, source, includeArchived, token } = req.body;
    const userId = req.user.id;

    if (!token) {
      return res.status(401).json({ message: "Missing Trello API token" });
    }

    const io = req.app.get('socketio');
    const importedBoards = [];

    const boardDataMap = [];
    let totalLists = 0;
    let totalCards = 0;

    // First: fetch all board data and count total items
    for (const board of boards) {
      const boardId = board.id;
      const workspaceId = board.workspaceId;

      const url = `https://api.trello.com/1/boards/${boardId}?lists=all&cards=all&members=all&key=${process.env.TRELLO_AUTH_API_KEY}&token=${token}`;
      const boardRes = await fetch(url);

      if (!boardRes.ok) {
        const errorText = await boardRes.text();
        console.error(`Failed to fetch board ${boardId}: ${errorText}`);
        importedBoards.push({ boardId, message: "Failed to fetch board", error: errorText });
        continue;
      }

      const boardData = await boardRes.json();

      if (!boardData.id) {
        importedBoards.push({ boardId, message: "Invalid board data", error: boardData });
        continue;
      }

      const listCount = (boardData.lists || []).filter(l => includeArchived || !l.closed).length;
      const cardCount = (boardData.cards || []).filter(c => includeArchived || !c.closed).length;

      totalLists += listCount;
      totalCards += cardCount;

      boardDataMap.push({ boardData, workspaceId });
    }

    const totalItems = boards.length + totalLists + totalCards;
    const processedCount = { count: 0 };

    io.to(userId).emit('importProgress', { message: 'Import started', progress: 0 });

    for (const { boardData, workspaceId } of boardDataMap) {
      const convertedBoard = convertTrelloExportToZoobbe(boardData, workspaceId);
      await processImportData(convertedBoard, workspaceId, includeArchived, userId, req, io, totalItems, processedCount);
      importedBoards.push({ boardId: boardData.id, message: "Board imported successfully" });
    }

    return res.status(201).json({
      message: "Trello import completed",
      boards: importedBoards,
    });

  } catch (error) {
    console.error("Error importing Trello boards:", error);
    if (!res.headersSent) {
      return res.status(500).json({ message: "Internal server error" });
    }
  }
};


const processImportData = async (importData, workspaceId, includeArchived, userId, req, io, totalItems, processedCount) => {
  const workspace = await Workspace.findById(workspaceId);
  if (!workspace) {
    throw new Error('Workspace not found');
  }

  let listOrder = 0;
  let boardData = {};
  let listData = {};

  const backgrounds = await Background.find();
  const cover = {
    name: backgrounds[0].fileName,
    url: backgrounds[0].sizes.large,
    coverColor: backgrounds[0].coverColors,
    sizes: backgrounds[0].sizes,
  };

  for (const item of importData) {
    if (!includeArchived && item.archived) continue;

    switch (item.import_type) {
      case 'workspace':
        workspace.name = item.name || workspace.name;
        workspace.description = item.description || workspace.description;
        await workspace.save();
        break;

      case 'board': {
        const newBoard = new Board({
          title: item.title,
          workspace: workspaceId,
          wShortId: workspace.shortId,
          importId: item.boardId || null,
          archived: item.archived || false,
          members: workspace.members || [],
          cover,
        });

        if (!newBoard.members.some(m => m.user.toString() === userId)) {
          newBoard.members.push({ user: userId, role: 'member' });
        }

        const savedBoard = await newBoard.save();
        boardData = savedBoard;

        workspace.boards.push(savedBoard._id);
        if (!workspace.members.some(m => m.user.toString() === userId)) {
          workspace.members.push({ user: userId, role: 'member' });
        }

        await workspace.save();

        processedCount.count++;
        io.to(userId).emit('importProgress', {
          type: 'Board',
          message: 'Board created',
          progress: (processedCount.count / totalItems) * 100,
          title: savedBoard.title,
          index: processedCount.count,
          length: totalItems,
        });
        break;
      }

      case 'actionlist': {
        const board = await Board.findById(boardData._id);
        if (!board) break;

        const actionList = new ActionList({
          title: item.title,
          board: boardData._id,
          boardShortId: boardData.shortId,
          importId: item.actionListId || null,
          archived: item.archived || false,
          order: item.order || listOrder,
          createdAt: item.createdAt || Date.now(),
        });

        const savedActionList = await actionList.save();
        board.actionLists.push(savedActionList._id);
        await board.save();
        listData = savedActionList;
        listOrder++;

        processedCount.count++;
        io.to(userId).emit('importProgress', {
          type: 'ActionList',
          message: 'List created',
          progress: (processedCount.count / totalItems) * 100,
          title: savedActionList.title,
          index: processedCount.count,
          length: totalItems,
        });
        break;
      }

      case 'card': {
        const actionListForCard = await ActionList.findById(listData._id);
        if (!actionListForCard) break;

        const card = new Card({
          title: item.title || '',
          description: item.description || '',
          actionList: actionListForCard._id,
          board: boardData.shortId,
          boardLink: boardData.permalink,
          order: item.order || 0,
          archived: item.archived || false,
          createdAt: item.createdAt || Date.now(),
          users: item.users || [],
          comments: item.comments || [],
          checklists: item.checklists || [],
          attachments: item.attachments || [],
          labels: item.labels || [],
          dueDate: {
            date: item.dueDate?.date || null,
            startDate: item.dueDate?.startDate || null,
            dueTime: item.dueDate?.dueTime || '',
            reminder: item.dueDate?.reminder || 'None',
            completed: item.dueDate?.completed || false,
            status: item.dueDate?.status || '',
          },
          watchers: item.watchers || [],
        });

        const savedCard = await card.save();
        actionListForCard.cards.push(savedCard._id);
        await actionListForCard.save();

        processedCount.count++;
        io.to(userId).emit('importProgress', {
          type: 'Card',
          message: 'Card created',
          progress: (processedCount.count / totalItems) * 100,
          title: savedCard.title,
          index: processedCount.count,
          length: totalItems,
        });
        break;
      }

      default:
        throw new Error(`Unknown import type: ${item.import_type}`);
    }
  }
};




module.exports = {
  getBoards,
  getBoardById,
  updateBoard,
  updateBoardOrder,
  getBoardLists,
  updateCardsOrder,
  createBoard,
  deleteBoard,
  archiveBoard,
  updateBoardVisibility,
  lastViewedBoard,
  getLastViewedBoard,
  addLabelToBoard,
  getLabelsFromBoard,
  updateLabelOnBoard,
  deleteLabelFromBoard,
  getBoardMembers,
  addMemberToBoard,
  removeMemberFromBoard,
  updateMemberRole,
  sendInvitationRequest,
  acceptInvitationRequest,
  generateJoinLink,
  deleteJoinLink,
  joinByLink,
  uploadBackground,
  updateBackground,
  uploadBackgroundFromUnsplash,
  getUnsplashPhotos,
  getBackgroundPhotos,
  importTrelloBoards
};
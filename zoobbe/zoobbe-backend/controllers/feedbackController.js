const Feedback = require('../models/Feedback');

// Controller: Add Feedback
const addFeedback = async (req, res) => {
    try {
        const { title, url, description, type, workspaceId } = req.body;

        if (!title || !url || !description || !type) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        if (!req.user || !req.user._id) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        const newFeedback = new Feedback({
            title,
            url,
            description,
            type,
            user: req.user._id, // Make sure this matches the model
        });

        await newFeedback.save();
        return res.status(201).json(newFeedback);
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Internal server error' });
    }
};


// Controller: Get All Feedbacks
const getFeedbacks = async (req, res) => {
    try {
        const feedbacks = await Feedback.find().populate('userId workspaceId');
        return res.status(200).json(feedbacks);
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Internal server error' });
    }
};

// Controller: Get Feedback by ID
const getFeedback = async (req, res) => {
    try {
        const feedback = await Feedback.findById(req.params.feedbackId).populate('userId workspaceId');

        if (!feedback) {
            return res.status(404).json({ message: 'Feedback not found' });
        }

        return res.status(200).json(feedback);
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Internal server error' });
    }
};

module.exports = { addFeedback, getFeedbacks, getFeedback };

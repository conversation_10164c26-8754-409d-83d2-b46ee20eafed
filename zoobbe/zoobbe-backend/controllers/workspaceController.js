const mongoose = require('mongoose');
const Workspace = require('../models/Workspace');
const User = require('../models/User');
const { emitUserAction, convertTrelloExportToZoobbe, convertFluentBoardToZoobbe, generateInviteToken, SUBSCRIPTION_LIMITS } = require('../utils/helper');
const { ActionTypes } = require('../sockets/ActionTypes');
const Board = require('../models/Board');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const ActionList = require('../models/ActionList');
const Card = require('../models/Card');

const fs = require('fs');
const path = require('path');
const { addPendingSeats, reserveSeats } = require('./billingController');
// No longer using Redis caching


// Fetch all workspaces
const getAllWorkspaces = async (req, res) => {
  try {
    const workspaces = await Workspace.find()
      .populate({
        path: 'boards',
        populate: {
          path: 'actionLists',
          populate: {
            path: 'cards'
          }
        }
      })
      .populate({
        path: 'members.user',
        select: 'name email username profilePicture bio online isPremiumMember canSeeOnlineStatus'
      });

    const transformedWorkspaces = workspaces.map(workspace => {
      // Filter out members with null user references and then map the valid ones
      const membersWithDetails = workspace.members
        .filter(member => member.user && member.user._id)
        .map(member => ({
          _id: member.user._id,
          name: member.user.name,
          email: member.user.email,
          username: member.user.username,
          online: member.user.online,
          profilePicture: member.user.profilePicture,
          bio: member.user.bio,
          isPremiumMember: member.user.isPremiumMember,
          canSeeOnlineStatus: member.user.canSeeOnlineStatus,
          role: member.role // Include role here
        }));

      return {
        ...workspace.toObject(),
        ownerId: workspace.ownerId,
        members: membersWithDetails
      };
    });

    res.status(200).json(transformedWorkspaces);
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    res.status(500).json({ message: error.message });
  }
};

const getWorkspaces = async (req, res) => {
  const userId = req.user.id;

  try {
    // Step 1: Fetch workspaces with minimal data first - only what's needed by the frontend
    const workspaces = await Workspace.find({
      $or: [{ ownerId: userId }, { 'members.user': userId }]
    })
      .populate({
        path: 'subscription',
        select: 'planType status' // Only select fields needed for UI display
      })
      .populate({
        path: 'boards',
        match: { archived: false },
        select: 'title shortId members permalink cover', // Include cover for board background
        populate: {
          path: 'members.user',
          select: 'name email username profilePicture online isPremiumMember canSeeOnlineStatus'
        }
      })
      .select('name shortId ownerId members') // Only select fields needed
      .hint({ 'members.user': 1 }) // Hint to use the index on members.user
      .lean();

    // Step 2: Prepare board IDs for batch card counting
    const allBoardShortIds = workspaces.flatMap(ws =>
      ws.boards.map(board => board.shortId)
    );

    // Step 3: Batch count cards for all boards in one aggregation query
    const cardCountsByBoard = await Card.aggregate([
      {
        $match: {
          board: { $in: allBoardShortIds },
          archived: false
        }
      },
      {
        $group: {
          _id: "$board",
          count: { $sum: 1 }
        }
      }
    ]).allowDiskUse(true); // Allow disk use for large datasets

    // Convert to a lookup map for faster access
    const cardCountsMap = {};
    cardCountsByBoard.forEach(item => {
      cardCountsMap[item._id] = item.count;
    });

    // Step 4: Transform workspace data efficiently
    const transformedWorkspaces = workspaces.map(workspace => {
      // Process workspace members once
      const membersWithDetails = workspace.members
        .filter(member => member.user && member.user._id)
        .map(member => ({
          _id: member.user._id,
          name: member.user.name,
          email: member.user.email,
          username: member.user.username,
          online: member.user.online,
          profilePicture: member.user.profilePicture,
          isPremiumMember: member.user.isPremiumMember,
          canSeeOnlineStatus: member.user.canSeeOnlineStatus,
          role: member.role
        }));

      // Process boards with the pre-fetched card counts
      let wCardsCount = 0;
      const boardsWithMembers = workspace.boards.map(board => {
        // Get card count from our map, default to 0 if not found
        const cardsCount = cardCountsMap[board.shortId] || 0;
        wCardsCount += cardsCount;

        // Process board members
        const boardMembers = board.members
          .filter(member => member.user && member.user._id)
          .map(member => ({
            _id: member.user._id,
            name: member.user.name,
            email: member.user.email,
            username: member.user.username,
            online: member.user.online,
            profilePicture: member.user.profilePicture,
            isPremiumMember: member.user.isPremiumMember,
            canSeeOnlineStatus: member.user.canSeeOnlineStatus,
            role: member.role
          }));

        return {
          ...board,
          members: boardMembers,
          cardsCount
        };
      });

      return {
        ...workspace,
        ownerId: workspace.ownerId,
        members: membersWithDetails,
        boards: boardsWithMembers,
        cardsCount: wCardsCount
      };
    });

    res.status(200).json(transformedWorkspaces);
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    res.status(500).json({ message: error.message });
  }
};
const getLiteWorkspaces = async (req, res) => {
  const userId = req.user.id;

  try {

    // Fetch workspaces with lean, optimized populate
    const workspaces = await Workspace.find({
      $or: [{ ownerId: userId }, { 'members.user': userId }]
    })
      .lean();
    // Converts documents to plain JavaScript objects

    res.status(200).json(workspaces);
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    res.status(500).json({ message: error.message });
  }
};

/**
 * Get guest workspaces for the current user
 * This is an optimized version that uses aggregation for card counting
 */
const getGuestWorkspaces = async (req, res) => {
  const userId = req.user.id;

  try {
    // Step 1: Fetch workspaces where the user is a guest with minimal data
    const workspaces = await Workspace.find({
      guests: userId
    })
      .populate({
        path: 'boards',
        match: { archived: false },
        select: 'title shortId members permalink cover', // Include cover for board background
        populate: {
          path: 'members.user',
          select: 'name email username profilePicture online isPremiumMember canSeeOnlineStatus'
        }
      })
      .select('name shortId ownerId members') // Only select fields needed
      .hint({ guests: 1 }) // Hint to use the index on guests field
      .lean();

    // Step 2: Filter boards where user is a member and collect their IDs
    const allBoardShortIds = [];

    workspaces.forEach(workspace => {
      workspace.boards = workspace.boards.filter(board => {
        const isBoardMember = board.members?.some(member =>
          member.user && member.user._id && member.user._id.toString() === userId
        );

        if (isBoardMember) {
          allBoardShortIds.push(board.shortId);
          return true;
        }
        return false;
      });
    });

    // If no boards are accessible, return early
    if (allBoardShortIds.length === 0) {
      return res.status(200).json([]);
    }

    // Step 3: Batch count cards for all boards in one aggregation query
    const cardCountsByBoard = await Card.aggregate([
      {
        $match: {
          board: { $in: allBoardShortIds },
          archived: false
        }
      },
      {
        $group: {
          _id: "$board",
          count: { $sum: 1 }
        }
      }
    ]).allowDiskUse(true); // Allow disk use for large datasets

    // Convert to a lookup map for faster access
    const cardCountsMap = {};
    cardCountsByBoard.forEach(item => {
      cardCountsMap[item._id] = item.count;
    });

    // Step 4: Transform workspace data efficiently
    // Filter out workspaces with no accessible boards
    const filteredWorkspaces = workspaces.filter(workspace => workspace.boards.length > 0);

    const transformedWorkspaces = filteredWorkspaces.map(workspace => {
      // Process workspace members once
      const membersWithDetails = workspace.members
        .filter(member => member.user && member.user._id)
        .map(member => ({
          _id: member.user._id,
          name: member.user.name,
          email: member.user.email,
          username: member.user.username,
          online: member.user.online,
          profilePicture: member.user.profilePicture,
          isPremiumMember: member.user.isPremiumMember,
          canSeeOnlineStatus: member.user.canSeeOnlineStatus,
          role: member.role
        }));

      // Process boards with the pre-fetched card counts
      let wCardsCount = 0;
      const boardsWithMembers = workspace.boards.map(board => {
        // Get card count from our map, default to 0 if not found
        const cardsCount = cardCountsMap[board.shortId] || 0;
        wCardsCount += cardsCount;

        // Process board members
        const boardMembers = board.members
          .filter(member => member.user && member.user._id)
          .map(member => ({
            _id: member.user._id,
            name: member.user.name,
            email: member.user.email,
            username: member.user.username,
            online: member.user.online,
            profilePicture: member.user.profilePicture,
            isPremiumMember: member.user.isPremiumMember,
            canSeeOnlineStatus: member.user.canSeeOnlineStatus,
            role: member.role
          }));

        return {
          ...board,
          members: boardMembers,
          cardsCount
        };
      });

      return {
        ...workspace,
        ownerId: workspace.ownerId,
        members: membersWithDetails,
        boards: boardsWithMembers,
        cardsCount: wCardsCount
      };
    });

    res.status(200).json(transformedWorkspaces);
  } catch (error) {
    console.error('Error fetching guest workspaces:', error);
    res.status(500).json({ message: error.message });
  }
};


const getWorkspacesUserId = async (req, res) => {
  const { userId } = req.params;

  try {
    const workspaces = await Workspace.find({ ownerId: userId })
      .populate({
        path: 'boards',
        populate: [
          {
            path: 'actionLists',
            populate: {
              path: 'cards',
              populate: {
                path: 'users',
                select: 'name email username profilePicture bio online isPremiumMember canSeeOnlineStatus'
              }
            }
          },
          {
            path: 'members',
            select: 'name email username profilePicture bio role online isPremiumMember canSeeOnlineStatus'
          }
        ]
      })
      .populate({
        path: 'members.user',
        select: 'name email username profilePicture bio role online isPremiumMember canSeeOnlineStatus'
      });

    const transformedWorkspaces = workspaces.map(workspace => {
      // Filter out members with null user references and then map the valid ones
      const membersWithDetails = workspace.members
        .filter(member => member.user && member.user._id)
        .map(member => ({
          _id: member.user._id,
          name: member.user.name,
          email: member.user.email,
          username: member.user.username,
          online: member.user.online,
          profilePicture: member.user.profilePicture,
          bio: member.user.bio,
          isPremiumMember: member.user.isPremiumMember,
          canSeeOnlineStatus: member.user.canSeeOnlineStatus,
          role: member.role // Include role here
        }));

      return {
        ...workspace.toObject(),
        ownerId: workspace.ownerId,
        members: membersWithDetails
      };
    });

    res.status(200).json(transformedWorkspaces);
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    res.status(500).json({ message: error.message });
  }
};


// Fetch a workspace by ID
const getWorkspaceById = async (req, res) => {
  const { workspaceId } = req.params;
  try {
    const workspace = await Workspace.findById(workspaceId)
      .populate({
        path: 'boards',
        populate: {
          path: 'actionLists',
          populate: {
            path: 'cards'
          }
        }
      })
      .populate({
        path: 'members.user',
        select: 'name email username profilePicture bio role online isPremiumMember canSeeOnlineStatus'
      });

    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Filter out members with null user references and then map the valid ones
    const membersWithDetails = workspace.members
      .filter(member => member.user && member.user._id)
      .map(member => ({
        _id: member.user._id,
        name: member.user.name,
        email: member.user.email,
        username: member.user.username,
        profilePicture: member.user.profilePicture,
        online: member.user.online,
        bio: member.user.bio,
        isPremiumMember: member.user.isPremiumMember,
        canSeeOnlineStatus: member.user.canSeeOnlineStatus,
        role: member.role // Include role here
      }));

    res.status(200).json({
      ...workspace.toObject(),
      ownerId: workspace.ownerId,
      members: membersWithDetails
    });
  } catch (error) {
    console.error('Error fetching workspace by ID:', error);
    res.status(500).json({ message: error.message });
  }
};


const getWorkspaceByShortName = async (req, res) => {
  const { shortName } = req.params; // Use correct parameter name
  try {
    const workspace = await Workspace.findOne({ shortName }).populate('boards');
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }
    res.status(200).json(workspace);
  } catch (error) {
    console.error('Error fetching workspace by shortName:', error);
    res.status(500).json({ message: error.message });
  }
};


// Fetch a workspace by User ID
const getWorkspaceByUserId = async (req, res) => {
  const { userId } = req.params;
  try {
    const workspaces = await Workspace.find({ 'members.user': userId }).populate({
      path: 'boards',
      populate: {
        path: 'actionLists',
        populate: {
          path: 'cards'
        }
      }
    });
    if (!workspaces.length) {
      return res.status(404).json({ message: 'Workspaces not found for this user' });
    }
    res.status(200).json(workspaces);
  } catch (error) {
    console.error('Error fetching workspaces by user ID:', error);
    res.status(500).json({ message: error.message });
  }
};

const createWorkspace = async (req, res) => {
  const { name, type, description } = req.body;
  const userId = req.user.id; // Assuming userId is extracted from the authenticated user's token

  try {
    // Create the new workspace with the provided data
    const newWorkspace = new Workspace({
      name,
      description,
      type,
      ownerId: userId, // Set the ownerId to the current user's ID
      members: [
        {
          user: userId,
          role: 'admin' // Set the role of the user to 'admin'
        }
      ]
    });

    // Save the workspace to the database
    await newWorkspace.save();

    // Update the user's workspaces array
    await User.findByIdAndUpdate(userId, {
      $push: { workspaces: newWorkspace._id }
    });

    res.status(201).json({ message: 'Workspace created successfully', workspace: newWorkspace });
  } catch (error) {
    console.error('Error creating workspace:', error);
    res.status(500).json({ message: error.message });
  }
};


// Delete a workspace
const deleteWorkspace = async (req, res) => {
  const { workspaceId } = req.params;

  try {
    const workspace = await Workspace.findById(workspaceId);

    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    await Workspace.findByIdAndDelete(workspaceId);

    res.status(200).json({ message: 'Workspace deleted successfully' });
  } catch (error) {
    console.error('Error deleting workspace:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Add member to workspace
const addMemberToWorkspace = async (req, res) => {
  try {
    const { workspaceId, members, message } = req.body;
    const currentMemberName = req.user.name;

    if (!workspaceId || !members || members.length === 0) {
      return res.status(400).json({ message: 'Invalid workspace ID or no members provided' });
    }

    const workspace = await Workspace.findOne({ shortId: workspaceId });
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }


    const subscription = workspace.subscription
      ? await Subscription.findById(workspace.subscription)
      : null;

    const currentPlan = subscription?.planType || 'Free';
    const memberLimit = SUBSCRIPTION_LIMITS[currentPlan];

    const existingMemberIds = [
      ...workspace.members.map(m => m.user.toString()),
      ...workspace.guests?.map(g => g.toString()) || []
    ];

    const newMembersCount = members.filter(({ memberId }) => memberId && !existingMemberIds.includes(memberId)).length;

    if (existingMemberIds.length + newMembersCount > memberLimit) {
      return res.status(403).json({
        message: `Your ${currentPlan} plan allows up to ${memberLimit} members. Upgrade your plan to add more.`,
      });
    }

    const isPro = subscription && ['Standard', 'Premium', 'Enterprise'].includes(currentPlan) && subscription.status === 'active';

    const newWorkspaceMembers = [];
    const notificationTasks = [];
    let inviteSent = false;
    let invitedEmails = [];

    for (const { memberId, email, role } of members) {

      if (!memberId) {
        if (email) {
          const inviteToken = generateInviteToken({ email, shortId: workspaceId, role, expiresIn: '7d' });

          notificationTasks.push(() => sendInvitationEmail({
            to: email,
            shortId: workspaceId,
            inviteToken,
            name: workspace.name,
            message,
            invitePath: 'w',
            memberName: currentMemberName
          }));

          inviteSent = true;
          invitedEmails.push(email);
        }
        continue;
      }

      const user = await User.findById(memberId);
      if (!user) {
        if (email) {
          const inviteToken = generateInviteToken({ email, shortId: workspaceId, role, expiresIn: '7d' });

          notificationTasks.push(() => sendInvitationEmail({
            to: email,
            shortId: workspaceId,
            inviteToken,
            name: workspace.name,
            message,
            invitePath: 'w',
            memberName: currentMemberName
          }));

          inviteSent = true;
          invitedEmails.push(email);
        }
        continue;
      }

      if (existingMemberIds.includes(memberId)) continue;

      const newMember = {
        user: memberId,
        role: role || 'member',
        accessLevel: 'limited' // default
      };

      const currentMemberCount = (workspace.members?.length || 0) + (workspace.guests?.length || 0);


      if (isPro) {
        const reservedSeats = subscription?.reservedSeats || 0;

        console.log(`[RESERVED_SEATS_DEBUG] Adding Member to Pro Workspace:`, {
          workspaceId: workspace._id,
          workspaceShortId: workspace.shortId,
          subscriptionId: subscription?._id,
          memberName: user.name,
          memberEmail: user.email,
          currentMemberCount,
          reservedSeats,
          pendingSeats: subscription?.pendingSeats || 0,
          totalSeats: subscription?.totalSeats || 0,
          freeLimit: SUBSCRIPTION_LIMITS['Free']
        });

        if (currentMemberCount < reservedSeats) {
          newMember.accessLevel = 'full'; // paid seat available
          console.log(`[RESERVED_SEATS_DEBUG] Member assigned FULL access - within reserved seats`);
        } else if (currentMemberCount < SUBSCRIPTION_LIMITS['Free']) {
          newMember.accessLevel = 'limited'; // unpaid, but within 15-member grace limit
          console.log(`[RESERVED_SEATS_DEBUG] Member assigned LIMITED access - within free limit`);
        } else {
          newMember.accessLevel = 'view-only'; // exceeded all limits
          console.log(`[RESERVED_SEATS_DEBUG] Member assigned VIEW-ONLY access - exceeded all limits, adding pending seat`);
          // Add pending seat for view-only members
          try {
            await addPendingSeats(workspace._id, 1, req.user.id, `Member ${user.name} added`);
          } catch (error) {
            console.error('Error adding pending seat:', error);
          }
        }

        workspace.members.push(newMember);
      } else {
        // Free plan: max 15 members, all limited access
        if (currentMemberCount >= SUBSCRIPTION_LIMITS['Free']) {
          continue; // reject silently or collect rejected list if needed
        }
        newMember.accessLevel = 'limited';
        workspace.members.push(newMember);
      }


      user.workspaces.push({ shortId: workspaceId, accessLevel: newMember.accessLevel });
      newWorkspaceMembers.push(memberId);

      notificationTasks.push(() =>
        sendJoinedEmail(user.email, workspaceId, workspace.name, message, 'w', currentMemberName)
      );
    }

    if (inviteSent) {
      notificationTasks.forEach(task => setTimeout(task, 0));
      return res.status(200).json({
        message: 'Invitations sent to new members.',
        invitedEmails,
      });
    }

    await workspace.save();

    const bulkOps = workspace.members
      .filter(m => newWorkspaceMembers.includes(m.user.toString()))
      .flatMap(m => [
        // Case 1: Workspace already exists, update accessLevel
        {
          updateOne: {
            filter: { _id: m.user, 'workspaces.shortId': workspaceId },
            update: {
              $set: {
                'workspaces.$.accessLevel': m.accessLevel
              }
            }
          }
        },
        // Case 2: Workspace not present, add it
        {
          updateOne: {
            filter: { _id: m.user, 'workspaces.shortId': { $ne: workspaceId } },
            update: {
              $push: {
                workspaces: {
                  shortId: workspaceId,
                  accessLevel: m.accessLevel
                }
              }
            }
          }
        }
      ]);

    if (bulkOps.length > 0) {
      await User.bulkWrite(bulkOps);
    }

    notificationTasks.forEach(task => setTimeout(task, 0));

    const fullMembers = await User.find({
      _id: { $in: workspace.members.map(member => member.user) },
    });

    return res.status(200).json({
      message: newWorkspaceMembers.length
        ? 'Members added to workspace.'
        : 'Members already exist in the workspace.',
      members: fullMembers,
      workspae: workspace
    });
  } catch (error) {
    console.error('Error adding members to workspace:', error);
    return res.status(500).json({ message: error.message });
  }
};


const updateMemberRole = async (req, res) => {
  try {
    const { workspaceId, memberId, newRole } = req.body;

    if (!workspaceId || !memberId || !newRole) {
      return res.status(400).json({ message: 'Invalid workspace ID, member ID, or role provided' });
    }

    // Find the workspace
    const workspace = await Workspace.findOne({ shortId: workspaceId });

    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    let userMoved = false;

    // Check if the user is in members
    const memberIndex = workspace.members.findIndex(
      (member) => member.user.toString() === memberId
    );

    // Check if the user is in guests
    const guestIndex = workspace.guests.findIndex(
      (guest) => guest.toString() === memberId
    );

    if (newRole === 'guest') {
      if (memberIndex !== -1) {
        // Move user from members to guests
        workspace.guests.push(workspace.members[memberIndex].user);
        workspace.members.splice(memberIndex, 1);
        userMoved = true;
      }
    } else {
      if (guestIndex !== -1) {
        // Move user from guests to members
        const user = workspace.guests[guestIndex];
        workspace.guests.splice(guestIndex, 1);
        workspace.members.push({ user, role: newRole });
        userMoved = true;
      } else if (memberIndex !== -1) {
        // Update role if user is already a member
        workspace.members[memberIndex].role = newRole;
        userMoved = true;
      }
    }

    if (!userMoved) {
      return res.status(404).json({
        message: 'User not found in workspace members or guests',
      });
    }

    // Update the workspaceRole in all boards
    const result = await Board.updateMany(
      {
        workspace: workspace._id,
        "members.user": memberId,
      },
      {
        $set: { "members.$[member].workspaceRole": newRole },
      },
      {
        arrayFilters: [{ "member.user": memberId }],
      }
    );


    // Save the workspace changes
    await workspace.save();

    // Optionally emit an event if needed
    const io = req.app.get('socketio');
    emitUserAction(io, memberId, ActionTypes.ROLE_UPDATED, { workspaceId, memberId, newRole });

    return res.status(200).json({
      message: 'Member role updated successfully in workspace and all boards',
      updatedRole: newRole,
    });
  } catch (error) {
    console.error('Error updating member role:', error);
    return res.status(500).json({ message: error.message });
  }
};



const removeMemberFromWorkspace = async (req, res) => {
  try {
    const { workspaceId } = req.params;
    const { memberId } = req.body; // Expecting a single member ID
    const requestingUserId = req.user.id; // The user making the request (workspace admin)

    if (!workspaceId || !memberId) {
      return res.status(400).json({ message: 'Invalid workspace ID or member ID not provided' });
    }

    const workspace = await Workspace.findOne({ shortId: workspaceId });
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    let updateResult = null;

    // Check if the member is a guest
    const isGuest = workspace.guests?.some(guestId => guestId.toString() === memberId);
    const user = await User.findById(memberId);

    if (isGuest) {
      // Find all boards where this guest is a member
      const boardsWithMember = await Board.find({
        workspace: workspace._id,
        'members.user': memberId
      });

      // Remove the member from workspace guests
      updateResult = await Workspace.updateOne(
        { shortId: workspaceId },
        { $pull: { guests: memberId } }
      );

      // For each board, check if there will be at least one admin after removing this guest
      for (const board of boardsWithMember) {
        // Remove the guest from the board
        await Board.updateOne(
          { _id: board._id },
          { $pull: { members: { user: memberId } } }
        );

        // Get updated board after removing the member
        const updatedBoard = await Board.findById(board._id);

        // Check if there's at least one admin in the board members
        const hasAdmin = updatedBoard.members.some(member => member.role === 'admin');

        // If no admin, make the requesting user (workspace admin) an admin of this board
        if (!hasAdmin) {
          // Check if requesting user is already a member of this board
          const isRequestingUserMember = updatedBoard.members.some(
            member => member.user.toString() === requestingUserId
          );

          if (isRequestingUserMember) {
            // Update existing member to admin role
            await Board.updateOne(
              { _id: board._id, 'members.user': requestingUserId },
              { $set: { 'members.$.role': 'admin' } }
            );
          } else {
            // Add requesting user as admin to the board
            await Board.updateOne(
              { _id: board._id },
              {
                $push: {
                  members: {
                    user: requestingUserId,
                    role: 'admin',
                    workspaceRole: 'admin' // Set workspace role as well
                  }
                }
              }
            );
          }
        }
      }
    } else {
      // Remove the workspace from the user's workspaces list
      await User.updateOne(
        { _id: memberId },
        { $pull: { workspaces: { shortId: workspaceId } } }
      );

      // Check if the user is a member of any boards in this workspace
      const boardsWithMember = await Board.find({
        workspace: workspace._id,
        'members.user': memberId
      });

      if (boardsWithMember.length > 0) {
        // User is still in boards, make them a workspace guest
        await Workspace.updateOne(
          { shortId: workspaceId },
          {
            $pull: { members: { user: memberId } },
            $addToSet: { guests: memberId }
          }
        );
      } else {
        // User is not in any boards, remove completely
        updateResult = await Workspace.updateOne(
          { shortId: workspaceId },
          { $pull: { members: { user: memberId } } }
        );

        if (updateResult?.modifiedCount === 0) {
          return res.status(404).json({ message: 'Member not found in workspace' });
        }
      }
    }

    // Handle seat reservation for Pro workspaces
    const subscription = await Subscription.findOne({ workspaceId: workspace._id });
    if (subscription && ['Standard', 'Premium', 'Enterprise'].includes(subscription.planType) && subscription.status === 'active') {
      try {
        // await reserveSeats(workspace._id, 1, requestingUserId, `Member ${user.name} removed`);
      } catch (error) {
        console.error('Error reserving seat:', error);
      }
    }

    // Retrieve updated workspace
    const updatedWorkspace = await Workspace.findOne({ shortId: workspaceId });

    // Emit WebSocket event
    const io = req.app.get('socketio');
    emitUserAction(io, memberId, ActionTypes.MEMBER_REMOVED, { workspaceId, memberId });

    res.status(200).json({ message: 'Member removed successfully', workspace: updatedWorkspace });
    sendRemoveFromEmail(user.email, workspace.name, 'w', req.user.name, workspace.shortLink);

  } catch (error) {
    console.error('Error removing member from workspace:', error);
    res.status(500).json({ message: error.message });
  }
};

// Ensure the directory exists
const ensureDirectoryExists = (dirPath, callback) => {
  fs.mkdir(dirPath, { recursive: true }, (err) => {
    if (err) {
      console.error(`Error creating directory: ${err}`);
    } else {
      console.log(`Directory created: ${dirPath}`);
    }
    if (callback) {
      callback(err);
    }
  });
};

// Delete a single file
const deleteFile = async (filePath) => {
  try {
    await fs.promises.unlink(filePath);
  } catch (err) {
    console.error(`Error deleting file: ${filePath}`, err);
  }
};

// Delete all files in a directory but keep the directory itself
const deleteFilesInDirectory = async (dirPath) => {
  try {
    const files = await fs.promises.readdir(dirPath);

    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = await fs.promises.stat(filePath);

      if (stats.isDirectory()) {
        // If you have nested directories and want to delete their files as well, uncomment the line below.
        // await deleteFilesInDirectory(filePath);
      } else {
        await deleteFile(filePath); // Delete file
      }
    }

    console.log(`All files deleted in directory: ${dirPath}`);
  } catch (err) {
    console.error(`Error deleting files in directory: ${dirPath}`, err);
  }
};

const { Readable } = require('stream');
const { sendInvitationEmail, sendJoinedEmail, sendRemoveFromEmail } = require('../mailer');
const UsedToken = require('../models/UsedToken');
const { getFileFromS3 } = require('../utils/s3Utils');
const Subscription = require('../models/Subscription');



const processImportData = async (importData, workspaceId, isImportMembers, userId, req, io) => {
  // Find the workspace
  const workspace = await Workspace.findById(workspaceId);
  if (!workspace) {
    throw new Error('Workspace not found');
  }

  // Process each import item
  let listOder = 0;
  let index = 1;
  let boardData = {};
  let listData = {};
  for (const item of importData) {
    switch (item.import_type) {

      case 'workspace':
        // Update workspace details
        workspace.name = item.name || workspace.name;
        workspace.description = item.description || workspace.description;
        await workspace.save();

        break;

      case 'board':
        // Create new board
        const newBoard = new Board({
          title: item.title,
          workspace: workspaceId,
          wShortId: workspace.shortId,
          importId: item.boardId || null,
          archived: item.archived || false,
          members: isImportMembers ? (workspace.members || []) : [],
        });


        // Add importing user as a member
        if (!newBoard.members?.some(member => member.user.toString() === userId)) {
          newBoard.members.push({ user: userId, role: 'member' });
        }

        const savedBoard = await newBoard.save();
        workspace.boards.push(savedBoard._id);

        if (!workspace.members?.some(member => member.user.toString() === userId)) {
          workspace.members.push({ user: userId, role: 'member' });
        }

        boardData = savedBoard;

        await workspace.save();

        io.to(userId).emit('importProgress', {
          type: 'Board',
          message: 'Board created successfully',
          progress: (index / importData.length) * 100,
          title: savedBoard.title,
          index: index,
          length: importData.length

        });

        break;

      case 'actionlist':
        // Create new action list
        const board = await Board.findById(boardData._id);

        if (!board) {
          console.log('Board not found', item.boardId);
        }

        const actionList = new ActionList({
          title: item.title,
          board: boardData._id,
          boardShortId: boardData.shortId,
          importId: item.actionListId || null,
          archived: item.archived || false,
          order: item.order || listOder,
          createdAt: item.createdAt || Date.now(),
        });

        const savedActionList = await actionList.save();
        board.actionLists.push(savedActionList._id);
        await board.save();

        listData = savedActionList;

        listOder++;

        io.to(userId).emit('importProgress', {
          type: 'ActionList',
          message: 'Actionlist created successfully',
          progress: (index / importData.length) * 100,
          title: savedActionList.title,
          index: index,
          length: importData.length

        });

        break;

      case 'card':
        // Create a new card
        const actionListForCard = await ActionList.findById(listData._id);
        if (!actionListForCard) {
          throw new Error('Action list not found');
        }

        const card = new Card({
          title: item.title || '',
          description: item.description || '',
          actionList: actionListForCard._id,
          board: boardData.shortId,
          boardLink: boardData.permalink,
          order: item.order || 0,
          archived: item.archived || false,
          createdAt: item.createdAt || Date.now(),
          users: item.users || [],
          comments: item.comments || [],
          checklists: item.checklists || [],
          attachments: item.attachments || [],
          labels: item.labels || [],
          dueDate: {
            date: item.dueDate?.date || null,
            startDate: item.dueDate?.startDate || null,
            dueTime: item.dueDate?.dueTime || '',
            reminder: item.dueDate?.reminder || 'None',
            completed: item.dueDate?.completed || false,
            status: item.dueDate?.status || '',
          },
          watchers: item.watchers || [],
        });

        const savedCard = await card.save();
        actionListForCard.cards.push(savedCard._id);
        await actionListForCard.save();

        io.to(userId).emit('importProgress', {
          type: 'Card',
          message: 'Card created successfully',
          progress: (index / importData.length) * 100,
          title: savedCard.title,
          index: index,
          length: importData.length

        });

        break;

      default:
        throw new Error(`Unknown import type: ${item.import_type}`);
    }

    index++;
  }
};



const importBoard = async (req, res) => {
  try {
    const { workspaceId, isImportMembers } = req.params;
    const userId = req.user.id;
    const filePath = path.join(__dirname, '..', `uploads/importfile/`, req.file.filename);
    const chunkDir = path.join(__dirname, '..', 'uploads/chunks'); // Directory for chunk files

    // Ensure the directory exists
    ensureDirectoryExists(chunkDir, (err) => {
      if (err) {
        return res.status(500).json({ message: 'Error creating chunk directory' });
      }

      let jsonData = '';
      const stream = fs.createReadStream(filePath, { encoding: 'utf8' });

      let chunkCount = 0;

      stream.on('data', chunk => {
        jsonData += chunk;
        chunkCount++;

        // Generate a file name for each chunk
        const fileName = path.join(chunkDir, `chunk_${chunkCount}.json`);

        // Write the chunk to a new file
        fs.promises.writeFile(fileName, chunk)
          .then(() => {
          })
          .catch((err) => {
            console.error(`Error writing chunk to file: ${err}`);
          });


      });

      stream.on('end', async () => {
        // Step 2: Parse the accumulated JSON data
        let parsedData;
        try {
          parsedData = JSON.parse(jsonData);
        } catch (err) {
          console.error('Error parsing JSON:', err);
          return res.status(400).json({ message: 'Invalid JSON file' });
        }

        // Step 3: Convert the parsed data
        const importData = convertTrelloExportToZoobbe(parsedData, workspaceId);

        // Step 4: Write the importData to a new JSON file
        const jsonContent = JSON.stringify(importData, null, 4);
        await fs.promises.writeFile('importData.json', jsonContent, 'utf8');
        console.log('JSON file has been saved successfully.');

        const io = req.app.get('socketio');
        io.to(userId).emit('importProgress', { message: 'Import started', progress: 0 });

        await processImportData(importData, workspaceId, isImportMembers, userId, req, io);


        // Clean up chunk files without deleting the directory
        deleteFilesInDirectory(chunkDir)
          .then(() => {
            console.log('Chunk files cleaned up successfully');
            return fs.promises.unlink(filePath);
          })
          .then(() => {
            console.log('Uploaded file cleaned up successfully');
            res.status(201).json({ message: 'Board imported successfully' });
          })
          .catch((error) => {
            console.error('Error during cleanup:', error);
            res.status(500).json({ message: 'Error during cleanup' });
          });
      });

      stream.on('error', error => {
        console.error('Error reading the file:', error);
        res.status(500).json({ message: error.message });
      });
    });
  } catch (error) {
    console.error('Error importing board:', error);
    res.status(500).json({ message: error.message });
  }
};



// Invite via email
const sendInvitationRequest = async (req, res) => {
  const { workspaceId } = req.params;
  const { invitees, message } = req.body;
  const workspace = await Workspace.findOne({ shortId: workspaceId });

  if (!workspace) return res.status(404).json({ message: 'Workspace not found' });

  try {
    const invitePromises = invitees.map(async ({ email, role }) => {
      const inviteToken = jwt.sign(
        { id: req.user._id, workspaceId, email },
        process.env.JWT_SECRET,
        { expiresIn: '1d' }
      );

      // Check if the user is already on the workspace
      const user = await User.findOne({ email });
      if (user && !workspace.members?.some(m => m.user.equals(user._id))) {
        workspace.members.push({ user: user._id, role, status: 'pending' });
      }

      await sendInvitationEmail({
        to: email,
        shortId: workspaceId,
        inviteToken,
        name: workspace.name,
        message,
        invitePath: 'w',
      });

    });

    await Promise.all(invitePromises);
    await workspace.save();

    res.status(200).json({ message: 'Invitations sent successfully' });
  } catch (error) {
    console.error('Error sending invitations:', error);
    res.status(500).json({ message: 'Failed to send invitations' });
  }
};


// Accept invite
const acceptInvitationRequest = async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({ message: 'No token provided' });
  }

  try {
    // Decode the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if the token has already been used
    const isTokenUsed = await UsedToken.findOne({ token });
    if (isTokenUsed) {
      return res.status(400).json({ message: 'This token has already been used' });
    }

    // Mark the token as used
    await UsedToken.create({ token });

    const { shortId, email, role } = decoded;

    // Check if a user with this email exists
    let user = await User.findOne({ email });


    if (!user) {
      // Create a new user with a default password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('defaultPassword123', salt);

      user = await User.create({
        email,
        name: email.split('@')[0],
        username: email.split('@')[0],
        passwordHash: hashedPassword,
        verified: true, // Auto-verify since they're joining via invitation
      });
    }


    // Find the workspace
    const workspace = await Workspace.findOne({ shortId: shortId });
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Check if the user is already a workspace member or guest
    const isWorkspaceMember = workspace.members?.some((m) => m.user.equals(user._id));
    const isWorkspaceGuest = workspace.guests?.some((g) => g.equals(user._id));

    if (!isWorkspaceMember && !isWorkspaceGuest) {
      // Add the user to the workspace guests list if they're not already a member or guest
      await workspace.updateOne({ $addToSet: { guests: user._id } });
    }

    // Check if the user is already a workspace member
    const existingWorkspaceMember = workspace.members.find((m) => m.user.equals(user._id));

    if (existingWorkspaceMember) {
      // Generate an access token for the user
      const accessToken = jwt.sign({ id: user._id }, process.env.JWT_SECRET, {
        expiresIn: '1h',
      });

      return res.status(200).json({
        message: 'You are already a member of this workspace',
        email,
        accessToken,
        isDefaultPasswordSet: user.isDefaultPasswordSet,

      });
    }

    // Add the user to the workspace members list
    await workspace.updateOne({
      $push: { members: { user: user._id, role, status: 'accepted' } },
    });

    // Generate an access token for the user
    const accessToken = jwt.sign({ id: user._id }, process.env.JWT_SECRET, {
      expiresIn: '1h',
    });

    // Populate updated workspace members and return
    const populatedWorkspace = await Workspace.findOne({ shortId: shortId }).populate({
      path: 'members.user',
      select: 'username bio profilePicture name',
    });

    res.status(200).json({
      message: 'Successfully joined the workspace',
      accessToken,
      members: populatedWorkspace.members,
      isDefaultPasswordSet: user.isDefaultPasswordSet,
    });
  } catch (error) {
    console.error('Error accepting invitation:', error.message);
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(400).json({ message: 'Invalid or expired token' });
    }
    res.status(500).json({ message: 'Failed to accept invitation' });
  }
};


// Generate or retrieve join link
const generateJoinLink = async (req, res) => {
  const { workspaceId } = req.params;

  try {
    // Fetch the workspace from the database
    const workspace = await Workspace.findOne({ shortId: workspaceId });
    if (!workspace) return res.status(404).json({ message: 'Workspace not found' });

    // Check if a join link already exists
    if (workspace.joinLink) {
      return res.status(200).json({ joinLink: workspace.joinLink });
    }

    // Generate a new join link without expiration
    const joinToken = jwt.sign({ workspaceId }, process.env.JWT_SECRET);
    const joinLink = `${process.env.SITE_URL}/invite/w/${workspaceId}?token=${joinToken}`;

    // Update the workspace with the new join link
    workspace.joinLink = joinLink;
    await workspace.save();

    res.status(200).json({ joinLink });
  } catch (error) {
    console.error('Error generating join link:', error);
    res.status(500).json({ message: 'Failed to generate join link' });
  }
};


// Delete join link
const deleteJoinLink = async (req, res) => {
  const { workspaceId } = req.params;

  try {
    const workspace = await Workspace.findOne({ shortId: workspaceId });
    if (!workspace) return res.status(404).json({ message: 'Workspace not found' });

    workspace.joinLink = null;
    await workspace.save();

    res.status(200).json({ message: 'Join link deleted' });
  } catch (error) {
    console.error('Error deleting join link:', error);
    res.status(500).json({ message: 'Failed to delete join link' });
  }
};

const joinByLink = async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({ message: 'No token provided' });
  }

  try {
    // Decode the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const { workspaceId, email } = decoded;

    // Verify the user making the request
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Ensure the token's email matches the user's email
    if (user.email !== email) {
      return res.status(400).json({ message: 'Email mismatch or invalid user' });
    }

    // Fetch the workspace associated with the workspace
    const workspace = await Workspace.findOne({ shortId: workspaceId });
    if (!workspace) {
      return res.status(404).json({ message: 'Workspace not found' });
    }

    // Check if the user is already a workspace member or guest
    const isWorkspaceMember = workspace.members?.some(m => m.user.equals(user._id));
    const isWorkspaceGuest = workspace.guests?.some(g => g.equals(user._id));

    // Add the user as a workspace guest if not already a member or guest
    if (!isWorkspaceMember && !isWorkspaceGuest) {
      await workspace.updateOne({ $addToSet: { guests: user._id } });
    }

    // Check if the user is already a workspace member
    const existingWorkspaceMember = workspace.members.find(m => m.user.equals(user._id));

    if (existingWorkspaceMember) {
      return res.status(200).json({ message: 'You are already a member of this workspace' });
    }

    // Add the user to the workspace members
    await workspace.updateOne({ $push: { members: { user: user._id, role: 'member', status: 'accepted' } } });

    // Populate updated workspace members and return
    const populatedWorkspace = await Workspace.findOne({ shortId: workspaceId }).populate({
      path: 'members.user',
      select: 'username bio profilePicture name'
    });

    res.status(200).json({
      message: 'Successfully joined the workspace',
      members: populatedWorkspace.members,
    });
  } catch (error) {
    console.error('Error accepting invitation:', error.message);
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(400).json({ message: 'Invalid or expired token' });
    }
    res.status(500).json({ message: 'Failed to accept invitation' });
  }
};


const getSearchResults = async (req, res) => {
  try {
    const { query } = req.query;
    const userId = req.user._id;

    if (!query) {
      return res.status(400).json({ message: 'Search query is required.' });
    }

    // Extract workspace IDs correctly, handling both ObjectId and object formats
    const workspaceIds = [];
    if (req.user.workspaces && Array.isArray(req.user.workspaces)) {
      req.user.workspaces.forEach(workspace => {
        if (workspace && typeof workspace === 'object' && workspace._id) {
          // If workspace is an object with _id
          workspaceIds.push(workspace._id);
        } else if (workspace && typeof workspace === 'object' && workspace.shortId) {
          // If workspace is an object with shortId, we need to find the actual workspace ID
          // We'll handle this in the query
        } else if (workspace && (typeof workspace === 'string' || workspace instanceof mongoose.Types.ObjectId)) {
          // If workspace is a string ID or ObjectId
          workspaceIds.push(workspace);
        }
      });
    }

    // Build the query to find accessible boards
    const boardQuery = {
      $or: [
        { 'members.user': userId }, // Check that user is part of the board
      ],
    };

    // Add workspace condition only if we have workspace IDs
    if (workspaceIds.length > 0) {
      boardQuery.$or.push({ workspace: { $in: workspaceIds } });
    }

    // If we have workspaces with shortId, add a condition for those
    if (req.user.workspaces && Array.isArray(req.user.workspaces)) {
      const shortIds = req.user.workspaces
        .filter(w => w && typeof w === 'object' && w.shortId)
        .map(w => w.shortId);

      if (shortIds.length > 0) {
        // Find workspaces by shortId and get their _ids
        const workspacesByShortId = await Workspace.find({ shortId: { $in: shortIds } }).select('_id');
        const additionalWorkspaceIds = workspacesByShortId.map(w => w._id);

        if (additionalWorkspaceIds.length > 0) {
          boardQuery.$or.push({ workspace: { $in: additionalWorkspaceIds } });
        }
      }
    }

    const accessibleBoards = await Board.find(boardQuery).select('_id shortId cover title');

    if (accessibleBoards.length === 0) {
      return res.status(200).json({
        success: true,
        data: { boards: [], cards: [] },
        message: 'No accessible boards found.',
      });
    }

    const boardIds = accessibleBoards.map(board => board.shortId);


    const boards = await Board.find({
      shortId: { $in: boardIds },
      title: { $regex: query, $options: 'i' }, // Case-insensitive regex
    })
      .select('title shortLink permalink cover workspace') // Include workspace reference in the selection
      .populate('workspace', 'name'); // Populate only the title field from the Workspace model


    const cards = await Card.find({
      board: { $in: boardIds.map(boardId => boardId) }, // Convert ObjectIds to strings
      title: { $regex: query, $options: 'i' }, // Case-insensitive regex
    })
      .select('title permalink boardTitle actionList board') // Include relevant fields
      .populate({
        path: 'actionList', // Populate the actionList reference
        select: 'title', // Select only the title field from ActionList
      });



    res.status(200).json({
      success: true,
      data: { boards, cards },
      message: `Found ${boards.length} boards and ${cards.length} cards matching the query.`,
    });
  } catch (error) {
    console.error('Error fetching search results:', error);
    res.status(500).json({ message: 'Server error while searching.', error });
  }
};



module.exports = {
  getAllWorkspaces,
  getWorkspaces,
  getLiteWorkspaces,
  getGuestWorkspaces,
  getWorkspacesUserId,
  getWorkspaceById,
  getWorkspaceByShortName,
  getWorkspaceByUserId,
  createWorkspace,
  deleteWorkspace,
  addMemberToWorkspace,
  updateMemberRole,
  removeMemberFromWorkspace,
  importBoard,
  sendInvitationRequest,
  acceptInvitationRequest,
  generateJoinLink,
  deleteJoinLink,
  joinByLink,
  getSearchResults

};

const express = require('express');
const router = express.Router();

const { authenticateToken, authenticateAndAuthorize } = require('../middleware/authMiddleware');

const {
  getCards,
  getArchivedCards,
  getCardsByMemberId,
  getCardsByMemberUserName,
  getArchiveCards,
  getCardsByActionListId,
  getCardById,
  createCard,
  copyCard,
  deleteCard,
  updateCard,
  archiveCard,
  addMemberToCard,
  removeMemberFromCard,
  getCardMembers,
  addCommentToCard,
  getCardComments,
  updateComment,
  deleteComment,
  addChecklistToCard,
  getChecklistsFromCard,
  updateChecklist,
  deleteChecklist,
  addChecklistItem,
  updateChecklistItem,
  deleteChecklistItem,
  getAttachments,
  getAttachmentsByCardId,
  getAttachment,
  getStaticAttachment,
  uploadAttachment,
  updateAttachment,
  deleteAttachment,
  addLabelToCard,
  getLabelsFromCard,
  deleteLabelFromCard,
  addDueDate,
  getDueDate,
  updateDueDate,
  removeDueDate,
  // createActivity,
  getActivities,
  // updateActivity,
  addPriority,
  getPriority,
  
} = require('../controllers/cardController');
const uploadMiddleware = require('../middleware/upload');


// Existing routes
router.get('/cards', authenticateToken, authenticateAndAuthorize('card'), getCards);
router.get('/:boardId/cards/archived/', authenticateToken, authenticateAndAuthorize('board'), getArchivedCards);
router.get('/:memberId/get-cards', authenticateToken, getCardsByMemberId);
router.get('/:userName/cards', authenticateToken, getCardsByMemberUserName);
router.get('/actionLists/:actionListId/cards/', authenticateToken, getCardsByActionListId);
router.get('/archive-cards', authenticateToken, authenticateAndAuthorize('card'), getArchiveCards);
router.post('/cards', authenticateToken, createCard);
router.post('/cards/:cardId/copy', authenticateToken, authenticateAndAuthorize('card'), copyCard);

router.post('/cards/delete/:cardId', authenticateToken, authenticateAndAuthorize('card'), deleteCard);
router.post('/cards/update/:cardId', authenticateToken, authenticateAndAuthorize('card'), updateCard);
router.post('/cards/archive/:cardId', authenticateToken, authenticateAndAuthorize('card'), archiveCard);
router.get('/cards/:cardId/members', authenticateToken, authenticateAndAuthorize('card'), getCardMembers);
router.post('/cards/:cardId/addMember', authenticateToken, authenticateAndAuthorize('card'), addMemberToCard);
router.post('/cards/:cardId/removeMember', authenticateToken, authenticateAndAuthorize('card'), removeMemberFromCard);
router.get('/cards/:cardId', authenticateToken, authenticateAndAuthorize('card'), getCardById);
// router.get('/cards/:shortId', authenticateToken, getCardById);

// Routes for comments
router.post('/cards/:cardId/comments', authenticateToken, authenticateAndAuthorize('card'), addCommentToCard);
router.get('/cards/:cardId/comments', authenticateToken, authenticateAndAuthorize('card'), getCardComments);
router.put('/cards/:cardId/comments/:commentId', authenticateToken, authenticateAndAuthorize('card'), updateComment);
router.delete('/cards/:cardId/comments/:commentId', authenticateToken, authenticateAndAuthorize('card'), deleteComment);

// Routes for checklists
router.post('/cards/:cardId/checklists', authenticateToken, authenticateAndAuthorize('card'), addChecklistToCard);
router.get('/cards/:cardId/checklists', authenticateToken, authenticateAndAuthorize('card'), getChecklistsFromCard);
router.put('/cards/:cardId/checklists/:checklistId', authenticateToken, authenticateAndAuthorize('card'), updateChecklist);
router.delete('/cards/:cardId/checklists/:checklistId', authenticateToken, authenticateAndAuthorize('card'), deleteChecklist);
router.post('/cards/:cardId/checklists/:checklistId/items', authenticateToken, authenticateAndAuthorize('card'), addChecklistItem);
router.put('/cards/:cardId/checklists/:checklistId/items/:itemId', authenticateToken, authenticateAndAuthorize('card'), updateChecklistItem);
router.delete('/cards/:cardId/checklists/:checklistId/items/:itemId', authenticateToken, authenticateAndAuthorize('card'), deleteChecklistItem);

// Routes for attachments
router.get('/attachments', authenticateToken, authenticateAndAuthorize('card'), getAttachments);
router.get('/cards/attachments/:cardId', authenticateToken, authenticateAndAuthorize('card'), getAttachmentsByCardId); // for
router.post('/cards/attachments/:cardId', authenticateToken, authenticateAndAuthorize('card'), uploadMiddleware, uploadAttachment);
router.get('/cards/attachments/:cardId/:attachmentId', authenticateToken, authenticateAndAuthorize('card'), getAttachment); // for
router.get('/static/cards/attachments/:cardId/:attachmentId', authenticateToken, authenticateAndAuthorize('card'), getStaticAttachment); // for
router.put('/cards/attachments/:cardId', authenticateToken, authenticateAndAuthorize('card'), updateAttachment);
router.delete('/cards/attachments/:cardId', authenticateToken, authenticateAndAuthorize('card'), deleteAttachment);

// Routes for labels
router.get('/cards/:cardId/labels', authenticateToken, authenticateAndAuthorize('card'), getLabelsFromCard);
router.put('/cards/:cardId/labels/:labelId', authenticateToken, authenticateAndAuthorize('card'), addLabelToCard);
router.delete('/cards/:cardId/labels/:labelId', authenticateToken, authenticateAndAuthorize('card'), deleteLabelFromCard);

// Routes for DueDate
router.post('/cards/:cardId/duedate', authenticateToken, authenticateAndAuthorize('card'), addDueDate);
router.get('/cards/:cardId/duedate', authenticateToken, authenticateAndAuthorize('card'), getDueDate);
router.put('/cards/:cardId/duedate', authenticateToken, authenticateAndAuthorize('card'), updateDueDate);
router.delete('/cards/:cardId/duedate', authenticateToken, authenticateAndAuthorize('card'), removeDueDate);

router.post('/cards/:cardId/priority', authenticateToken, authenticateAndAuthorize('card'), addPriority);
router.get('/cards/:cardId/priority', authenticateToken, authenticateAndAuthorize('card'), getPriority);

// Routes for activites
// router.post('/cards/:cardId/activities', authenticateToken, authenticateAndAuthorize('card'), createActivity);
router.get('/cards/:cardId/activities', authenticateToken, authenticateAndAuthorize('card'), getActivities);
// router.put('/cards/:cardId/activities', authenticateToken, authenticateAndAuthorize('card'), updateActivity);


module.exports = router;

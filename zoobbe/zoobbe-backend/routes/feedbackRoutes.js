const express = require('express');
const multer = require('multer');
const { addFeedback, getFeedbacks, getFeedback } = require('../controllers/feedbackController');
const { authenticateToken, authenticateAndAuthorize } = require('../middleware/authMiddleware');

const User = require('../models/User');

const router = express.Router();

router.post('/feedback/', authenticateToken, addFeedback);
router.get('/feedbacks/', authenticateToken, getFeedbacks);
router.get('/feedback/:feedbackId/', authenticateToken, getFeedback);

module.exports = router;

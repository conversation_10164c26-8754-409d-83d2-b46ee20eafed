const express = require('express');
const router = express.Router();
const Stripe = require('stripe');
const rateLimit = require('express-rate-limit');
const allowPaymentAction = require('../middleware/workspaceAuthMiddleware');
const { authenticateToken } = require('../middleware/authMiddleware');
const { validatePriceId, calculateAmount } = require('../utils/helper');
const Subscription = require('../models/Subscription');
const Workspace = require('../models/Workspace');
const User = require('../models/User');

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Helper function to update member access levels
async function updateMemberAccessLevels(workspaceId, seatsToUpgrade) {
    try {
        const workspace = await Workspace.findById(workspaceId);
        if (!workspace) return;

        // Find view-only members and upgrade them to full access
        const viewOnlyMembers = workspace.members.filter(member => member.accessLevel === 'view-only');
        const membersToUpgrade = viewOnlyMembers.slice(0, seatsToUpgrade);

        for (const member of membersToUpgrade) {
            member.accessLevel = 'full';
        }

        await workspace.save();

        // Update user workspaces access level
        const bulkOps = membersToUpgrade.map(member => ({
            updateOne: {
                filter: { _id: member.user, 'workspaces.shortId': workspace.shortId },
                update: {
                    $set: {
                        'workspaces.$.accessLevel': 'full'
                    }
                }
            }
        }));

        if (bulkOps.length > 0) {
            await User.bulkWrite(bulkOps);
        }

    } catch (error) {
        console.error('Error updating member access levels:', error);
    }
}

// Rate limiting for payment attempts
const paymentLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 10
});

// Rate limiting for webhooks
const webhookLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100
});

router.post('/create-payment-intent',
    paymentLimiter,
    authenticateToken,
    allowPaymentAction,
    async (req, res) => {
        const {
            priceId,
            workspaceId,
            userCount,
            additionalSeats = 0,
            billingCycle,
            planType
        } = req.body;

        try {
            // Validate inputs
            if (!priceId || !workspaceId || (!userCount && !additionalSeats) || !billingCycle || !planType) {
                return res.status(400).json({ error: 'Missing required parameters' });
            }

            // Validate price ID
            if (!validatePriceId(priceId)) {
                return res.status(400).json({ error: 'Invalid price ID' });
            }

            // Create or update customer in Stripe
            let customer;
            const workspace = await Workspace.findById(workspaceId)
                .populate('subscription');

            if (workspace.subscription?.stripeCustomerId) {
                customer = await stripe.customers.retrieve(workspace.subscription?.stripeCustomerId);
            } else {
                const user = await User.findById(req.user.id);
                customer = await stripe.customers.create({
                    email: user.email,
                    metadata: {
                        workspaceId,
                        userId: req.user.id
                    }
                });
            }

            const subscription = await Subscription.findOne({ workspaceId });

            // Calculate total amount (in cents) including additional seats
            // NOTE: Do NOT add existing reservedSeats here - they're already paid for
            const totalSeats = parseInt(userCount || 0) + parseInt(additionalSeats || 0);
            console.log(`[RESERVED_SEATS_DEBUG] Payment Intent Creation - Calculating total seats:`, {
                workspaceId,
                userCount: parseInt(userCount || 0),
                additionalSeats: parseInt(additionalSeats || 0),
                existingReservedSeats: subscription?.reservedSeats || 0,
                totalSeatsBeingPaidFor: totalSeats,
                subscriptionId: subscription?._id,
                note: "totalSeats is ONLY the new seats being paid for, not including existing reserved seats"
            });
            const amount = calculateAmount(planType, billingCycle, totalSeats);

            // Create PaymentIntent first
            const paymentIntent = await stripe.paymentIntents.create({
                amount,
                currency: 'usd',
                customer: customer.id,
                metadata: {
                    workspaceId,
                    userId: req.user.id,
                    planType,
                    billingCycle,
                    userCount,
                    additionalSeats,
                    priceId
                },
                payment_method_types: ['card'],
                setup_future_usage: 'off_session'
            });

            // Create or update subscription in our database
            const currentReservedSeats = parseInt(subscription?.reservedSeats || 0);
            const newTotalReservedSeats = parseInt(currentReservedSeats) + parseInt(totalSeats);

            // CRITICAL: Ensure we're working with numbers, not strings
            if (isNaN(currentReservedSeats) || isNaN(newTotalReservedSeats)) {
                console.error(`[RESERVED_SEATS_DEBUG] CRITICAL ERROR - NaN detected:`, {
                    currentReservedSeats,
                    newTotalReservedSeats,
                    totalSeats,
                    subscriptionReservedSeats: subscription?.reservedSeats
                });
                throw new Error('Invalid seat calculation - NaN detected');
            }

            console.log(`[RESERVED_SEATS_DEBUG] CRITICAL - About to call findOneAndUpdate:`, {
                workspaceId,
                currentReservedSeats,
                currentReservedSeatsType: typeof currentReservedSeats,
                totalSeats,
                totalSeatsType: typeof totalSeats,
                newTotalReservedSeats,
                newTotalReservedSeatsType: typeof newTotalReservedSeats,
                updateObject: {
                    reservedSeats: newTotalReservedSeats,
                    quantity: newTotalReservedSeats
                }
            });

            // FINAL CHECK: Ensure the values we're about to save are correct
            console.log(`[RESERVED_SEATS_DEBUG] FINAL CHECK - Values about to be saved to database:`, {
                workspaceId,
                reservedSeatsToSave: newTotalReservedSeats,
                quantityToSave: newTotalReservedSeats,
                typeCheck: {
                    reservedSeatsType: typeof newTotalReservedSeats,
                    isNumber: typeof newTotalReservedSeats === 'number',
                    isInteger: Number.isInteger(newTotalReservedSeats)
                }
            });

            const subscriptionDoc = await Subscription.findOneAndUpdate(
                { workspaceId },
                {
                    workspaceId,
                    status: 'incomplete',
                    planType,
                    billingCycle,
                    priceId,
                    quantity: newTotalReservedSeats,
                    reservedSeats: newTotalReservedSeats,
                    stripeCustomerId: customer.id,
                    currentPeriodStart: new Date(),
                    currentPeriodEnd: new Date(Date.now() + (billingCycle === 'annually' ? 365 : 30) * 24 * 60 * 60 * 1000),
                    cancelAtPeriodEnd: false
                },
                { upsert: true, new: true }
            );

            console.log(`[RESERVED_SEATS_DEBUG] CRITICAL - After findOneAndUpdate returned:`, {
                workspaceId,
                subscriptionId: subscriptionDoc._id,
                returnedReservedSeats: subscriptionDoc.reservedSeats,
                returnedReservedSeatsType: typeof subscriptionDoc.reservedSeats,
                returnedQuantity: subscriptionDoc.quantity,
                returnedQuantityType: typeof subscriptionDoc.quantity
            });

            console.log(`[RESERVED_SEATS_DEBUG] Payment Intent Creation - Subscription created/updated:`, {
                workspaceId,
                subscriptionId: subscriptionDoc._id,
                previousReservedSeats: currentReservedSeats,
                seatsBeingPaidFor: totalSeats,
                newReservedSeats: subscriptionDoc.reservedSeats,
                quantity: subscriptionDoc.quantity,
                status: subscriptionDoc.status,
                action: subscription ? 'updated' : 'created'
            });

            // Update workspace with subscription reference
            await Workspace.findByIdAndUpdate(workspaceId, {
                subscription: subscriptionDoc._id
            });

            // Return client secret
            res.json({
                clientSecret: paymentIntent.client_secret,
                subscription: subscriptionDoc
            });

        } catch (err) {
            console.error('Payment intent creation error:', err);
            res.status(500).json({ error: 'Failed to create payment intent' });
        }
    }
);

router.post('/webhook',
    webhookLimiter,
    express.raw({ type: 'application/json' }),

    async (req, res) => {

        const sig = req.headers['stripe-signature'];

        try {
            const event = stripe.webhooks.constructEvent(
                req.body,
                sig,
                process.env.STRIPE_WEBHOOK_SECRET
            );

            console.log(`[RESERVED_SEATS_DEBUG] Webhook received:`, {
                eventType: event.type,
                eventId: event.id,
                paymentIntentId: event.data.object?.id,
                metadata: event.data.object?.metadata
            });

            switch (event.type) {
                case 'payment_intent.succeeded':
                    console.log(`[RESERVED_SEATS_DEBUG] WEBHOOK - payment_intent.succeeded - TEMPORARILY DISABLED to test if this is causing the multiplication`);
                    // TEMPORARILY DISABLED: await handlePaymentSuccess(event.data.object);
                    break;
                case 'payment_intent.payment_failed':
                    await handlePaymentFailure(event.data.object);
                    break;
                case 'customer.subscription?.updated':
                    await handleSubscriptionUpdated(event.data.object);
                    break;
                case 'customer.subscription?.deleted':
                    await handleSubscriptionDeletion(event.data.object);
                    break;
            }

            res.json({ received: true });
        } catch (err) {
            console.error('Webhook error:', err);
            return res.status(400).json({ error: 'Webhook signature verification failed' });
        }
    }
);

async function handlePaymentSuccess(paymentIntent) {
    const { workspaceId, userId, planType, billingCycle, priceId, userCount, additionalSeats, type, pendingSeats } = paymentIntent.metadata;

    console.log(`[RESERVED_SEATS_DEBUG] Payment Success Handler Started:`, {
        workspaceId,
        paymentIntentId: paymentIntent.id,
        metadata: paymentIntent.metadata,
        userCount: userCount,
        additionalSeats: additionalSeats,
        type: type,
        pendingSeats: pendingSeats
    });

    try {
        // Find all subscriptions and clean up duplicates
        const allSubscriptions = await Subscription.find({ workspaceId })
            .sort({ createdAt: -1 });

        console.log(`[RESERVED_SEATS_DEBUG] Payment Success: Found ${allSubscriptions.length} subscriptions for workspace ${workspaceId}:`,
            allSubscriptions.map(sub => ({
                id: sub._id,
                reservedSeats: sub.reservedSeats,
                status: sub.status,
                createdAt: sub.createdAt
            }))
        );

        let subscription = null;

        if (allSubscriptions.length > 1) {
            // Multiple subscriptions - keep newest, delete rest
            subscription = allSubscriptions[0];
            const oldSubscriptions = allSubscriptions.slice(1);

            console.log(`Payment Success: AUTO-CLEANING ${oldSubscriptions.length} old subscriptions`);

            for (const oldSub of oldSubscriptions) {
                await Subscription.findByIdAndDelete(oldSub._id);
                console.log(`Payment Success: DELETED old subscription ${oldSub._id}`);
            }
        } else if (allSubscriptions.length === 1) {
            subscription = allSubscriptions[0];
        }

        if (type === 'pending_seats_payment') {
            // Handle pending seats payment
            if (subscription) {
                const seatsToMove = parseInt(pendingSeats) || 0;
                const previousReservedSeats = subscription?.reservedSeats || 0;
                const previousPendingSeats = subscription?.pendingSeats || 0;

                subscription.reservedSeats = parseInt(subscription?.reservedSeats || 0) + parseInt(seatsToMove);
                subscription.pendingSeats = Math.max((subscription?.pendingSeats || 0) - seatsToMove, 0);
                subscription.lastPaymentDate = new Date();

                console.log(`[RESERVED_SEATS_DEBUG] Pending Seats Payment - Moving seats from pending to reserved:`, {
                    workspaceId,
                    subscriptionId: subscription._id,
                    seatsToMove,
                    previousReservedSeats,
                    newReservedSeats: subscription.reservedSeats,
                    previousPendingSeats,
                    newPendingSeats: subscription.pendingSeats,
                    userId
                });

                // Add to seat history
                subscription?.seatHistory.push({
                    action: 'paid',
                    seats: seatsToMove,
                    userId,
                    reason: 'Pending seats payment'
                });

                await subscription?.save();

                // Update member access levels from view-only to full
                await updateMemberAccessLevels(workspaceId, seatsToMove);
            }
        } else if (type === 'additional_seats_payment') {
            // Handle additional seats payment
            if (subscription) {
                const seatsToAdd = parseInt(additionalSeats) || 0;
                const previousReservedSeats = subscription?.reservedSeats || 0;

                subscription.reservedSeats = parseInt(subscription?.reservedSeats || 0) + parseInt(seatsToAdd);
                subscription.lastPaymentDate = new Date();

                console.log(`[RESERVED_SEATS_DEBUG] Additional Seats Payment - Adding seats to reserved:`, {
                    workspaceId,
                    subscriptionId: subscription._id,
                    seatsToAdd,
                    previousReservedSeats,
                    newReservedSeats: subscription.reservedSeats,
                    userId
                });

                // Add to seat history
                subscription?.seatHistory.push({
                    action: 'added',
                    seats: seatsToAdd,
                    userId,
                    reason: 'Additional seats purchased'
                });

                await subscription?.save();
            }
        } else {
            // Handle regular subscription payment
            const seatsBeingPaidFor = parseInt(userCount) + parseInt(additionalSeats || 0);

            console.log(`[RESERVED_SEATS_DEBUG] Payment Success: Processing regular subscription payment`, {
                workspaceId,
                userCount: userCount,
                additionalSeats: additionalSeats,
                userCountParsed: parseInt(userCount),
                additionalSeatsParsed: parseInt(additionalSeats || 0),
                seatsBeingPaidFor,
                existingSubscription: subscription ? {
                    id: subscription?._id,
                    currentQuantity: subscription?.quantity,
                    currentReservedSeats: subscription?.reservedSeats,
                    currentPendingSeats: subscription?.pendingSeats
                } : null
            });

            if (!subscription) {
                // First time subscription - set seats to what we're paying for
                subscription = new Subscription({
                    workspaceId,
                    status: 'active',
                    planType,
                    billingCycle,
                    priceId,
                    quantity: seatsBeingPaidFor,
                    reservedSeats: seatsBeingPaidFor,
                    totalSeats: seatsBeingPaidFor,
                    pendingSeats: 0,
                    stripeCustomerId: paymentIntent.customer,
                    lastPaymentDate: new Date(),
                    currentPeriodStart: new Date(),
                    currentPeriodEnd: new Date(Date.now() + (billingCycle === 'annually' ? 365 : 30) * 24 * 60 * 60 * 1000),
                    seatHistory: [{
                        action: 'paid',
                        seats: seatsBeingPaidFor,
                        userId,
                        reason: `Initial subscription for ${seatsBeingPaidFor} seat${seatsBeingPaidFor > 1 ? 's' : ''}`
                    }]
                });

                console.log(`[RESERVED_SEATS_DEBUG] Regular Payment - Created new subscription:`, {
                    workspaceId,
                    subscriptionId: subscription._id,
                    reservedSeats: seatsBeingPaidFor,
                    totalSeats: seatsBeingPaidFor,
                    pendingSeats: 0,
                    seatsBeingPaidFor,
                    userId
                });
                console.log(`Payment Success: Created new subscription with ${seatsBeingPaidFor} seats`);
            } else {
                // Existing subscription - DO NOT add seats again, they were already added in payment intent creation
                // Just update status and other fields
                const previousReservedSeats = subscription?.reservedSeats || 0;
                const previousPendingSeats = subscription?.pendingSeats || 0;

                console.log(`[RESERVED_SEATS_DEBUG] CRITICAL - Payment Success: NOT adding seats (already added in payment intent):`, {
                    workspaceId,
                    subscriptionId: subscription._id,
                    currentReservedSeats: previousReservedSeats,
                    seatsBeingPaidFor: seatsBeingPaidFor,
                    note: "Seats were already added during payment intent creation, just updating status to active"
                });

                subscription.status = 'active';
                subscription.planType = planType;
                subscription.billingCycle = billingCycle;
                subscription.priceId = priceId;
                // DO NOT CHANGE reservedSeats - they were already set correctly in payment intent creation
                subscription.pendingSeats = Math.max(0, previousPendingSeats - seatsBeingPaidFor); // Reduce pending seats
                subscription.totalSeats = subscription.reservedSeats + subscription?.pendingSeats;
                subscription.lastPaymentDate = new Date();
                subscription.currentPeriodStart = new Date();
                subscription.currentPeriodEnd = new Date(Date.now() + (billingCycle === 'annually' ? 365 : 30) * 24 * 60 * 60 * 1000);

                console.log(`[RESERVED_SEATS_DEBUG] CRITICAL - After updating subscription status only:`, {
                    workspaceId,
                    subscriptionId: subscription._id,
                    reservedSeatsAfterUpdate: subscription.reservedSeats,
                    status: subscription.status,
                    note: "Reserved seats unchanged, only status updated to active"
                });

                console.log(`[RESERVED_SEATS_DEBUG] Regular Payment - Updated existing subscription:`, {
                    workspaceId,
                    subscriptionId: subscription._id,
                    reservedSeats: subscription.reservedSeats,
                    previousPendingSeats,
                    newPendingSeats: subscription.pendingSeats,
                    seatsBeingPaidFor,
                    newTotalSeats: subscription.totalSeats,
                    userId,
                    note: "Reserved seats NOT changed in payment success - already set in payment intent"
                });

                console.log(`Payment Success: Updated existing subscription`, {
                    reservedSeats: subscription.reservedSeats,
                    previousPendingSeats,
                    seatsBeingPaidFor,
                    newPendingSeats: subscription?.pendingSeats,
                    note: "Status updated to active, seats already correct"
                });

                // Add to seat history
                subscription.seatHistory.push({
                    action: 'paid',
                    seats: seatsBeingPaidFor,
                    userId,
                    reason: `Payment confirmed for ${seatsBeingPaidFor} seat${seatsBeingPaidFor > 1 ? 's' : ''} (total reserved: ${subscription.reservedSeats})`
                });
            }

            console.log(`[RESERVED_SEATS_DEBUG] CRITICAL - About to save subscription:`, {
                workspaceId,
                subscriptionId: subscription._id,
                reservedSeatsBeforeSave: subscription.reservedSeats,
                reservedSeatsType: typeof subscription.reservedSeats
            });

            await subscription?.save();

            console.log(`[RESERVED_SEATS_DEBUG] CRITICAL - After saving subscription:`, {
                workspaceId,
                subscriptionId: subscription._id,
                reservedSeatsAfterSave: subscription.reservedSeats,
                reservedSeatsType: typeof subscription.reservedSeats
            });
        }

        // Update workspace with subscription reference
        await Workspace.findByIdAndUpdate(workspaceId, {
            subscription: subscription?._id
        });

        // Get workspace and update members' premium status
        const workspace = await Workspace.findById(workspaceId).populate('members.user');

        if (workspace) {
            // Update all workspace members' premium status to true since payment was successful
            const memberUpdates = [...workspace.members.map(member => member.user), ...workspace.guests].map(async (user) => {
                if (user) {
                    await User.findByIdAndUpdate(user._id, {
                        isPremiumMember: true,
                        canSeeOnlineStatus: true
                    });
                }
            });

            await Promise.all(memberUpdates);
        }

    } catch (error) {
        console.error('Error handling payment success:', error);
        throw error;
    }
}

async function handlePaymentFailure(paymentIntent) {
    const { workspaceId } = paymentIntent.metadata;

    try {
        await Subscription.findOneAndUpdate(
            { workspaceId },
            {
                status: 'payment_failed',
                updatedAt: new Date()
            }
        );
    } catch (error) {
        console.error('Error handling payment failure:', error);
        throw error;
    }
}

async function handleSubscriptionUpdated(subscription) {
    const { workspaceId } = subscription?.metadata;

    try {
        // Update subscription status
        await Subscription.findOneAndUpdate(
            { workspaceId },
            {
                status: subscription?.status,
                updatedAt: new Date(),
                currentPeriodEnd: new Date(subscription?.current_period_end * 1000),
                currentPeriodStart: new Date(subscription?.current_period_start * 1000)
            }
        );

        // Get workspace and update members' premium status
        const workspace = await Workspace.findOne({ shortId: workspaceId }).populate('members.user');

        if (workspace) {
            const isPremium = subscription?.status === 'active';

            // Update all workspace members' premium status
            if (workspace) {
                // Update all workspace members' premium status to true since payment was successful
                const memberUpdates = [...workspace.members.map(member => member.user), ...workspace.guests].map(async (user) => {
                    if (user) {
                        await User.findByIdAndUpdate(user._id, {
                            isPremiumMember: true,
                            canSeeOnlineStatus: true
                        });
                    }
                });

                await Promise.all(memberUpdates);
            }
        }
    } catch (error) {
        console.error('Error handling subscription update:', error);
        throw error;
    }
}

async function handleSubscriptionDeletion(subscription) {
    const { workspaceId } = subscription?.metadata;

    try {
        // Update subscription status
        await Subscription.findOneAndUpdate(
            { workspaceId },
            {
                status: 'canceled',
                canceledAt: new Date(),
                updatedAt: new Date()
            }
        );

        // Get workspace and update members' premium status
        const workspace = await Workspace.findOne({ shortId: workspaceId }).populate('members.user');

        if (workspace) {
            // Update all workspace members' and guests' premium status to false
            const userUpdates = [...workspace.members.map(member => member.user), ...workspace.guests].map(async (user) => {
                if (user) {
                    await User.findByIdAndUpdate(user._id, {
                        isPremiumMember: false,
                        canSeeOnlineStatus: false
                    });
                }
            });

            await Promise.all(userUpdates);
        }
    } catch (error) {
        console.error('Error handling subscription deletion:', error);
        throw error;
    }
}

module.exports = router;
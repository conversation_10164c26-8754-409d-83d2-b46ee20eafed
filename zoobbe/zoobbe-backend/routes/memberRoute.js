const express = require('express');
const multer = require('multer');
const { getMembers, getBoards } = require('../controllers/memberController');
const { authenticateToken, authenticateAndAuthorize } = require('../middleware/authMiddleware');

const User = require('../models/User');
const WorkSpace = require('../models/Workspace');
const uploadProfilePicture = require('../middleware/uploadProfilePicture');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

router.get('/members/:type/:id', authenticateToken, authenticateAndAuthorize('members'), getMembers);
router.get('/members/:workspaceId/:id/boards', authenticateToken, getBoards);

module.exports = router;

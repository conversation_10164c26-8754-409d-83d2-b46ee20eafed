const express = require('express');
const router = express.Router();
const { authenticateToken, authenticateAndAuthorize } = require('../middleware/authMiddleware');

const {
    getBoards,
    getBoardById,
    createBoard,
    deleteBoard,
    archiveBoard,
    updateBoardVisibility,
    lastViewedBoard,
    getLastViewedBoard,
    updateBoard,
    updateBoardOrder,
    getBoardLists,
    updateCardsOrder,
    getBoardMembers,
    addMemberToBoard,
    removeMemberFromBoard,
    updateMemberRole,
    addLabelToBoard,
    getLabelsFromBoard,
    updateLabelOnBoard,
    deleteLabelFromBoard,
    sendInvitationRequest,
    acceptInvitationRequest,
    generateJoinLink,
    deleteJoinLink,
    joinByLink,
    uploadBackground,
    updateBackground,
    uploadBackgroundFromUnsplash,
    getUnsplashPhotos,
    getBackgroundPhotos,
    importTrelloBoards

} = require('../controllers/boardController');
const uploadMiddleware = require('../middleware/upload');

// Endpoint for fetching all boards
router.get('/boards', authenticateToken, getBoards);

// Endpoint for creating a new board
router.post('/boards', authenticateToken, createBoard);

router.post('/boards/delete/:boardId', authenticateToken, authenticateAndAuthorize('board'), deleteBoard);
router.post('/boards/archive/:boardId', authenticateToken, authenticateAndAuthorize('board'), archiveBoard);
router.put('/boards/:boardId/visibility', authenticateToken, authenticateAndAuthorize('board'), updateBoardVisibility);

router.post('/boards/:boardId/last-viewed', authenticateToken, authenticateAndAuthorize('board'), lastViewedBoard);
router.get('/boards/recent-viewed', authenticateToken, authenticateAndAuthorize('board'), getLastViewedBoard);
router.get('/boards/stared', authenticateToken, authenticateAndAuthorize('board'), getLastViewedBoard);

// Endpoint for fetching a specific board by ID
router.get('/boards/:boardId', authenticateToken, authenticateAndAuthorize('board'), getBoardById);
router.put('/boards/:boardId', authenticateToken, authenticateAndAuthorize('board'), updateBoard);
router.put('/boards/:boardId/order', authenticateToken, authenticateAndAuthorize('board'), updateBoardOrder);
router.get('/boards/:boardId/lists', authenticateToken, authenticateAndAuthorize('board'), getBoardLists);
router.put('/boards/:boardId/cards/order', authenticateToken, authenticateAndAuthorize('board'), updateCardsOrder);

// Add/Remove member to board route
router.get('/boards/:boardId/members', authenticateToken, authenticateAndAuthorize('board'), getBoardMembers);
router.post('/boards/:boardId/member', authenticateToken, authenticateAndAuthorize('board'), addMemberToBoard);
router.put('/boards/:boardId/member', authenticateToken, authenticateAndAuthorize('board'), updateMemberRole);
router.delete('/boards/:boardId/member', authenticateToken, authenticateAndAuthorize('board'), removeMemberFromBoard);

// Routes for labels
router.post('/boards/:boardId/labels', authenticateToken, authenticateAndAuthorize('board'), addLabelToBoard);
router.get('/boards/:boardId/labels', authenticateToken, authenticateAndAuthorize('board'), getLabelsFromBoard);
router.put('/boards/:boardId/labels/:labelId', authenticateToken, authenticateAndAuthorize('board'), updateLabelOnBoard);
router.delete('/boards/:boardId/labels/:labelId', authenticateToken, authenticateAndAuthorize('board'), deleteLabelFromBoard);

//Share boards
router.post('/boards/:boardId/invite', authenticateToken, sendInvitationRequest);
router.post('/boards/accept', acceptInvitationRequest);

router.post('/boards/:boardId/generate-join-link', authenticateToken, authenticateAndAuthorize('board'), generateJoinLink);
router.delete('/boards/:boardId/delete-join-link', authenticateToken, authenticateAndAuthorize('board'), deleteJoinLink);
router.post('/boards/join-board', authenticateToken, joinByLink);

router.post('/boards/:boardId/background', authenticateToken, authenticateAndAuthorize('board'), uploadMiddleware, uploadBackground);
router.put('/boards/:boardId/background', authenticateToken, authenticateAndAuthorize('board'), updateBackground);
router.post('/boards/boards-background', authenticateToken, uploadBackgroundFromUnsplash);
router.get('/unsplash', authenticateToken, getUnsplashPhotos);
router.get('/boards/:boardId/backgrounds', getBackgroundPhotos);

router.post('/trello/import', authenticateToken, importTrelloBoards);


module.exports = router;

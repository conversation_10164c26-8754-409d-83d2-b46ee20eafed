const express = require('express');
const router = express.Router();

const UploadImport = require('../middleware/import');

const {
    getAllWorkspaces,
    getWorkspaces,
    getLiteWorkspaces,
    getGuestWorkspaces,
    getWorkspaceById,
    getWorkspaceByUserId,
    createWorkspace,
    deleteWorkspace,
    addMemberToWorkspace,
    removeMemberFromWorkspace,
    getWorkspaceByShortName,
    importBoard,
    updateMemberRole,
    sendInvitationRequest,
    acceptInvitationRequest,
    generateJoinLink,
    deleteJoinLink,
    joinByLink,
    getSearchResults
} = require('../controllers/workspaceController');

const { authenticateToken, authenticateAndAuthorize } = require('../middleware/authMiddleware');

// Fetch all workspaces
router.get('/workspaces', authenticateToken, authenticateAndAuthorize(''), getAllWorkspaces);

// Fetch workspaces for the logged-in user
router.get('/workspaces/me', authenticateToken, authenticateAndAuthorize(''), getWorkspaces);
router.get('/workspaces/lite/me', authenticateToken, authenticateAndAuthorize(''), getLiteWorkspaces);
router.get('/workspaces/guests/me', authenticateToken, authenticateAndAuthorize(''), getGuestWorkspaces); //
router.get('/workspaces/:userId', authenticateToken, authenticateAndAuthorize(''), getWorkspaceByUserId); //

// Endpoint for creating a new workspace
router.post('/workspaces/me', authenticateToken, authenticateAndAuthorize(''), createWorkspace);
router.post('/workspaces/:workspaceId', authenticateToken, authenticateAndAuthorize(''), deleteWorkspace);

// Fetch a specific workspace by ID
router.get('/workspaces/:workspaceId', authenticateToken, authenticateAndAuthorize(''), getWorkspaceById);
router.get('/workspaces/shortname/:workspaceShortName', authenticateToken, authenticateAndAuthorize(''), getWorkspaceByShortName);

// Add member to workspace route
// router.post('/workspace/:workspaceId/add-member', authenticateToken,  authenticateAndAuthorize(''),addMemberToWorkspace);
// router.post('/workspace/:workspaceId/remove-member', authenticateToken,  authenticateAndAuthorize(''),removeMemberFromWorkspace);
router.post('/workspace/:workspaceId/member', authenticateToken, authenticateAndAuthorize(''), addMemberToWorkspace);
router.put('/workspace/:workspaceId/member', authenticateToken, authenticateAndAuthorize(''), updateMemberRole);
router.delete('/workspace/:workspaceId/member', authenticateToken, authenticateAndAuthorize(''), removeMemberFromWorkspace);

// router.post('/:workspaceId/import', authenticateToken, authenticateAndAuthorize(''), UploadImport.single('file'), importBoard);

// share workspace
router.post('/workspace/:workspaceId/invite', authenticateToken, sendInvitationRequest);

router.post('/workspace/accept', acceptInvitationRequest);

router.post('/workspace/:workspaceId/generate-join-link', authenticateToken, authenticateAndAuthorize(''), generateJoinLink);
router.delete('/workspace/:workspaceId/delete-join-link', authenticateToken, authenticateAndAuthorize(''), deleteJoinLink);
router.post('/workspace/join-workspace', authenticateToken, joinByLink);
router.post('/global/search', authenticateToken, getSearchResults);

module.exports = router;

const express = require('express');
const router = express.Router();

const { authenticateToken } = require('../middleware/authMiddleware');

const {
    watch,
    unwatch,
    getNotifications,
    addNewNotificationsCount,
    getNewNotificationsCount,
    sendPushNotificationHandler,
    updateNotificationTokens,
    markAsAllRead,
    mark<PERSON>Read
} = require('../controllers/notificationController');

// watch
router.post('/cards/:cardId/watch', authenticateToken, watch);
router.post('/cards/:cardId/unwatch', authenticateToken, unwatch);

// Get all notifications for the logged-in user
router.get('/notifications', authenticateToken, getNotifications);
router.post('/notifications/count', authenticateToken, addNewNotificationsCount);
router.get('/notifications/count', authenticateToken, getNewNotificationsCount);
router.post('/notifications/push/send', authenticateToken, sendPushNotificationHandler);
router.post('/notifications/push/update', authenticateToken, updateNotificationTokens);
router.put('/notifications/mark-all-read', authenticateToken, markAsAllRead);
router.put('/notifications/:notificationId', authenticateToken, markAsRead);


module.exports = router;

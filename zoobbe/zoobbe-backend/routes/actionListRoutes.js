const express = require('express');
const router = express.Router();
const { createActionList, updateActionList, archiveActionListCards, watchActionList, getActionLists, getArchivedLists, getActionListById, getActionListByBoardId, deleteActionList, archiveActionList, copyActionList } = require('../controllers/actionListController');

const { authenticateToken, authenticateAndAuthorize } = require('../middleware/authMiddleware');


// Endpoint for creating a new action list
router.post('/actionLists', authenticateToken, authenticateAndAuthorize('board'), createActionList);
router.get('/actionLists', authenticateToken,authenticateAndAuthorize('board'),  getActionLists);
router.get('/:boardId/lists/archived/', authenticateToken, authenticateAndAuthorize('board'), getArchivedLists);

router.put('/actionLists/:actionListId', authenticateToken,authenticateAndAuthorize('board'), updateActionList);
router.delete('/actionLists/:actionListId', authenticateToken, authenticateAndAuthorize('board'), deleteActionList);
router.put('/actionLists/archive/:actionListId', authenticateToken, authenticateAndAuthorize('board'), archiveActionList);
router.post('/actionLists/:actionListId/cards/archived', authenticateToken, authenticateAndAuthorize('board'), archiveActionListCards);

// Endpoint for fetching a specific action list by ID
router.get('/actionLists/:actionListId', authenticateToken, authenticateAndAuthorize('board'), getActionListById);

// Endpoint for fetching a all action lists by boardID
router.get('/:boardId/actionLists/', authenticateToken, authenticateAndAuthorize('board'), getActionListByBoardId);

router.post('/actionLists/:actionListId/watch', authenticateToken, authenticateAndAuthorize('board'), watchActionList);
router.post('/actionLists/:actionListId/copy', authenticateToken, authenticateAndAuthorize('board'), copyActionList);

module.exports = router;

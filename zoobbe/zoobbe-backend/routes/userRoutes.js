const express = require('express');
const {
  registerUser,
  resendVerificationEmail,
  loginUser,
  sendRecoverEmail,
  resetPassword,
  setPassword,
  verifyEmail,
  refreshToken,
  authWithGoogle,
  getUsers,
  getUserById,
  getUserByUserName,
  getActivities,
  getUserActivities,
  filters,
  getFilters,
  getSearchResults,
  uploadPicture,
  updateUserProfile,
  updateTheme,
  updateStared,
  getStaredBoards,
  getRecentBoards,
  updateSettings,
  getSettings,
  connectWithSlack,
  disconnectFromSlack,
  getMe,
} = require('../controllers/userController');

const { authenticateToken } = require('../middleware/authMiddleware');
const User = require('../models/User');
const upload = require('../middleware/uploadProfilePicture');

const router = express.Router();

// Authentication Routes
router.post('/register', registerUser);
router.post('/login', loginUser);
router.post('/reset', resetPassword);
router.post('/set-password', setPassword);
router.post('/recovery', sendRecoverEmail);
router.post('/verify', verifyEmail);
router.get('/refresh-token', refreshToken);
router.post('/resend-verification', resendVerificationEmail);

router.post('/auth/google', authWithGoogle);

// User Management Routes
router.get('/all-users', authenticateToken, getUsers);
router.get('/me', authenticateToken, getMe);
router.get('/:userId', authenticateToken, getUserById);
router.get('/username/:userName', authenticateToken, getUserByUserName);
router.get('/me/activities', authenticateToken, getActivities);
router.get('/:userName/activities', authenticateToken, getUserActivities);
router.post('/update', authenticateToken, updateUserProfile);
router.put('/theme', authenticateToken, updateTheme);
router.post('/upload-profile-picture', authenticateToken, upload.single('profilePicture'), uploadPicture);

// Filter Routes
router.put('/me/filters', authenticateToken, filters);
router.get('/me/filters', authenticateToken, getFilters);

router.put('/me/stared', authenticateToken, updateStared);
router.get('/me/get-starred', authenticateToken, getStaredBoards);

router.get('/me/recent-viewed', authenticateToken, getRecentBoards);

// Settings Routes
router.put('/me/settings', authenticateToken, updateSettings);
router.get('/me/settings', authenticateToken, getSettings);

//connect with server
router.post('/slack/auth/callback', authenticateToken, connectWithSlack);
router.post('/slack/auth/disconnect', authenticateToken, disconnectFromSlack);

router.get("/online", authenticateToken, async (req, res) => {
  const users = await User.find({ online: true }, "name email profilePicture");
  res.json(users);
});

module.exports = router;

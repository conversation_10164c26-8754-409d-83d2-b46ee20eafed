{"name": "zoobbe-backend", "version": "1.0.03", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "preinstall": "npm install --include=optional sharp"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.674.0", "@aws-sdk/cloudfront-signer": "^3.734.0", "@aws-sdk/lib-storage": "^3.749.0", "@aws-sdk/s3-request-presigner": "^3.674.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "colorthief": "^2.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.1.0", "google-auth-library": "^9.15.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.4.1", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.7", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "sharp": "^0.33.5", "socket.io": "^4.7.5", "stream-json": "^1.8.0", "stripe": "^18.0.0"}, "devDependencies": {"JSONStream": "^1.3.5", "nodemon": "^3.1.10"}}
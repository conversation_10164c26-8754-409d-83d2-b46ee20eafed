const { Server } = require('socket.io');
const User = require('./models/User');

const setupSocket = (server) => {
    const corsOptions = {
        origin: [
            process.env.SITE_URL,
            'https://zoobbe.com',
            'http://zoobbe.com',
            'https://www.zoobbe.com',
            'http://www.zoobbe.com',
            'https://beta.zoobbe.com',
            'https://www.beta.zoobbe.com',
            'https://beta.zoobbe.com',
            'http://localhost:3000'
        ],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
        optionsSuccessStatus: 200,
    };

    const io = new Server(server, {
        cors: corsOptions,
    });

    const onlineUsers = new Map();
    const userSockets = new Map(); // Track multiple sockets per user

    io.on("connection", (socket) => {

        socket.on("join", async (userId) => {
            if (!userId) return;

            socket.join(userId);
            onlineUsers.set(socket.id, userId);

            // Store multiple sockets per user (for multi-device support)
            if (!userSockets.has(userId)) {
                userSockets.set(userId, new Set());
            }
            userSockets.get(userId).add(socket.id);

            console.log(`User ${userId} joined`);
            await User.findByIdAndUpdate(userId, { online: true });
            io.emit("user-online-status", { userId, online: true });

            // Heartbeat check every 10s (detect stale online status)
            socket.heartbeatInterval = setInterval(async () => {
                if (!socket.connected) {
                    handleDisconnect(socket);
                }
            }, 10000);
        });

        socket.on("leave", async (userId) => {
            handleDisconnect(socket, userId);
        });

        socket.on("disconnect", () => {
            handleDisconnect(socket);
        });

        async function handleDisconnect(socket, userId = null) {
            userId = userId || onlineUsers.get(socket.id);
            if (!userId) return;

            console.log(`User ${userId} disconnected`);
            onlineUsers.delete(socket.id);
            userSockets.get(userId)?.delete(socket.id);

            // Remove user from tracking if all sockets are gone
            if (!userSockets.get(userId)?.size) {
                userSockets.delete(userId);
                await User.findByIdAndUpdate(userId, { online: false, lastActive: new Date() });
                io.emit("user-online-status", { userId, online: false });
            }

            clearInterval(socket.heartbeatInterval);
        }
    });


    return io;
};

module.exports = setupSocket;

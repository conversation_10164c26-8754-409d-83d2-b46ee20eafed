{"name": "zoobbe-frontend", "version": "0.1.0", "private": true, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.3.0", "@atlaskit/pragmatic-drag-and-drop-auto-scroll": "^1.4.0", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "@lexical/html": "^0.18.0", "@lexical/react": "^0.18.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.6", "@tanstack/react-virtual": "^3.10.9", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "date-fns": "^3.6.0", "firebase": "^11.3.1", "lexical": "^0.18.0", "lexical-beautiful-mentions": "^0.1.43", "marked": "^14.1.3", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^5.0.0", "react-cloudflare-turnstile": "^1.1.11", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-google-recaptcha-v3": "^1.10.1", "react-helmet": "^6.1.0", "react-loading-skeleton": "^3.4.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "redis": "^4.7.0", "redux": "^5.0.1", "sass": "^1.77.5", "short-uuid": "^5.2.0", "socket.io-client": "^4.7.5", "tiny-invariant": "^1.3.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
.ImageNode__contentEditable{border:0;caret-color:#050505;cursor:text;display:block;font-size:12px;min-height:20px;outline:0;padding:10px;position:relative;resize:none;-webkit-user-select:text;user-select:text;white-space:pre-wrap;width:calc(100% - 20px);word-break:break-word}.ImageNode__placeholder{color:#888;display:inline-block;font-size:12px;left:10px;overflow:hidden;pointer-events:none;position:absolute;text-overflow:ellipsis;top:10px;-webkit-user-select:none;user-select:none;white-space:nowrap}.image-control-wrapper--resizing{touch-action:none}
/*# sourceMappingURL=840.949c067d.chunk.css.map*/
{"version": 3, "file": "static/css/840.949c067d.chunk.css", "mappings": "AASA,4BAEE,QAAW,CAGX,mBAAyB,CADzB,WAAY,CAEZ,aAAc,CAKd,cAAe,CAVf,eAAgB,CAOhB,SAAY,CACZ,YAAa,CAFb,iBAAkB,CAJlB,WAAY,CAOZ,wBAAiB,CAAjB,gBAAiB,CAGjB,oBAAqB,CADrB,uBAAwB,CAExB,qBACF,CAEA,wBAEE,UAAW,CAQX,oBAAqB,CATrB,cAAe,CAMf,SAAU,CAJV,eAAgB,CAQhB,mBAAoB,CAPpB,iBAAkB,CAClB,sBAAuB,CACvB,QAAS,CAET,wBAAiB,CAAjB,gBAAiB,CACjB,kBAGF,CAEA,iCACE,iBACF", "sources": ["components/LexicalEditor/nodes/ImageNode.css"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n *\n */\n\n.ImageNode__contentEditable {\n  min-height: 20px;\n  border: 0px;\n  resize: none;\n  cursor: text;\n  caret-color: rgb(5, 5, 5);\n  display: block;\n  position: relative;\n  outline: 0px;\n  padding: 10px;\n  user-select: text;\n  font-size: 12px;\n  width: calc(100% - 20px);\n  white-space: pre-wrap;\n  word-break: break-word;\n}\n\n.ImageNode__placeholder {\n  font-size: 12px;\n  color: #888;\n  overflow: hidden;\n  position: absolute;\n  text-overflow: ellipsis;\n  top: 10px;\n  left: 10px;\n  user-select: none;\n  white-space: nowrap;\n  display: inline-block;\n  pointer-events: none;\n}\n\n.image-control-wrapper--resizing {\n  touch-action: none;\n}\n"], "names": [], "sourceRoot": ""}
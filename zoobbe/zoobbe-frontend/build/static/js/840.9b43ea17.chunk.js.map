{"version": 3, "file": "static/js/840.9b43ea17.chunk.js", "mappings": "6LAQsD,MAAMA,EAAE,CAAC,CAAC,MAAM,mBAAmB,CAAC,MAAM,kBAAkB,CAAC,SAAS,kBAAkB,CAAC,OAAO,kBAAkB,CAAC,MAAM,mBAAmB,CAAC,WAAW,iBAAiB,CAAC,SAAS,kBAAkB,CAAC,WAAW,mBAAmB,CAAC,OAAO,oBAAoB,CAAC,QAAQ,kBAAkB,CAAC,UAAU,kBAAkB,CAAC,QAAQ,kBAAkB,CAAC,OAAO,oBAAoB,CAAC,MAAM,oBAAoB,CAAC,OAAO,oBAAoB,CAAC,QAAQ,qBAAqBC,EAAED,EAAEE,KAAKC,MAAMD,KAAKE,SAASJ,EAAEK,SAASC,GAAEC,EAAAA,EAAAA,eAAE,CAACC,SAAS,EAAEC,MAAMR,EAAE,GAAGS,gBAAe,EAAGC,KAAKV,EAAE,GAAGW,UAAU,IAAIC,M,qDCgBvmB,MACA,EAAe,IAA0B,iE,aCrBzC,SAASC,EAAMC,EAAOC,EAAKC,GACzB,OAAOf,KAAKc,IAAId,KAAKe,IAAIF,EAAOC,GAAMC,EACxC,CAEA,MAAMC,EAAY,CAChBC,KAAM,EACNC,MAAO,EACPC,MAAO,EACPC,KAAM,GAGO,SAASC,EAAYC,GAUhC,IAViC,cACnCC,EAAa,YACbC,EAAW,UACXC,EAAS,SACTC,EAAQ,SACRC,EAAQ,OACRC,EAAM,YACNC,EAAW,eACXC,EAAc,gBACdC,GACDT,EACC,MAAMU,GAAoBC,EAAAA,EAAAA,QAAO,MAC3BC,GAAaD,EAAAA,EAAAA,QAAO,CACxBE,SAAU,GACVtB,MAAO,YAEHuB,GAAiBH,EAAAA,EAAAA,QAAO,CAC5BI,cAAe,EACfC,aAAc,EACdC,UAAW,EACXC,YAAY,EACZC,MAAO,EACPC,YAAa,EACbC,WAAY,EACZC,OAAQ,EACRC,OAAQ,IAEJC,EAAoBlB,EAAOmB,iBAE3BC,EAAoBrB,IAEA,OAAtBmB,EACEA,EAAkBG,wBAAwBC,MAAQ,GAClD,KACAC,EACkB,OAAtBL,EACIA,EAAkBG,wBAAwBG,OAAS,GACnD,IAuDAC,EAAoBA,CAACC,EAAOf,KAChC,IAAKX,EAAO2B,aACV,OAGF,MAAMC,EAAQ9B,EAAS+B,QACjBC,EAAiB1B,EAAkByB,QAEzC,GAAc,OAAVD,GAAqC,OAAnBE,EAAyB,CAC7CJ,EAAMK,iBACN,MAAM,MAAET,EAAK,OAAEE,GAAWI,EAAMP,wBAC1BW,GAAOC,EAAAA,EAAAA,IAAmBL,GAC1BM,EAAc1B,EAAeqB,QACnCK,EAAYnB,WAAaO,EACzBY,EAAYpB,YAAcU,EAC1BU,EAAYrB,MAAQS,EAAQE,EAC5BU,EAAYxB,aAAeY,EAC3BY,EAAYzB,cAAgBe,EAC5BU,EAAYlB,OAASU,EAAMS,QAAUH,EACrCE,EAAYjB,OAASS,EAAMU,QAAUJ,EACrCE,EAAYtB,YAAa,EACzBsB,EAAYvB,UAAYA,EAvELA,KACrB,MAAM0B,EAAK1B,IAAcvB,EAAUC,MAAQsB,IAAcvB,EAAUI,KAC7D8C,EAAK3B,IAAcvB,EAAUE,OAASqB,IAAcvB,EAAUG,MAC9DgD,EACH5B,EAAYvB,EAAUE,OAASqB,EAAYvB,EAAUI,MACrDmB,EAAYvB,EAAUG,OAASoB,EAAYvB,EAAUC,KAElDmD,EAAYH,EAAK,KAAOC,EAAK,KAAOC,EAAO,OAAS,OAEhC,OAAtBrB,GACFA,EAAkBuB,MAAMC,YACtB,SACA,GAAGF,WACH,aAGkB,OAAlBG,SAASC,OACXD,SAASC,KAAKH,MAAMC,YAClB,SACA,GAAGF,WACH,aAEFlC,EAAWuB,QAAQ5C,MAAQ0D,SAASC,KAAKH,MAAMI,iBAC7C,uBAEFvC,EAAWuB,QAAQtB,SAAWoC,SAASC,KAAKH,MAAMK,oBAChD,uBAEFH,SAASC,KAAKH,MAAMC,YAClB,sBACA,OACA,aAEJ,EAwCEK,CAAepC,GACfhB,IAEAmC,EAAekB,UAAUC,IAAI,mCAC7BrB,EAAMa,MAAMjB,OAAS,GAAGA,MACxBI,EAAMa,MAAMnB,MAAQ,GAAGA,MAEvBqB,SAASO,iBAAiB,cAAeC,GACzCR,SAASO,iBAAiB,YAAaE,EACzC,GAEID,EAAoBzB,IACxB,MAAME,EAAQ9B,EAAS+B,QACjBK,EAAc1B,EAAeqB,QAE7BwB,EACJnB,EAAYvB,WAAavB,EAAUC,KAAOD,EAAUI,MAChD8D,EACJpB,EAAYvB,WAAavB,EAAUG,MAAQH,EAAUE,OAEvD,GAAc,OAAVsC,GAAkBM,EAAYtB,WAAY,CAC5C,MAAMoB,GAAOC,EAAAA,EAAAA,IAAmBL,GAEhC,GAAIyB,GAAgBC,EAAY,CAC9B,IAAIC,EAAOnF,KAAKC,MAAM6D,EAAYlB,OAASU,EAAMS,QAAUH,GAC3DuB,EAAOrB,EAAYvB,UAAYvB,EAAUC,MAAQkE,EAAOA,EAExD,MAAMjC,EAAQtC,EACZkD,EAAYnB,WAAawC,EAxGhB,IA0GTnC,GAGII,EAASF,EAAQY,EAAYrB,MACnCe,EAAMa,MAAMnB,MAAQ,GAAGA,MACvBM,EAAMa,MAAMjB,OAAS,GAAGA,MACxBU,EAAYzB,cAAgBe,EAC5BU,EAAYxB,aAAeY,CAC7B,MAAO,GAAIgC,EAAY,CACrB,IAAIC,EAAOnF,KAAKC,MAAM6D,EAAYjB,OAASS,EAAMU,QAAUJ,GAC3DuB,EAAOrB,EAAYvB,UAAYvB,EAAUG,OAASgE,EAAOA,EAEzD,MAAM/B,EAASxC,EACbkD,EAAYpB,YAAcyC,EAtHhB,IAwHVhC,GAGFK,EAAMa,MAAMjB,OAAS,GAAGA,MACxBU,EAAYzB,cAAgBe,CAC9B,KAAO,CACL,IAAI+B,EAAOnF,KAAKC,MAAM6D,EAAYlB,OAASU,EAAMS,QAAUH,GAC3DuB,EAAOrB,EAAYvB,UAAYvB,EAAUC,MAAQkE,EAAOA,EAExD,MAAMjC,EAAQtC,EACZkD,EAAYnB,WAAawC,EAnIhB,IAqITnC,GAGFQ,EAAMa,MAAMnB,MAAQ,GAAGA,MACvBY,EAAYxB,aAAeY,CAC7B,CACF,GAEI8B,EAAkBA,KACtB,MAAMxB,EAAQ9B,EAAS+B,QACjBK,EAAc1B,EAAeqB,QAC7BC,EAAiB1B,EAAkByB,QACzC,GAAc,OAAVD,GAAqC,OAAnBE,GAA2BI,EAAYtB,WAAY,CACvE,MAAMU,EAAQY,EAAYxB,aACpBc,EAASU,EAAYzB,cAC3ByB,EAAYnB,WAAa,EACzBmB,EAAYpB,YAAc,EAC1BoB,EAAYrB,MAAQ,EACpBqB,EAAYlB,OAAS,EACrBkB,EAAYjB,OAAS,EACrBiB,EAAYxB,aAAe,EAC3BwB,EAAYzB,cAAgB,EAC5ByB,EAAYtB,YAAa,EAEzBkB,EAAekB,UAAUQ,OAAO,mCArHR,OAAtBtC,GACFA,EAAkBuB,MAAMC,YAAY,SAAU,QAE1B,OAAlBC,SAASC,OACXD,SAASC,KAAKH,MAAMC,YAAY,SAAU,WAC1CC,SAASC,KAAKH,MAAMC,YAClB,sBACApC,EAAWuB,QAAQ5C,MACnBqB,EAAWuB,QAAQtB,WAgHrBX,EAAY0B,EAAOE,GAEnBmB,SAASc,oBAAoB,cAAeN,GAC5CR,SAASc,oBAAoB,YAAaL,EAC5C,GAEF,OACEM,EAAAA,EAAAA,MAAA,OAAKC,IAAKvD,EAAkBwD,SAAA,EAY1BC,EAAAA,EAAAA,KAAA,OACEC,UAAU,gCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUE,MAAM,KAG7CuE,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUE,MAAQF,EAAUC,KAAK,KAG9DwE,EAAAA,EAAAA,KAAA,OACEC,UAAU,gCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUC,KAAK,KAG5CwE,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUG,MAAQH,EAAUC,KAAK,KAG9DwE,EAAAA,EAAAA,KAAA,OACEC,UAAU,gCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUG,MAAM,KAG7CsE,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUG,MAAQH,EAAUI,KAAK,KAG9DqE,EAAAA,EAAAA,KAAA,OACEC,UAAU,gCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUI,KAAK,KAG5CqE,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVC,cAAerC,IACbD,EAAkBC,EAAOtC,EAAUE,MAAQF,EAAUI,KAAK,MAKpE,C,cC9OA,MAAMwE,EAAa,IAAIC,IAEVC,GAA4BC,EAAAA,EAAAA,IACvC,6BAmBF,SAASC,EAAS1E,GASd,IATe,QACjB2E,EAAO,UACPP,EAAS,SACThE,EAAQ,IACRwE,EAAG,MACHhD,EAAK,OACLE,EAAM,SACNzB,EAAQ,QACRwE,GACD7E,EAEC,OA3BF,SAA0B4E,GACxB,IAAKN,EAAWQ,IAAIF,GAClB,MAAM,IAAIG,SAAQC,IAChB,MAAMC,EAAM,IAAIC,MAChBD,EAAIL,IAAMA,EACVK,EAAIE,OAAS,KACXb,EAAWf,IAAIqB,GACfI,EAAQ,KAAK,EAEfC,EAAIG,QAAU,KACZd,EAAWf,IAAIqB,EAAI,CACpB,GAGP,CAYES,CAAiBT,IAEfT,EAAAA,EAAAA,KAAA,OACEC,UAAWA,QAAakB,EACxBV,IAAKA,EACLW,IAAKZ,EACLV,IAAK7D,EACL2C,MAAO,CACLjB,SACAzB,WACAuB,SAEFiD,QAASA,EACTW,UAAU,QACVC,QAAQ,QAGd,CAEA,SAASC,IACP,OACEvB,EAAAA,EAAAA,KAAA,OACES,IAAKe,EACL5C,MAAO,CACLjB,OAAQ,IACR8D,QAAS,GACThE,MAAO,KAET4D,UAAU,QACVC,QAAQ,QAGd,CAEe,SAASI,EAAcC,GAWlC,IAXmC,IACrClB,EAAG,QACHD,EAAO,QACPoB,EAAO,MACPnE,EAAK,OACLE,EAAM,SACNzB,EAAQ,UACR2F,EAAS,YACTzF,EAAW,QACX0F,EAAO,gBACPxF,GACDqF,EACC,MAAM1F,GAAWO,EAAAA,EAAAA,QAAO,MAClBR,GAAYQ,EAAAA,EAAAA,QAAO,OAClBuF,EAAYC,EAAaC,IAAkBC,EAAAA,EAAAA,GAChDN,IAEK7E,EAAYoF,IAAiBC,EAAAA,EAAAA,WAAS,IACvC,eAAErH,GHzHmmB,SAAWH,EAAEP,GAAG,MAAMC,GAAE+H,EAAAA,EAAAA,YAAE1H,GAAG,OAAO,MAAMC,IAAIN,EAAEU,KAAKJ,GAAG,MAAMP,IAAIC,EAAEQ,MAAMT,GAAGC,CAAC,CGyH9pBgI,IACpBnG,IAAUoG,EAAAA,EAAAA,OACVC,EAAWC,IAAgBL,EAAAA,EAAAA,UAAS,MACrCM,GAAkBlG,EAAAA,EAAAA,QAAO,OACxBmG,EAAaC,IAAkBR,EAAAA,EAAAA,WAAS,GACzCtE,GAAa+E,EAAAA,EAAAA,KAEbC,GAAYC,EAAAA,EAAAA,cAChBC,IACE,MAAMC,GAAkBC,EAAAA,EAAAA,MACxB,GAAInB,IAAcoB,EAAAA,EAAAA,IAAiBF,GAAkB,CACrCD,EACR9E,iBACN/B,EAAOiH,QAAO,KACZH,EAAgBI,WAAWC,SAAQC,KAC7BC,EAAAA,EAAAA,IAAaD,IACfA,EAAK5D,QACP,GACA,GAEN,CACA,OAAO,CAAK,GAEd,CAACxD,EAAQ4F,IAGL0B,GAAWV,EAAAA,EAAAA,cACflF,IACE,MAAM6F,GAAkBR,EAAAA,EAAAA,MAClBS,EAAa3H,EAAUgC,QAC7B,GACE+D,IACAoB,EAAAA,EAAAA,IAAiBO,IACqB,IAAtCA,EAAgBL,WAAW3I,OAC3B,CACA,GAAI0B,EAKF,OAHAwH,EAAAA,EAAAA,IAAc,MACd/F,EAAMK,iBACN4D,EAAQ+B,SACD,EACF,GACU,OAAfF,GACAA,IAAe7E,SAASgF,cAIxB,OAFAjG,EAAMK,iBACNyF,EAAWE,SACJ,CAEX,CACA,OAAO,CAAK,GAEd,CAAC/B,EAASC,EAAY3F,IAGlB2H,GAAYhB,EAAAA,EAAAA,cAChBlF,IAEI6E,EAAgB1E,UAAY8D,GAC5B9F,EAAUgC,UAAYH,EAAMmG,WAE5BJ,EAAAA,EAAAA,IAAc,MACdzH,EAAOiH,QAAO,KACZpB,GAAY,GACZ,MAAMiC,EAAoB9H,EAAOmB,iBACP,OAAtB2G,GACFA,EAAkBJ,OACpB,KAEK,IAIX,CAAC/B,EAAS3F,EAAQ6F,IAGdkC,GAAUnB,EAAAA,EAAAA,cACdC,IACE,MAAMnF,EAAQmF,EAEd,QAAIjG,GAGAc,EAAMmG,SAAW/H,EAAS+B,UACxBH,EAAMsG,SACRnC,GAAaD,IAEbE,IACAD,GAAY,KAEP,EAGG,GAEd,CAACjF,EAAYgF,EAAYC,EAAaC,IAGlCmC,GAAerB,EAAAA,EAAAA,cACnBlF,IACE1B,EAAOkI,iBAAiBC,MAAK,KAC3B,MAAMZ,GAAkBR,EAAAA,EAAAA,MAGC,QAFNrF,EAAMmG,OAEZO,UACXC,EAAAA,EAAAA,IAAkBd,IACoB,IAAtCA,EAAgBL,WAAW3I,QAE3ByB,EAAOsI,gBAAgBpE,EAA2BxC,EACpD,GACA,GAEJ,CAAC1B,KAGHuI,EAAAA,EAAAA,YAAU,KACR,IAAIC,GAAY,EAChB,MAAMC,EAAczI,EAAOmB,iBACrBuH,GAAaC,EAAAA,EAAAA,IACjB3I,EAAO4I,wBAAuBC,IAAsB,IAArB,YAAEC,GAAaD,EACxCL,GACFlC,EAAawC,EAAYX,MAAK,KAAMpB,EAAAA,EAAAA,QACtC,IAEF/G,EAAO+I,gBACLC,EAAAA,IACA,CAACC,EAAGC,KACF3C,EAAgB1E,QAAUqH,GACnB,IAETC,EAAAA,IAEFnJ,EAAO+I,gBAAgBK,EAAAA,GAAerB,EAASoB,EAAAA,IAC/CnJ,EAAO+I,gBACL7E,EACA6D,EACAoB,EAAAA,IAEFnJ,EAAO+I,gBACLM,EAAAA,IACA3H,GACMA,EAAMmG,SAAW/H,EAAS+B,UAG5BH,EAAMK,kBACC,IAIXoH,EAAAA,IAEFnJ,EAAO+I,gBACLO,EAAAA,GACA3C,EACAwC,EAAAA,IAEFnJ,EAAO+I,gBACLQ,EAAAA,GACA5C,EACAwC,EAAAA,IAEFnJ,EAAO+I,gBAAgBS,EAAAA,GAAmBlC,EAAU6B,EAAAA,IACpDnJ,EAAO+I,gBACLU,EAAAA,GACA7B,EACAuB,EAAAA,KAMJ,OAFW,OAAXV,QAAW,IAAXA,GAAAA,EAAavF,iBAAiB,cAAe+E,GAEtC,KACLO,GAAY,EACZE,IACW,OAAXD,QAAW,IAAXA,GAAAA,EAAahF,oBAAoB,cAAewE,EAAa,CAC9D,GACA,CACDnC,EACA9F,EACAY,EACAgF,EACAH,EACAkB,EACAW,EACAM,EACAG,EACAE,EACApC,IA+BF6D,QAAQC,IAAI/D,GAEZ,MAAMV,EAAYU,IAAcoB,EAAAA,EAAAA,IAAiBX,KAAezF,EAC1DgJ,GAAahE,GAAchF,IAAee,EAChD,OACEkC,EAAAA,EAAAA,KAACgG,EAAAA,SAAQ,CAACC,SAAU,KAAKlG,UACvBC,EAAAA,EAAAA,KAAAkG,EAAAA,SAAA,CAAAnG,UACEF,EAAAA,EAAAA,MAAA,OAAKwB,UAAWA,EAAUtB,SAAA,CACvB4C,GACC3C,EAAAA,EAAAA,KAACuB,EAAW,KAEZvB,EAAAA,EAAAA,KAACO,EAAS,CACRN,UACE8F,EACI,aAAW5C,EAAAA,EAAAA,IAAiBX,GAAa,YAAc,IACvD,KAEN/B,IAAKA,EACLD,QAASA,EACTvE,SAAUA,EACVwB,MAAOA,EACPE,OAAQA,EACRzB,SAAUA,EACVwE,QAASA,IAAMkC,GAAe,KAGjCf,IAAasB,EAAAA,EAAAA,IAAiBX,IAAcuD,IAC3C/F,EAAAA,EAAAA,KAACpE,EAAY,CACXQ,YAAaA,EACbC,eAzDWA,KACrBF,EAAOiH,QAAO,KACZ,MAAMG,GAAO4C,EAAAA,EAAAA,IAAcvE,IACvB4B,EAAAA,EAAAA,IAAaD,IACfA,EAAKlH,gBAAe,EACtB,GACA,EAoDQF,OAAQA,EACRH,UAAWA,EACXC,SAAUA,EACVC,SAAUA,EACVJ,cAvCUA,KACpBqG,GAAc,EAAK,EAuCTpG,YAtDQA,CAACqK,EAAWC,KAE9BC,YAAW,KACTnE,GAAc,EAAM,GACnB,KAEHhG,EAAOiH,QAAO,KACZ,MAAMG,GAAO4C,EAAAA,EAAAA,IAAcvE,IACvB4B,EAAAA,EAAAA,IAAaD,IACfA,EAAKgD,kBAAkBH,EAAWC,EACpC,GACA,EA4CQ/J,iBAAkBqG,GAAerG,UAS/C,C", "sources": ["../node_modules/@lexical/react/LexicalCollaborationContext.prod.mjs", "components/LexicalEditor/images/image-broken.svg", "components/LexicalEditor/ui/ImageResizer.js", "components/LexicalEditor/nodes/ImageComponent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport{createContext as r,useContext as g}from\"react\";const o=[[\"<PERSON>\",\"rgb(125, 50, 0)\"],[\"<PERSON>\",\"rgb(100, 0, 0)\"],[\"Rabbit\",\"rgb(150, 0, 0)\"],[\"Frog\",\"rgb(200, 0, 0)\"],[\"Fox\",\"rgb(200, 75, 0)\"],[\"Hedgehog\",\"rgb(0, 75, 0)\"],[\"Pigeon\",\"rgb(0, 125, 0)\"],[\"Squirrel\",\"rgb(75, 100, 0)\"],[\"<PERSON>\",\"rgb(125, 100, 0)\"],[\"<PERSON>\",\"rgb(0, 0, 150)\"],[\"<PERSON>pard\",\"rgb(0, 0, 200)\"],[\"Zebra\",\"rgb(0, 0, 250)\"],[\"<PERSON>\",\"rgb(0, 100, 150)\"],[\"Owl\",\"rgb(0, 100, 100)\"],[\"Gull\",\"rgb(100, 0, 100)\"],[\"Squid\",\"rgb(150, 0, 150)\"]],b=o[Math.floor(Math.random()*o.length)],e=r({clientID:0,color:b[1],isCollabActive:!1,name:b[0],yjsDocMap:new Map});function l(r,o){const b=g(e);return null!=r&&(b.name=r),null!=o&&(b.color=o),b}export{e as CollaborationContext,l as useCollaborationContext};\n", "var _path;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgImageBroken(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: \"800px\",\n    height: \"800px\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M22 3H2v18h20v-2h-2v-2h2v-2h-2v-2h2v-2h-2V9h2V7h-2V5h2V3zm-2 4v2h-2v2h2v2h-2v2h2v2h-2v2H4V5h14v2h2zm-6 2h-2v2h-2v2H8v2H6v2h2v-2h2v-2h2v-2h2v2h2v-2h-2V9zM6 7h2v2H6V7z\",\n    fill: \"#000000\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgImageBroken);\nexport default __webpack_public_path__ + \"static/media/image-broken.3b166d0bab97ea92c3c762c75d341727.svg\";\nexport { ForwardRef as ReactComponent };", "import { calculateZoomLevel } from \"@lexical/utils\"\nimport * as React from \"react\"\nimport { useRef } from \"react\"\n\nfunction clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max)\n}\n\nconst Direction = {\n  east: 1 << 0,\n  north: 1 << 3,\n  south: 1 << 1,\n  west: 1 << 2\n}\n\nexport default function ImageResizer({\n  onResizeStart,\n  onResizeEnd,\n  buttonRef,\n  imageRef,\n  maxWidth,\n  editor,\n  showCaption,\n  setShowCaption,\n  captionsEnabled\n}) {\n  const controlWrapperRef = useRef(null)\n  const userSelect = useRef({\n    priority: \"\",\n    value: \"default\"\n  })\n  const positioningRef = useRef({\n    currentHeight: 0,\n    currentWidth: 0,\n    direction: 0,\n    isResizing: false,\n    ratio: 0,\n    startHeight: 0,\n    startWidth: 0,\n    startX: 0,\n    startY: 0\n  })\n  const editorRootElement = editor.getRootElement()\n  // Find max width, accounting for editor padding.\n  const maxWidthContainer = maxWidth\n    ? maxWidth\n    : editorRootElement !== null\n      ? editorRootElement.getBoundingClientRect().width - 20\n      : 100\n  const maxHeightContainer =\n    editorRootElement !== null\n      ? editorRootElement.getBoundingClientRect().height - 20\n      : 100\n\n  const minWidth = 100\n  const minHeight = 100\n\n  const setStartCursor = direction => {\n    const ew = direction === Direction.east || direction === Direction.west\n    const ns = direction === Direction.north || direction === Direction.south\n    const nwse =\n      (direction & Direction.north && direction & Direction.west) ||\n      (direction & Direction.south && direction & Direction.east)\n\n    const cursorDir = ew ? \"ew\" : ns ? \"ns\" : nwse ? \"nwse\" : \"nesw\"\n\n    if (editorRootElement !== null) {\n      editorRootElement.style.setProperty(\n        \"cursor\",\n        `${cursorDir}-resize`,\n        \"important\"\n      )\n    }\n    if (document.body !== null) {\n      document.body.style.setProperty(\n        \"cursor\",\n        `${cursorDir}-resize`,\n        \"important\"\n      )\n      userSelect.current.value = document.body.style.getPropertyValue(\n        \"-webkit-user-select\"\n      )\n      userSelect.current.priority = document.body.style.getPropertyPriority(\n        \"-webkit-user-select\"\n      )\n      document.body.style.setProperty(\n        \"-webkit-user-select\",\n        `none`,\n        \"important\"\n      )\n    }\n  }\n\n  const setEndCursor = () => {\n    if (editorRootElement !== null) {\n      editorRootElement.style.setProperty(\"cursor\", \"text\")\n    }\n    if (document.body !== null) {\n      document.body.style.setProperty(\"cursor\", \"default\")\n      document.body.style.setProperty(\n        \"-webkit-user-select\",\n        userSelect.current.value,\n        userSelect.current.priority\n      )\n    }\n  }\n\n  const handlePointerDown = (event, direction) => {\n    if (!editor.isEditable()) {\n      return\n    }\n\n    const image = imageRef.current\n    const controlWrapper = controlWrapperRef.current\n\n    if (image !== null && controlWrapper !== null) {\n      event.preventDefault()\n      const { width, height } = image.getBoundingClientRect()\n      const zoom = calculateZoomLevel(image)\n      const positioning = positioningRef.current\n      positioning.startWidth = width\n      positioning.startHeight = height\n      positioning.ratio = width / height\n      positioning.currentWidth = width\n      positioning.currentHeight = height\n      positioning.startX = event.clientX / zoom\n      positioning.startY = event.clientY / zoom\n      positioning.isResizing = true\n      positioning.direction = direction\n\n      setStartCursor(direction)\n      onResizeStart()\n\n      controlWrapper.classList.add(\"image-control-wrapper--resizing\")\n      image.style.height = `${height}px`\n      image.style.width = `${width}px`\n\n      document.addEventListener(\"pointermove\", handlePointerMove)\n      document.addEventListener(\"pointerup\", handlePointerUp)\n    }\n  }\n  const handlePointerMove = event => {\n    const image = imageRef.current\n    const positioning = positioningRef.current\n\n    const isHorizontal =\n      positioning.direction & (Direction.east | Direction.west)\n    const isVertical =\n      positioning.direction & (Direction.south | Direction.north)\n\n    if (image !== null && positioning.isResizing) {\n      const zoom = calculateZoomLevel(image)\n      // Corner cursor\n      if (isHorizontal && isVertical) {\n        let diff = Math.floor(positioning.startX - event.clientX / zoom)\n        diff = positioning.direction & Direction.east ? -diff : diff\n\n        const width = clamp(\n          positioning.startWidth + diff,\n          minWidth,\n          maxWidthContainer\n        )\n\n        const height = width / positioning.ratio\n        image.style.width = `${width}px`\n        image.style.height = `${height}px`\n        positioning.currentHeight = height\n        positioning.currentWidth = width\n      } else if (isVertical) {\n        let diff = Math.floor(positioning.startY - event.clientY / zoom)\n        diff = positioning.direction & Direction.south ? -diff : diff\n\n        const height = clamp(\n          positioning.startHeight + diff,\n          minHeight,\n          maxHeightContainer\n        )\n\n        image.style.height = `${height}px`\n        positioning.currentHeight = height\n      } else {\n        let diff = Math.floor(positioning.startX - event.clientX / zoom)\n        diff = positioning.direction & Direction.east ? -diff : diff\n\n        const width = clamp(\n          positioning.startWidth + diff,\n          minWidth,\n          maxWidthContainer\n        )\n\n        image.style.width = `${width}px`\n        positioning.currentWidth = width\n      }\n    }\n  }\n  const handlePointerUp = () => {\n    const image = imageRef.current\n    const positioning = positioningRef.current\n    const controlWrapper = controlWrapperRef.current\n    if (image !== null && controlWrapper !== null && positioning.isResizing) {\n      const width = positioning.currentWidth\n      const height = positioning.currentHeight\n      positioning.startWidth = 0\n      positioning.startHeight = 0\n      positioning.ratio = 0\n      positioning.startX = 0\n      positioning.startY = 0\n      positioning.currentWidth = 0\n      positioning.currentHeight = 0\n      positioning.isResizing = false\n\n      controlWrapper.classList.remove(\"image-control-wrapper--resizing\")\n\n      setEndCursor()\n      onResizeEnd(width, height)\n\n      document.removeEventListener(\"pointermove\", handlePointerMove)\n      document.removeEventListener(\"pointerup\", handlePointerUp)\n    }\n  }\n  return (\n    <div ref={controlWrapperRef}>\n      {/* {!showCaption && captionsEnabled && (\n        <button\n          className=\"image-caption-button\"\n          ref={buttonRef}\n          onClick={() => {\n            setShowCaption(!showCaption)\n          }}\n        >\n          Add Caption\n        </button>\n      )} */}\n      <div\n        className=\"image-resizer image-resizer-n\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.north)\n        }}\n      />\n      <div\n        className=\"image-resizer image-resizer-ne\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.north | Direction.east)\n        }}\n      />\n      <div\n        className=\"image-resizer image-resizer-e\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.east)\n        }}\n      />\n      <div\n        className=\"image-resizer image-resizer-se\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.south | Direction.east)\n        }}\n      />\n      <div\n        className=\"image-resizer image-resizer-s\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.south)\n        }}\n      />\n      <div\n        className=\"image-resizer image-resizer-sw\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.south | Direction.west)\n        }}\n      />\n      <div\n        className=\"image-resizer image-resizer-w\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.west)\n        }}\n      />\n      <div\n        className=\"image-resizer image-resizer-nw\"\n        onPointerDown={event => {\n          handlePointerDown(event, Direction.north | Direction.west)\n        }}\n      />\n    </div>\n  )\n}\n", "import \"./ImageNode.css\"\n\nimport { HashtagNode } from \"@lexical/hashtag\"\nimport { LinkNode } from \"@lexical/link\"\nimport { AutoFocusPlugin } from \"@lexical/react/LexicalAutoFocusPlugin\"\nimport { useCollaborationContext } from \"@lexical/react/LexicalCollaborationContext\"\nimport { CollaborationPlugin } from \"@lexical/react/LexicalCollaborationPlugin\"\nimport { useLexicalComposerContext } from \"@lexical/react/LexicalComposerContext\"\nimport { LexicalErrorBoundary } from \"@lexical/react/LexicalErrorBoundary\"\nimport { HashtagPlugin } from \"@lexical/react/LexicalHashtagPlugin\"\nimport { HistoryPlugin } from \"@lexical/react/LexicalHistoryPlugin\"\nimport { LexicalNestedComposer } from \"@lexical/react/LexicalNestedComposer\"\nimport { RichTextPlugin } from \"@lexical/react/LexicalRichTextPlugin\"\nimport { useLexicalEditable } from \"@lexical/react/useLexicalEditable\"\nimport { useLexicalNodeSelection } from \"@lexical/react/useLexicalNodeSelection\"\nimport { mergeRegister } from \"@lexical/utils\"\nimport {\n  $getNodeByKey,\n  $getSelection,\n  $isNodeSelection,\n  $isRangeSelection,\n  $setSelection,\n  CLICK_COMMAND,\n  COMMAND_PRIORITY_LOW,\n  createCommand,\n  DRAGSTART_COMMAND,\n  KEY_BACKSPACE_COMMAND,\n  KEY_DELETE_COMMAND,\n  KEY_ENTER_COMMAND,\n  KEY_ESCAPE_COMMAND,\n  LineBreakNode,\n  ParagraphNode,\n  RootNode,\n  SELECTION_CHANGE_COMMAND,\n  TextNode\n} from \"lexical\"\nimport * as React from \"react\"\nimport { Suspense, useCallback, useEffect, useRef, useState } from \"react\"\n\n\nimport brokenImage from \"../images/image-broken.svg\"\n\nimport ImageResizer from \"../ui/ImageResizer\"\nimport { $isImageNode } from \"./ImageNode\"\n\nconst imageCache = new Set()\n\nexport const RIGHT_CLICK_IMAGE_COMMAND = createCommand(\n  \"RIGHT_CLICK_IMAGE_COMMAND\"\n)\n\nfunction useSuspenseImage(src) {\n  if (!imageCache.has(src)) {\n    throw new Promise(resolve => {\n      const img = new Image()\n      img.src = src\n      img.onload = () => {\n        imageCache.add(src)\n        resolve(null)\n      }\n      img.onerror = () => {\n        imageCache.add(src)\n      }\n    })\n  }\n}\n\nfunction LazyImage({\n  altText,\n  className,\n  imageRef,\n  src,\n  width,\n  height,\n  maxWidth,\n  onError\n}) {\n  useSuspenseImage(src)\n  return (\n    <img\n      className={className || undefined}\n      src={src}\n      alt={altText}\n      ref={imageRef}\n      style={{\n        height,\n        maxWidth,\n        width\n      }}\n      onError={onError}\n      draggable=\"false\"\n      loading=\"lazy\"\n    />\n  )\n}\n\nfunction BrokenImage() {\n  return (\n    <img\n      src={brokenImage}\n      style={{\n        height: 200,\n        opacity: 0.2,\n        width: 200\n      }}\n      draggable=\"false\"\n      loading=\"lazy\"\n    />\n  )\n}\n\nexport default function ImageComponent({\n  src,\n  altText,\n  nodeKey,\n  width,\n  height,\n  maxWidth,\n  resizable,\n  showCaption,\n  caption,\n  captionsEnabled\n}) {\n  const imageRef = useRef(null)\n  const buttonRef = useRef(null)\n  const [isSelected, setSelected, clearSelection] = useLexicalNodeSelection(\n    nodeKey\n  )\n  const [isResizing, setIsResizing] = useState(false)\n  const { isCollabActive } = useCollaborationContext()\n  const [editor] = useLexicalComposerContext()\n  const [selection, setSelection] = useState(null)\n  const activeEditorRef = useRef(null)\n  const [isLoadError, setIsLoadError] = useState(false)\n  const isEditable = useLexicalEditable()\n\n  const $onDelete = useCallback(\n    payload => {\n      const deleteSelection = $getSelection()\n      if (isSelected && $isNodeSelection(deleteSelection)) {\n        const event = payload\n        event.preventDefault()\n        editor.update(() => {\n          deleteSelection.getNodes().forEach(node => {\n            if ($isImageNode(node)) {\n              node.remove()\n            }\n          })\n        })\n      }\n      return false\n    },\n    [editor, isSelected]\n  )\n\n  const $onEnter = useCallback(\n    event => {\n      const latestSelection = $getSelection()\n      const buttonElem = buttonRef.current\n      if (\n        isSelected &&\n        $isNodeSelection(latestSelection) &&\n        latestSelection.getNodes().length === 1\n      ) {\n        if (showCaption) {\n          // Move focus into nested editor\n          $setSelection(null)\n          event.preventDefault()\n          caption.focus()\n          return true\n        } else if (\n          buttonElem !== null &&\n          buttonElem !== document.activeElement\n        ) {\n          event.preventDefault()\n          buttonElem.focus()\n          return true\n        }\n      }\n      return false\n    },\n    [caption, isSelected, showCaption]\n  )\n\n  const $onEscape = useCallback(\n    event => {\n      if (\n        activeEditorRef.current === caption ||\n        buttonRef.current === event.target\n      ) {\n        $setSelection(null)\n        editor.update(() => {\n          setSelected(true)\n          const parentRootElement = editor.getRootElement()\n          if (parentRootElement !== null) {\n            parentRootElement.focus()\n          }\n        })\n        return true\n      }\n      return false\n    },\n    [caption, editor, setSelected]\n  )\n\n  const onClick = useCallback(\n    payload => {\n      const event = payload\n\n      if (isResizing) {\n        return true\n      }\n      if (event.target === imageRef.current) {\n        if (event.shiftKey) {\n          setSelected(!isSelected)\n        } else {\n          clearSelection()\n          setSelected(true)\n        }\n        return true\n      }\n\n      return false\n    },\n    [isResizing, isSelected, setSelected, clearSelection]\n  )\n\n  const onRightClick = useCallback(\n    event => {\n      editor.getEditorState().read(() => {\n        const latestSelection = $getSelection()\n        const domElement = event.target\n        if (\n          domElement.tagName === \"IMG\" &&\n          $isRangeSelection(latestSelection) &&\n          latestSelection.getNodes().length === 1\n        ) {\n          editor.dispatchCommand(RIGHT_CLICK_IMAGE_COMMAND, event)\n        }\n      })\n    },\n    [editor]\n  )\n\n  useEffect(() => {\n    let isMounted = true\n    const rootElement = editor.getRootElement()\n    const unregister = mergeRegister(\n      editor.registerUpdateListener(({ editorState }) => {\n        if (isMounted) {\n          setSelection(editorState.read(() => $getSelection()))\n        }\n      }),\n      editor.registerCommand(\n        SELECTION_CHANGE_COMMAND,\n        (_, activeEditor) => {\n          activeEditorRef.current = activeEditor\n          return false\n        },\n        COMMAND_PRIORITY_LOW\n      ),\n      editor.registerCommand(CLICK_COMMAND, onClick, COMMAND_PRIORITY_LOW),\n      editor.registerCommand(\n        RIGHT_CLICK_IMAGE_COMMAND,\n        onClick,\n        COMMAND_PRIORITY_LOW\n      ),\n      editor.registerCommand(\n        DRAGSTART_COMMAND,\n        event => {\n          if (event.target === imageRef.current) {\n            // TODO This is just a temporary workaround for FF to behave like other browsers.\n            // Ideally, this handles drag & drop too (and all browsers).\n            event.preventDefault()\n            return true\n          }\n          return false\n        },\n        COMMAND_PRIORITY_LOW\n      ),\n      editor.registerCommand(\n        KEY_DELETE_COMMAND,\n        $onDelete,\n        COMMAND_PRIORITY_LOW\n      ),\n      editor.registerCommand(\n        KEY_BACKSPACE_COMMAND,\n        $onDelete,\n        COMMAND_PRIORITY_LOW\n      ),\n      editor.registerCommand(KEY_ENTER_COMMAND, $onEnter, COMMAND_PRIORITY_LOW),\n      editor.registerCommand(\n        KEY_ESCAPE_COMMAND,\n        $onEscape,\n        COMMAND_PRIORITY_LOW\n      )\n    )\n\n    rootElement?.addEventListener(\"contextmenu\", onRightClick)\n\n    return () => {\n      isMounted = false\n      unregister()\n      rootElement?.removeEventListener(\"contextmenu\", onRightClick)\n    }\n  }, [\n    clearSelection,\n    editor,\n    isResizing,\n    isSelected,\n    nodeKey,\n    $onDelete,\n    $onEnter,\n    $onEscape,\n    onClick,\n    onRightClick,\n    setSelected\n  ])\n\n  const setShowCaption = () => {\n    editor.update(() => {\n      const node = $getNodeByKey(nodeKey)\n      if ($isImageNode(node)) {\n        node.setShowCaption(true)\n      }\n    })\n  }\n\n  const onResizeEnd = (nextWidth, nextHeight) => {\n    // Delay hiding the resize bars for click case\n    setTimeout(() => {\n      setIsResizing(false)\n    }, 200)\n\n    editor.update(() => {\n      const node = $getNodeByKey(nodeKey)\n      if ($isImageNode(node)) {\n        node.setWidthAndHeight(nextWidth, nextHeight)\n      }\n    })\n  }\n\n  const onResizeStart = () => {\n    setIsResizing(true)\n  }\n\n\n  console.log(isSelected);\n\n  const draggable = isSelected && $isNodeSelection(selection) && !isResizing\n  const isFocused = (isSelected || isResizing) && isEditable\n  return (\n    <Suspense fallback={null}>\n      <>\n        <div draggable={draggable}>\n          {isLoadError ? (\n            <BrokenImage />\n          ) : (\n            <LazyImage\n              className={\n                isFocused\n                  ? `focused ${$isNodeSelection(selection) ? \"draggable\" : \"\"}`\n                  : null\n              }\n              src={src}\n              altText={altText}\n              imageRef={imageRef}\n              width={width}\n              height={height}\n              maxWidth={maxWidth}\n              onError={() => setIsLoadError(true)}\n            />\n          )}\n          {resizable && $isNodeSelection(selection) && isFocused && (\n            <ImageResizer\n              showCaption={showCaption}\n              setShowCaption={setShowCaption}\n              editor={editor}\n              buttonRef={buttonRef}\n              imageRef={imageRef}\n              maxWidth={maxWidth}\n              onResizeStart={onResizeStart}\n              onResizeEnd={onResizeEnd}\n              captionsEnabled={!isLoadError && captionsEnabled}\n            />\n          )}\n        </div>\n\n\n      </>\n    </Suspense>\n  )\n}\n"], "names": ["o", "b", "Math", "floor", "random", "length", "e", "r", "clientID", "color", "isCollabActive", "name", "yjsDocMap", "Map", "clamp", "value", "min", "max", "Direction", "east", "north", "south", "west", "ImageResizer", "_ref", "onResizeStart", "onResizeEnd", "buttonRef", "imageRef", "max<PERSON><PERSON><PERSON>", "editor", "showCaption", "setShowCaption", "captionsEnabled", "controlWrapperRef", "useRef", "userSelect", "priority", "positioningRef", "currentHeight", "currentWidth", "direction", "isResizing", "ratio", "startHeight", "startWidth", "startX", "startY", "editorRootElement", "getRootElement", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getBoundingClientRect", "width", "maxHeightContainer", "height", "handlePointerDown", "event", "isEditable", "image", "current", "controlWrapper", "preventDefault", "zoom", "calculateZoomLevel", "positioning", "clientX", "clientY", "ew", "ns", "nwse", "cursorDir", "style", "setProperty", "document", "body", "getPropertyValue", "getPropertyPriority", "setStartCursor", "classList", "add", "addEventListener", "handlePointerMove", "handlePointerUp", "isHorizontal", "isVertical", "diff", "remove", "removeEventListener", "_jsxs", "ref", "children", "_jsx", "className", "onPointerDown", "imageCache", "Set", "RIGHT_CLICK_IMAGE_COMMAND", "createCommand", "LazyImage", "altText", "src", "onError", "has", "Promise", "resolve", "img", "Image", "onload", "onerror", "useSuspenseImage", "undefined", "alt", "draggable", "loading", "BrokenImage", "brokenImage", "opacity", "ImageComponent", "_ref2", "nodeKey", "resizable", "caption", "isSelected", "setSelected", "clearSelection", "useLexicalNodeSelection", "setIsResizing", "useState", "g", "useCollaborationContext", "useLexicalComposerContext", "selection", "setSelection", "activeEditorRef", "isLoadError", "setIsLoadError", "useLexicalEditable", "$onDelete", "useCallback", "payload", "deleteSelection", "$getSelection", "$isNodeSelection", "update", "getNodes", "for<PERSON>ach", "node", "$isImageNode", "$onEnter", "latestSelection", "buttonElem", "$setSelection", "focus", "activeElement", "$onEscape", "target", "parentRootElement", "onClick", "shift<PERSON>ey", "onRightClick", "getEditorState", "read", "tagName", "$isRangeSelection", "dispatchCommand", "useEffect", "isMounted", "rootElement", "unregister", "mergeRegister", "registerUpdateListener", "_ref3", "editorState", "registerCommand", "SELECTION_CHANGE_COMMAND", "_", "activeEditor", "COMMAND_PRIORITY_LOW", "CLICK_COMMAND", "DRAGSTART_COMMAND", "KEY_DELETE_COMMAND", "KEY_BACKSPACE_COMMAND", "KEY_ENTER_COMMAND", "KEY_ESCAPE_COMMAND", "console", "log", "isFocused", "Suspense", "fallback", "_Fragment", "$getNodeByKey", "nextWidth", "nextHeight", "setTimeout", "setWidthAndHeight"], "sourceRoot": ""}
"use strict";(self.webpackChunkzoobbe_frontend=self.webpackChunkzoobbe_frontend||[]).push([[840],{5840:(e,t,r)=>{r.r(t),r.d(t,{RIGHT_CLICK_IMAGE_COMMAND:()=>v,default:()=>C});var n=r(5043);const s=[["Cat","rgb(125, 50, 0)"],["<PERSON>","rgb(100, 0, 0)"],["Rabbit","rgb(150, 0, 0)"],["<PERSON>","rgb(200, 0, 0)"],["<PERSON>","rgb(200, 75, 0)"],["Hedgehog","rgb(0, 75, 0)"],["Pigeon","rgb(0, 125, 0)"],["Squirrel","rgb(75, 100, 0)"],["Bear","rgb(125, 100, 0)"],["<PERSON>","rgb(0, 0, 150)"],["<PERSON><PERSON>","rgb(0, 0, 200)"],["Zebra","rgb(0, 0, 250)"],["<PERSON>","rgb(0, 100, 150)"],["Owl","rgb(0, 100, 100)"],["Gull","rgb(100, 0, 100)"],["Squid","rgb(150, 0, 150)"]],i=s[Math.floor(Math.random()*s.length)],o=(0,n.createContext)({clientID:0,color:i[1],isCollabActive:!1,name:i[0],yjsDocMap:new Map});var a=r(5532),l=r(2812),c=r(4736),u=r(8578),d=r(407);const g=r.p+"static/media/image-broken.3b166d0bab97ea92c3c762c75d341727.svg";var h=r(579);function m(e,t,r){return Math.min(Math.max(e,t),r)}const f={east:1,north:8,south:2,west:4};function b(e){let{onResizeStart:t,onResizeEnd:r,buttonRef:s,imageRef:i,maxWidth:o,editor:a,showCaption:l,setShowCaption:c,captionsEnabled:d}=e;const g=(0,n.useRef)(null),b=(0,n.useRef)({priority:"",value:"default"}),p=(0,n.useRef)({currentHeight:0,currentWidth:0,direction:0,isResizing:!1,ratio:0,startHeight:0,startWidth:0,startX:0,startY:0}),w=a.getRootElement(),v=o||(null!==w?w.getBoundingClientRect().width-20:100),y=null!==w?w.getBoundingClientRect().height-20:100,x=(e,r)=>{if(!a.isEditable())return;const n=i.current,s=g.current;if(null!==n&&null!==s){e.preventDefault();const{width:i,height:o}=n.getBoundingClientRect(),a=(0,u.OV)(n),l=p.current;l.startWidth=i,l.startHeight=o,l.ratio=i/o,l.currentWidth=i,l.currentHeight=o,l.startX=e.clientX/a,l.startY=e.clientY/a,l.isResizing=!0,l.direction=r,(e=>{const t=e===f.east||e===f.west,r=e===f.north||e===f.south,n=e&f.north&&e&f.west||e&f.south&&e&f.east,s=t?"ew":r?"ns":n?"nwse":"nesw";null!==w&&w.style.setProperty("cursor",`${s}-resize`,"important"),null!==document.body&&(document.body.style.setProperty("cursor",`${s}-resize`,"important"),b.current.value=document.body.style.getPropertyValue("-webkit-user-select"),b.current.priority=document.body.style.getPropertyPriority("-webkit-user-select"),document.body.style.setProperty("-webkit-user-select","none","important"))})(r),t(),s.classList.add("image-control-wrapper--resizing"),n.style.height=`${o}px`,n.style.width=`${i}px`,document.addEventListener("pointermove",C),document.addEventListener("pointerup",R)}},C=e=>{const t=i.current,r=p.current,n=r.direction&(f.east|f.west),s=r.direction&(f.south|f.north);if(null!==t&&r.isResizing){const i=(0,u.OV)(t);if(n&&s){let n=Math.floor(r.startX-e.clientX/i);n=r.direction&f.east?-n:n;const s=m(r.startWidth+n,100,v),o=s/r.ratio;t.style.width=`${s}px`,t.style.height=`${o}px`,r.currentHeight=o,r.currentWidth=s}else if(s){let n=Math.floor(r.startY-e.clientY/i);n=r.direction&f.south?-n:n;const s=m(r.startHeight+n,100,y);t.style.height=`${s}px`,r.currentHeight=s}else{let n=Math.floor(r.startX-e.clientX/i);n=r.direction&f.east?-n:n;const s=m(r.startWidth+n,100,v);t.style.width=`${s}px`,r.currentWidth=s}}},R=()=>{const e=i.current,t=p.current,n=g.current;if(null!==e&&null!==n&&t.isResizing){const e=t.currentWidth,s=t.currentHeight;t.startWidth=0,t.startHeight=0,t.ratio=0,t.startX=0,t.startY=0,t.currentWidth=0,t.currentHeight=0,t.isResizing=!1,n.classList.remove("image-control-wrapper--resizing"),null!==w&&w.style.setProperty("cursor","text"),null!==document.body&&(document.body.style.setProperty("cursor","default"),document.body.style.setProperty("-webkit-user-select",b.current.value,b.current.priority)),r(e,s),document.removeEventListener("pointermove",C),document.removeEventListener("pointerup",R)}};return(0,h.jsxs)("div",{ref:g,children:[(0,h.jsx)("div",{className:"image-resizer image-resizer-n",onPointerDown:e=>{x(e,f.north)}}),(0,h.jsx)("div",{className:"image-resizer image-resizer-ne",onPointerDown:e=>{x(e,f.north|f.east)}}),(0,h.jsx)("div",{className:"image-resizer image-resizer-e",onPointerDown:e=>{x(e,f.east)}}),(0,h.jsx)("div",{className:"image-resizer image-resizer-se",onPointerDown:e=>{x(e,f.south|f.east)}}),(0,h.jsx)("div",{className:"image-resizer image-resizer-s",onPointerDown:e=>{x(e,f.south)}}),(0,h.jsx)("div",{className:"image-resizer image-resizer-sw",onPointerDown:e=>{x(e,f.south|f.west)}}),(0,h.jsx)("div",{className:"image-resizer image-resizer-w",onPointerDown:e=>{x(e,f.west)}}),(0,h.jsx)("div",{className:"image-resizer image-resizer-nw",onPointerDown:e=>{x(e,f.north|f.west)}})]})}var p=r(3212);const w=new Set,v=(0,d.gu)("RIGHT_CLICK_IMAGE_COMMAND");function y(e){let{altText:t,className:r,imageRef:n,src:s,width:i,height:o,maxWidth:a,onError:l}=e;return function(e){if(!w.has(e))throw new Promise((t=>{const r=new Image;r.src=e,r.onload=()=>{w.add(e),t(null)},r.onerror=()=>{w.add(e)}}))}(s),(0,h.jsx)("img",{className:r||void 0,src:s,alt:t,ref:n,style:{height:o,maxWidth:a,width:i},onError:l,draggable:"false",loading:"lazy"})}function x(){return(0,h.jsx)("img",{src:g,style:{height:200,opacity:.2,width:200},draggable:"false",loading:"lazy"})}function C(e){let{src:t,altText:r,nodeKey:s,width:i,height:g,maxWidth:m,resizable:f,showCaption:w,caption:C,captionsEnabled:R}=e;const z=(0,n.useRef)(null),E=(0,n.useRef)(null),[D,P,W]=(0,c.Y)(s),[j,M]=(0,n.useState)(!1),{isCollabActive:N}=function(e,t){const r=(0,n.useContext)(o);return null!=e&&(r.name=e),null!=t&&(r.color=t),r}(),[k]=(0,a.DF)(),[A,S]=(0,n.useState)(null),H=(0,n.useRef)(null),[T,L]=(0,n.useState)(!1),I=(0,l.a)(),$=(0,n.useCallback)((e=>{const t=(0,d.vJ)();if(D&&(0,d.RT)(t)){e.preventDefault(),k.update((()=>{t.getNodes().forEach((e=>{(0,p.Eh)(e)&&e.remove()}))}))}return!1}),[k,D]),X=(0,n.useCallback)((e=>{const t=(0,d.vJ)(),r=E.current;if(D&&(0,d.RT)(t)&&1===t.getNodes().length){if(w)return(0,d.n1)(null),e.preventDefault(),C.focus(),!0;if(null!==r&&r!==document.activeElement)return e.preventDefault(),r.focus(),!0}return!1}),[C,D,w]),_=(0,n.useCallback)((e=>(H.current===C||E.current===e.target)&&((0,d.n1)(null),k.update((()=>{P(!0);const e=k.getRootElement();null!==e&&e.focus()})),!0)),[C,k,P]),Y=(0,n.useCallback)((e=>{const t=e;return!!j||t.target===z.current&&(t.shiftKey?P(!D):(W(),P(!0)),!0)}),[j,D,P,W]),G=(0,n.useCallback)((e=>{k.getEditorState().read((()=>{const t=(0,d.vJ)();"IMG"===e.target.tagName&&(0,d.I2)(t)&&1===t.getNodes().length&&k.dispatchCommand(v,e)}))}),[k]);(0,n.useEffect)((()=>{let e=!0;const t=k.getRootElement(),r=(0,u.Sd)(k.registerUpdateListener((t=>{let{editorState:r}=t;e&&S(r.read((()=>(0,d.vJ)())))})),k.registerCommand(d.Mv,((e,t)=>(H.current=t,!1)),d.Ac),k.registerCommand(d.d8,Y,d.Ac),k.registerCommand(v,Y,d.Ac),k.registerCommand(d.Tg,(e=>e.target===z.current&&(e.preventDefault(),!0)),d.Ac),k.registerCommand(d.w$,$,d.Ac),k.registerCommand(d.gC,$,d.Ac),k.registerCommand(d.if,X,d.Ac),k.registerCommand(d.Q$,_,d.Ac));return null===t||void 0===t||t.addEventListener("contextmenu",G),()=>{e=!1,r(),null===t||void 0===t||t.removeEventListener("contextmenu",G)}}),[W,k,j,D,s,$,X,_,Y,G,P]);console.log(D);const O=D&&(0,d.RT)(A)&&!j,B=(D||j)&&I;return(0,h.jsx)(n.Suspense,{fallback:null,children:(0,h.jsx)(h.Fragment,{children:(0,h.jsxs)("div",{draggable:O,children:[T?(0,h.jsx)(x,{}):(0,h.jsx)(y,{className:B?"focused "+((0,d.RT)(A)?"draggable":""):null,src:t,altText:r,imageRef:z,width:i,height:g,maxWidth:m,onError:()=>L(!0)}),f&&(0,d.RT)(A)&&B&&(0,h.jsx)(b,{showCaption:w,setShowCaption:()=>{k.update((()=>{const e=(0,d.ns)(s);(0,p.Eh)(e)&&e.setShowCaption(!0)}))},editor:k,buttonRef:E,imageRef:z,maxWidth:m,onResizeStart:()=>{M(!0)},onResizeEnd:(e,t)=>{setTimeout((()=>{M(!1)}),200),k.update((()=>{const r=(0,d.ns)(s);(0,p.Eh)(r)&&r.setWidthAndHeight(e,t)}))},captionsEnabled:!T&&R})]})})})}}}]);
//# sourceMappingURL=840.9b43ea17.chunk.js.map
importScripts("https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js");

// Firebase Configuration
const firebaseConfig = {
    apiKey: "AIzaSyB3yg_3PfZLgOFbXJh_U7BV1jukE95R-0",
    authDomain: "zoobbe-3c098.firebaseapp.com",
    projectId: "zoobbe-3c098",
    storageBucket: "zoobbe-3c098.firebasestorage.app",
    messagingSenderId: "635946153936",
    appId: "1:635946153936:web:4b159228c5616be819ebd5",
    measurementId: "G-21224880KZ"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Handle Background Messages
messaging.onBackgroundMessage((payload) => {
    console.log("Received background notification:", payload);

    const title = payload.notification?.title || "Zoobbe Notification";
    const body = payload.notification?.body || "You have a new message!";
    const icon = payload.notification?.icon || "/default-icon.png";
    const badge = "/workspace.png"; // Small icon on mobile

    // Extract URL from data payload (Firebase sometimes places it under different keys)
    const url = payload.data?.url || payload.notification?.click_action || "https://zoohbbe.com";

    console.log("Opening URL on click:", url);

    const notificationOptions = {
        body,
        icon,
        badge,
        data: { url }, // Attach URL so it can be used in notification click event
    };

    self.registration.showNotification(title, notificationOptions);
});

// Handle Click Events
self.addEventListener("notificationclick", (event) => {
    event.notification.close();

    const targetUrl = event.notification.data?.url || "https://zoobbe.com";

    console.log("Redirecting to:", targetUrl);

    event.waitUntil(
        clients.matchAll({ type: "window", includeUncontrolled: true }).then((windowClients) => {
            // Check if the app is already open
            for (let client of windowClients) {
                if (client.url.includes(targetUrl) && "focus" in client) {
                    return client.focus();
                }
            }
            // Otherwise, open a new tab
            return clients.openWindow(targetUrl);
        })
    );
});

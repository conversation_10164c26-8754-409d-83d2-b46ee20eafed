import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuth from '../hooks/useAuth';
import Spinner from '../components/Global/Spinner';

/**
 * Higher-order component for optimized authentication
 * Replaces the existing withAuthRedirect HOC with a more efficient implementation
 * 
 * @param {React.Component} WrappedComponent - Component to wrap with authentication
 * @param {Object} options - Configuration options
 * @param {boolean} options.requireAuth - Whether authentication is required (default: true)
 * @param {string} options.redirectPath - Path to redirect to if not authenticated (default: '/login')
 * @returns {React.Component} - Wrapped component with authentication
 */
const withOptimizedAuth = (WrappedComponent, options = {}) => {
  const {
    requireAuth = true,
    redirectPath = '/login'
  } = options;

  return (props) => {
    const { isAuthenticated, isLoading, checkAuthStatus } = useAuth();
    const [authChecked, setAuthChecked] = useState(false);
    const navigate = useNavigate();

    useEffect(() => {
      const verifyAuth = async () => {
        const isAuth = await checkAuthStatus();
        
        if (requireAuth && !isAuth) {
          // Redirect if authentication is required but user is not authenticated
          navigate(redirectPath);
        } else if (!requireAuth && isAuth) {
          // Redirect to workspaces if already authenticated and on a non-auth page
          navigate('/workspaces');
        }
        
        setAuthChecked(true);
      };

      verifyAuth();
    }, [checkAuthStatus, navigate]);

    // Show loading spinner while checking authentication
    if (isLoading || !authChecked) {
      return (
        <div className="auth-loading-container">
          <Spinner size={40} color="#3498db" />
        </div>
      );
    }

    // If authentication is required and user is not authenticated, don't render component
    // (redirect should have happened in useEffect)
    if (requireAuth && !isAuthenticated) {
      return null;
    }

    // If we're on a non-auth page and user is authenticated, don't render component
    // (redirect should have happened in useEffect)
    if (!requireAuth && isAuthenticated) {
      return null;
    }

    // Otherwise, render the wrapped component
    return <WrappedComponent {...props} />;
  };
};

export default withOptimizedAuth;

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { config } from '../config';

const withAuthRedirect = (WrappedComponent) => {
    return (props) => {
        const [isLoading, setIsLoading] = useState(true);
        const navigate = useNavigate();

        useEffect(() => {
            const token = localStorage.getItem('accessToken');
            if (token) {
                fetch(config.API_URI + '/api/users/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include',
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.valid) {
                            navigate('/workspaces');
                        } else {
                            setIsLoading(false);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        setIsLoading(false);
                    });
            } else {
                setIsLoading(false);
            }
        }, [navigate]);

        if (isLoading) {
            return <div>Loading...</div>; // You can show a loading spinner here
        }

        return <WrappedComponent {...props} />;
    };
};

export default withAuthRedirect;

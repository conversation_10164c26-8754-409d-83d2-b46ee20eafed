import { useNavigate } from 'react-router-dom';
import './scss/PaymentSuccess.scss';

const PaymentSuccess = () => {
    const navigate = useNavigate();

    return (
        <div className="payment-success">
            <div className="success-card">
                <div className="success-emoji">
                    <span>🎉</span>
                </div>
                
                <h1>Payment Successful!</h1>
                
                <div className="success-content">
                    <p className="message">
                        Thank you! Your Zoobbe Pro account is now active.
                    </p>
                    
                    <div className="info-box">
                        <p>You will receive a confirmation email shortly with your receipt.</p>
                    </div>

                    <button 
                        onClick={() => navigate('/')}
                        className="dashboard-btn"
                    >
                        Go to Dashboard
                    </button>
                </div>
            </div>
        </div>
    );
}

export default PaymentSuccess;
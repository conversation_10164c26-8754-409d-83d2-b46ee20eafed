.payment-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    padding: 0 1rem;

    .success-card {
        max-width: 28rem;
        width: 100%;
        padding: 2rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

        .success-emoji {
            margin-bottom: 1.5rem;
            text-align: center;

            span {
                font-size: 3rem;
                display: inline-block;
                animation: bounce 1s infinite;
            }
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--primary-text-color);
            margin: 0;
        }

        .success-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .message {
                color: #4b5563;
                font-size: 1.125rem;
                line-height: 1.5;
            }

            .info-box {
                border: 1px solid var(--outline-color);
                border-radius: 0.375rem;
                padding: 1rem;

                p {
                    color: #15803d;
                    font-size: 0.875rem;
                    margin: 0;
                    padding: 0;
                }
            }

            .dashboard-btn {
                width: 100%;
                background-color: var(--brand-color);
                color: var(--brand-btn-primary-color);
                font-weight: 500;
                padding: 0.75rem 1rem;
                border-radius: 0.375rem;
                border: none;
                cursor: pointer;
                transition: background-color 0.2s ease;

                &:hover {
                    background-color: var(--hover-brand-color);
                }

                &:focus {
                    outline: 2px solid var(--brand-color);
                    outline-offset: 2px;
                }
            }
        }
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-20px);
    }
}

@media (max-width: 640px) {
    .payment-success {
        .success-card {
            padding: 1.5rem;

            h1 {
                font-size: 1.5rem;
            }

            .success-content {
                .message {
                    font-size: 1rem;
                }
            }
        }
    }
}
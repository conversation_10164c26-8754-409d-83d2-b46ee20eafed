// CheckoutForm.scss
.checkout-container {
    display: flex;
    max-width: 1100px;
    margin: 40px auto;
    font-size: 14px;
    color: var(--white-text-color-alternative);
    line-height: 1.6em;
}

.board-header {
    display: flex;
    align-items: center;
    /* background-color: var(--single-card-side-action-bg-color); */
    padding: 10px;
    /* border-radius: 8px; */
    color: var(--white-text-color-alternative);
    max-width: 1100px;
    padding: 20px 20px;
    margin: auto;
    border-bottom: 1px solid var(--outline-color);
    padding-top: 60px;

    .board-icon {
        background-color: #f05a28;
        border-radius: var(--element-border-radius);
        width: 48px;
        height: 48px;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon-text {
            color: var(--white-text-color-alternative);
            font-size: 24px;
            font-weight: bold;
        }
    }

    .board-details {
        margin-left: 10px;

        .board-name {
            display: flex;
            align-items: center;

            span {
                font-size: 20px;
                font-weight: 600;
            }

            span.material-symbols-outlined {
                margin-left: 5px;
                cursor: pointer;
                color: var(--single-card-text-color);
                transform: rotate(-15deg);
            }
        }

        .board-visibility {
            display: flex;
            align-items: center;
            margin-top: 5px;

            span.material-symbols-outlined {
                font-size: 14px !important;
                margin-right: 4px;
            }

            span {
                font-size: 14px;
                color: var(--single-card-text-color);
            }
        }
    }
}

.checkout-left {
    width: 280px;
    padding: 0 24px;

    h2 {
        font-size: 18px;
        font-weight: bold;
        margin-top: 0;
        line-height: 1.4;
    }

    .feature-intro {
        margin-top: 16px;
        font-weight: 500;
    }

    .features-list {
        list-style: none;
        margin: 16px 0;
        padding: 0;

        li {
            position: relative;
            display: flex;
            font-size: 0.875rem;
            margin-bottom: 8px;
            color: var(--white-text-color-alternative);
            line-height: 1.6;

            .check {
                margin-right: 0.5rem;
                display: inline-flex;
                align-items: center;
                height: 16px;
                width: 16px;
                border-radius: 50px;
                justify-content: center;
                margin-top: 3px;
                svg {
                    height: 14px;
                    width: 14px;
                    fill: var(--brand-color);
                }
            }
        }
    }

    .compare-link {
        display: block;
        margin: 16px 0;
        color: #0052cc;
        font-weight: 500;
        text-decoration: none;
    }

    .support-links {
        margin-top: 24px;

        a {
            display: block;
            color: #6b778c;
            font-size: 12px;
            margin-bottom: 4px;
        }
    }
}

.checkout-right {
    width: calc(100% - 280px);
    padding: 0 24px;
    border-right: 1px solid var(--outline-color);

    h3 {
        font-size: 16px;
        margin-bottom: 16px;
        padding: 0;
        margin-top: 0;
    }

    .plan-options {
        display: flex;
        gap: 12px;

        .plan {
            flex: 1;
            outline: 1px solid var(--outline-color);
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;

            &.selected {
                outline: 2px solid var(--brand-color);
                background-color: var(--select-option-hover-background);
            }

            .plan-name {
                font-weight: bold;
                margin-bottom: 4px;
            }

            .plan-price {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 5px;

                span {
                    font-size: 12px;
                    margin-left: 4px;
                }
            }

            .plan-note {
                font-size: 12px;
                line-height: 1.6;
            }
        }
    }

    .step-label {
        margin: 24px 0 8px;
        font-weight: bold;
        font-size: 12px;
        color: #6b778c;
    }

    .payment-form {
        label {
            display: flex;
            margin-bottom: 16px;
            flex-direction: column;
            gap: 5px;
            font-weight: 500;

            input,
            .stripe-input {
                margin-top: 4px;
                width: 100%;
                padding: 0 12px;
                outline: 1px solid var(--input-border-color);
                border-radius: 2px;
                height: 35px;
                border: none;
                font-size: 14px;
                font-weight: 500;
                font-family: system-ui;
                background-color: transparent;
                color: var(--white-text-color-alternative);
                width: calc(100% - 20px);

                .StripeElement{
                    margin-top: 10px;
                }

                &:focus {
                    outline: 2px solid var(--brand-color);
                }
            }
            :not(.search-select).focused {
                outline: 2px solid var(--brand-color);
            }


            .search-select-trigger {
                margin-top: 4px;
                background-color: transparent;

                input {
                    outline: none;
                    margin: 0;
                }
            }
        }

        .input-row {
            display: flex;
            gap: 12px;

            label {
                flex: 1;
            }
        }
    }

    .seat-configuration {
        margin: 24px 0;
        padding: 20px;
        background: var(--select-option-hover-background);
        border-radius: 8px;
        border: 1px solid var(--input-border-color);

        .seat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            label {
                flex: 1;
                margin-bottom: 0;
                font-weight: 500;
                color: var(--white-text-color-alternative);

                .seat-input {
                    width: 80px;
                    margin-top: 8px;
                    padding: 8px 12px;
                    border: 1px solid var(--input-border-color);
                    border-radius: 4px;
                    background: transparent;
                    color: var(--white-text-color-alternative);
                    font-size: 14px;

                    &:focus {
                        outline: 2px solid var(--brand-color);
                        border-color: var(--brand-color);
                    }
                }
            }

            .seat-count {
                font-weight: 600;
                color: var(--brand-color);
                font-size: 16px;

                &.paid {
                    color: #10b981; // Green for paid seats
                }

                &.unpaid {
                    color: #f59e0b; // Orange for unpaid seats
                }
            }

            .seat-note {
                font-size: 12px;
                color: #6b778c;
                margin-top: 4px;
                font-style: italic;
            }
        }
    }

    .billing-summary {
        margin-top: 24px;

        .summary-item,
        .summary-total {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .summary-note {
            margin-bottom: 8px;
            padding: 8px 12px;
            background: var(--select-option-hover-background);
            border-radius: 4px;
            border-left: 3px solid #10b981;

            span {
                font-size: 13px;
                color: #065f46;
                font-style: italic;
            }
        }

        .summary-total {
            font-weight: bold;
        }

        .billing-toggle {
            display: flex;
            align-items: center;
            margin-top: 16px;

            .toggle {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-left: 8px;

                .option {
                    font-size: 12px;
                    color: #6b778c;
                }

                .option.active {
                    color: var(--brand-color);
                    font-weight: bold;
                }

                .switch {
                    width: 36px;
                    height: 17px;
                    border: 1px solid var(--brand-color);
                    border-radius: 999px;
                    position: relative;
                    transition: all 0.3s;
                    cursor: pointer;

                    &.on::after {
                        content: "";
                        position: absolute;
                        top: 2.3px;
                        left: 20px;
                        width: 14px;
                        height: 14px;
                        background: var(--brand-color);
                        border-radius: 50%;
                        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
                        transition: all 0.3s;
                        top: 50%;
                        transform: translate(0px, -50%);
                    }
                    &.off::after {
                        content: "";
                        position: absolute;
                        top: 2.3px;
                        right: 20px;
                        width: 14px;
                        height: 14px;
                        background: var(--brand-color);
                        border-radius: 50%;
                        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
                        transition: all 0.3s;
                        top: 50%;
                        transform: translate(0px, -50%);
                    }
                }
            }
        }
    }

    .pending-invite {
        background: var(--select-option-hover-background);
        padding: 12px;
        border-radius: 4px;
        margin-top: 24px;
        font-size: 13px;

        p {
            color: var(--white-text-color-alternative);
            line-height: 1.6;
            margin: 0;
        }
    }

    .confirm-btn {
        display: block;
        background: var(--brand-color);
        color: white;
        font-weight: 500;
        padding: 10px 20px;
        border: none;
        margin-top: 16px;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
            background: var(--hover-brand-color);
        }
    }
}

.checkout-loading{
    color: var(--primary-text-color);
}
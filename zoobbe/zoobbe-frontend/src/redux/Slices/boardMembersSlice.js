// src/redux/Slices/membersSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { config } from '../../config';

export const fetchBoardMembers = createAsyncThunk(
    'members/fetchBoardMembers',
    async (boardId, { rejectWithValue }) => {
        try {
            const token = localStorage.getItem('accessToken');
            const response = await fetch(config.API_URI + `/api/boards/${boardId}/members`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                credentials: 'include',
            });
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            const data = await response.json();
            return data;
        } catch (error) {
            return rejectWithValue(error.message);
        }
    }
);

const boardMembersSlice = createSlice({
    name: 'members',
    initialState: {
        members: [],
        loading: false,
        error: null
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchBoardMembers.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchBoardMembers.fulfilled, (state, action) => {
                state.members = action.payload;
                state.loading = false;
            })
            .addCase(fetchBoardMembers.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    }
});

export default boardMembersSlice.reducer;

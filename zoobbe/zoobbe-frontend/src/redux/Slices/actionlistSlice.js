import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { config } from '../../config';

// Async thunk to fetch an action list by ID
export const fetchActionListById = createAsyncThunk(
    'actionList/fetchActionListById',
    async (actionListId, { getState }) => {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`${config.API_URI}/api/actionLists/${actionListId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            credentials: 'include',
        });

        if (!response.ok) {
            throw new Error('Failed to fetch action list');
        }

        const data = await response.json();
        return { actionListId, actionList: data.actionList };
    }
);

// Async thunk to fetch cards by action list ID with pagination
export const fetchCardsByListId = createAsyncThunk(
    'actionList/fetchCardsByListId',
    async ({ actionListId, skip = 0, limit = 0 }, { getState }) => {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`${config.API_URI}/api/actionLists/${actionListId}/cards?skip=${skip}&limit=${limit}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            credentials: 'include',
        });

        if (!response.ok) {
            throw new Error('Failed to fetch cards');
        }

        const data = await response.json();
        return {
            actionListId,
            cards: data.cards,
            hasMore: data.hasMore,
            totalCards: data.totalCards,
            isInitialLoad: skip === 0
        };
    }
);

const actionListSlice = createSlice({
    name: 'actionList',
    initialState: {
        actionLists: {}, // Stores action lists by ID
        status: 'idle',
        error: null,
    },
    reducers: {
        // Add a reducer to update totalCards when moving cards between lists
        updateTotalCardsOnMove: (state, action) => {
            const { sourceListId, targetListId } = action.payload;

            // Decrease count for source list
            if (state.actionLists[sourceListId] && state.actionLists[sourceListId].totalCards > 0) {
                state.actionLists[sourceListId].totalCards -= 1;
            }

            // Increase count for target list
            if (state.actionLists[targetListId]) {
                state.actionLists[targetListId].totalCards =
                    (state.actionLists[targetListId].totalCards || 0) + 1;
            }
        }
    },
    extraReducers: (builder) => {
        builder
            // Fetch action list by ID
            .addCase(fetchActionListById.pending, (state, action) => {
                const actionListId = action.meta.arg;
                state.actionLists[actionListId] = state.actionLists[actionListId] || { details: null, cards: [], status: 'idle', error: null };
                state.actionLists[actionListId].status = 'loading';
            })
            .addCase(fetchActionListById.fulfilled, (state, action) => {
                const { actionListId, actionList } = action.payload;
                state.actionLists[actionListId] = state.actionLists[actionListId] || { details: null, cards: [], status: 'idle', error: null };
                state.actionLists[actionListId].details = actionList;
                state.actionLists[actionListId].status = 'succeeded';
            })
            .addCase(fetchActionListById.rejected, (state, action) => {
                const actionListId = action.meta.arg;
                state.actionLists[actionListId] = state.actionLists[actionListId] || { details: null, cards: [], status: 'idle', error: null };
                state.actionLists[actionListId].status = 'failed';
                state.actionLists[actionListId].error = action.error.message;
            })

            // Fetch cards by action list ID
            .addCase(fetchCardsByListId.pending, (state, action) => {
                const { actionListId } = action.meta.arg;
                state.actionLists[actionListId] = state.actionLists[actionListId] || {
                    details: null,
                    cards: [],
                    status: 'idle',
                    error: null,
                    hasMore: true,
                    totalCards: 0
                };
                state.actionLists[actionListId].status = 'loading';
            })
            .addCase(fetchCardsByListId.fulfilled, (state, action) => {
                const { actionListId, cards, hasMore, totalCards, isInitialLoad } = action.payload;
                state.actionLists[actionListId] = state.actionLists[actionListId] || {
                    details: null,
                    cards: [],
                    status: 'idle',
                    error: null,
                    hasMore: true,
                    totalCards: 0
                };

                // If this is the initial load, replace the cards array
                // Otherwise, append the new cards to the existing array
                if (isInitialLoad) {
                    state.actionLists[actionListId].cards = cards;
                } else {
                    state.actionLists[actionListId].cards = [
                        ...state.actionLists[actionListId].cards,
                        ...cards
                    ];
                }

                state.actionLists[actionListId].hasMore = hasMore;
                state.actionLists[actionListId].totalCards = totalCards;
                state.actionLists[actionListId].status = 'succeeded';
            })
            .addCase(fetchCardsByListId.rejected, (state, action) => {
                const { actionListId } = action.meta.arg;
                state.actionLists[actionListId] = state.actionLists[actionListId] || {
                    details: null,
                    cards: [],
                    status: 'idle',
                    error: null,
                    hasMore: true,
                    totalCards: 0
                };
                state.actionLists[actionListId].status = 'failed';
                state.actionLists[actionListId].error = action.error.message;
            });
    },
});

// Export the action creator
export const { updateTotalCardsOnMove } = actionListSlice.actions;

export default actionListSlice.reducer;

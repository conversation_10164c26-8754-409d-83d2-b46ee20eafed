import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { config } from '../../config';
import { api } from '../../utils/apiUtils';

// Async thunk to fetch boards by workspace
export const fetchBoards = createAsyncThunk(
    'boards/fetchBoards',
    async ({ workspaceId, useCache = true } = {}, { rejectWithValue }) => {
        try {
            // Build URL with workspaceId if provided
            const url = workspaceId
                ? `${config.API_URI}/api/boards?workspaceId=${workspaceId}`
                : `${config.API_URI}/api/boards`;

            // Use the enhanced API utility with caching
            return await api.get(
                url,
                {},
                useCache,
                5 * 60 * 1000 // Cache for 5 minutes
            );
        } catch (error) {
            console.error('Error fetching boards:', error);
            return rejectWithValue(error.message);
        }
    }
);

export const fetchBoardLists = createAsyncThunk(
    'boards/fetchBoardLists',
    async (params, { rejectWithValue }) => {
        try {
            // Handle both string boardId and object with options
            const boardId = typeof params === 'string' ? params : params.boardId;
            const includeCards = typeof params === 'object' && params.includeCards !== undefined
                ? params.includeCards
                : false; // Default to false for better performance
            const useCache = typeof params === 'object' && params.useCache !== undefined
                ? params.useCache
                : true; // Default to using cache

            const url = `${config.API_URI}/api/boards/${boardId}/lists?includeCards=${includeCards}`;

            // Use the enhanced API utility with caching
            const actionLists = await api.get(
                url,
                {},
                useCache,
                3 * 60 * 1000 // Cache for 3 minutes
            );

            return { boardId, actionLists };
        } catch (error) {
            console.error('Error fetching board lists:', error);
            return rejectWithValue(error.message);
        }
    }
);

// Async thunk to fetch starred boards
export const fetchStarredBoards = createAsyncThunk(
    'boards/fetchStarredBoards',
    async ({ useCache = true } = {}, { rejectWithValue }) => {
        try {
            // Use the enhanced API utility with caching
            return await api.get(
                `${config.API_URI}/api/users/me/get-starred`,
                {},
                useCache,
                5 * 60 * 1000 // Cache for 5 minutes
            );
        } catch (error) {
            return rejectWithValue(error.message || 'An error occurred while fetching starred boards');
        }
    }
);

export const fetchRecentBoards = createAsyncThunk(
    'boards/fetchRecentBoards',
    async ({ limit = 5, useCache = true } = {}, { rejectWithValue }) => {
        try {
            // Use the enhanced API utility with caching
            return await api.get(
                `${config.API_URI}/api/users/me/recent-viewed?limit=${limit}`,
                {},
                useCache,
                2 * 60 * 1000 // Cache for 2 minutes
            );
        } catch (error) {
            return rejectWithValue(error.message || 'An error occurred while fetching recent boards');
        }
    }
);

export const fetchBoardsByWorkspaceAndMember = createAsyncThunk(
    'boards/fetchBoardsByWorkspaceAndMember',
    async ({ workspaceId, memberId, useCache = true }, { rejectWithValue }) => {
        try {
            // Use the enhanced API utility with caching
            return await api.get(
                `${config.API_URI}/api/members/${workspaceId}/${memberId}/boards`,
                {},
                useCache,
                5 * 60 * 1000 // Cache for 5 minutes
            );
        } catch (error) {
            return rejectWithValue(error.message || 'Failed to fetch boards for the member in the workspace');
        }
    }
);

const boardsSlice = createSlice({
    name: 'boards',
    initialState: {
        boards: [],
        memberBoards: [],
        starredBoards: [],
        recentBoards: [],
        actionLists: {},
        status: 'idle',
        starredStatus: 'idle',
        recentStatus: 'idle',
        error: null,
    },
    reducers: {
        // Add a reducer to clear cache when needed
        clearBoardsCache: (state) => {
            // This is just to trigger a re-fetch, the actual cache clearing happens in the component
            state.status = 'idle';
        }
    },
    extraReducers: (builder) => {
        builder
            // Fetch all boards
            .addCase(fetchBoards.pending, (state) => {
                state.status = 'loading';
            })
            .addCase(fetchBoards.fulfilled, (state, action) => {
                state.status = 'succeeded';
                // The API now returns an array of boards directly
                state.boards = action.payload;
            })
            .addCase(fetchBoards.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
            })
            // Fetch boards by workspace and member
            .addCase(fetchBoardsByWorkspaceAndMember.pending, (state) => {
                state.status = 'loading';
            })
            .addCase(fetchBoardsByWorkspaceAndMember.fulfilled, (state, action) => {
                state.status = 'succeeded';
                // Handle both old and new API response formats
                state.memberBoards = action.payload.boards || action.payload;
            })
            .addCase(fetchBoardsByWorkspaceAndMember.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
            })
            // Fetch board lists
            .addCase(fetchBoardLists.pending, () => {
                // No need to set global loading state for lists
            })
            .addCase(fetchBoardLists.fulfilled, (state, action) => {
                state.actionLists[action.payload.boardId] = action.payload.actionLists;
            })
            .addCase(fetchBoardLists.rejected, (state, action) => {
                state.error = action.payload;
            })
            // Fetch starred boards
            .addCase(fetchStarredBoards.pending, (state) => {
                state.starredStatus = 'loading';
            })
            .addCase(fetchStarredBoards.fulfilled, (state, action) => {
                state.starredStatus = 'succeeded';
                state.starredBoards = action.payload;
            })
            .addCase(fetchStarredBoards.rejected, (state, action) => {
                state.starredStatus = 'failed';
                state.error = action.payload;
            })
            // Fetch recent view boards
            .addCase(fetchRecentBoards.pending, (state) => {
                state.recentStatus = 'loading';
            })
            .addCase(fetchRecentBoards.fulfilled, (state, action) => {
                state.recentStatus = 'succeeded';
                state.recentBoards = action.payload;
            })
            .addCase(fetchRecentBoards.rejected, (state, action) => {
                state.recentStatus = 'failed';
                state.error = action.payload;
            });
    },
});

export const { clearBoardsCache } = boardsSlice.actions;

export default boardsSlice.reducer;
import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const SkeltonWorkspace = () => {
    return (
        <div style={{ padding: '20px', color: '#FFF', backgroundColor: '#1E1E1E' }}>
            <h3><Skeleton width={150} /></h3>
            <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
                {Array(3).fill().map((_, index) => (
                    <div key={index} style={{ width: '200px', padding: '10px', background: '#333', borderRadius: '5px' }}>
                        <Skeleton height={20} width="80%" style={{ marginBottom: '10px' }} />
                        <Skeleton circle height={40} width={40} style={{ marginBottom: '10px' }} />
                        <Skeleton height={20} width="60%" />
                    </div>
                ))}
            </div>

            <h3><Skeleton width={150} /></h3>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
                {Array(8).fill().map((_, index) => (
                    <div key={index} style={{ width: '150px', padding: '10px', background: '#333', borderRadius: '5px' }}>
                        <Skeleton height={20} width="80%" style={{ marginBottom: '10px' }} />
                        <Skeleton circle height={40} width={40} style={{ marginBottom: '10px' }} />
                        <Skeleton height={20} width="60%" />
                    </div>
                ))}
            </div>
        </div>
    );
};

export default SkeltonWorkspace;

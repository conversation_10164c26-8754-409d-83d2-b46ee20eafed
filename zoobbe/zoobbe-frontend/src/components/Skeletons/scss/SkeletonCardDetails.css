.zoobbe-card-details .react-loading-skeleton {
  --base-color: var(--single-card-side-action-bg-color) !important;
  --highlight-color: #17171a36;
}

.zoobbe-card-wrapper.skeleton .card-heading {
  margin-bottom: 10px;
}
.zoobbe-card-wrapper.skeleton .card-members {
  display: flex;
  gap: 5px;
}
.zoobbe-card-wrapper.skeleton .watchers {
  display: flex;
  gap: 8px;
}
.zoobbe-card-wrapper.skeleton .zoobbe-attachment {
  display: flex;
  align-items: center;
  gap: 20px;
}
.zoobbe-card-wrapper.skeleton .zoobbe-duedate {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.zoobbe-card-wrapper.skeleton .zoobbe-card-details-info-right span.react-loading-skeleton {
  margin-bottom: 8px;
}
.zoobbe-card-wrapper.skeleton .zoobbe-card-content {
  display: flex;
  gap: 10px;
}/*# sourceMappingURL=SkeletonCardDetails.css.map */
.react-loading-skeleton {
    --base-color: var(--skelton-base-color);
    --highlight-color: var(--skelton-highlight-color);
}

.zoobbe-workspace-board-container.loading {
    justify-content: center;
    display: flex;
    height: 100vh;
}

.zoobbe-board-skeleton {
    gap: 20px;
    justify-content: center;
}

.skeleton-inner {
    background: var(--skeleton-inner-background-color);
    // background: rgba(30, 30, 30, 0.5490196078);
    padding: 10px;
    border-radius: 15px;
}

.zoobbe-board-skeleton {
    display: grid;
    gap: 10px;
    grid-auto-flow: column;
    grid-auto-columns: 290px;
    grid-template-rows: auto;
    height: 100vh;
}

.add-list-skeleton {
    display: flex;
    gap: 10px;
}
.header-wrapper {
    background: var(--skeleton-header-wrapper-background-color);
}
.skeleton-board-header {
    display: flex;
    justify-content: space-between;
    max-width: calc(100% - 100px);
    margin: auto;
    height: 60px;
    margin-bottom: 20px;

    .board-header-left {
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--skeleton-board-header-color);
    }
    .board-header-right {
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--skeleton-board-header-color);
    }
}

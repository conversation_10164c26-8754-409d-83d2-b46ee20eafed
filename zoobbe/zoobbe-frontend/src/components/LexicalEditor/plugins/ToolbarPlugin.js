import { useLexical<PERSON>omposer<PERSON>ontext } from "@lexical/react/LexicalComposerContext";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  CAN_REDO_COMMAND,
  CAN_UNDO_COMMAND,
  REDO_COMMAND,
  UNDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
  FORMAT_TEXT_COMMAND,
  FORMAT_ELEMENT_COMMAND,
  $getSelection,
  $isRangeSelection,
  $createParagraphNode,
  $getNodeByKey
} from "lexical";
import { $isLinkNode, TOGGLE_LINK_COMMAND } from "@lexical/link";
import {
  $isParentElementRTL,
  $wrapNodes,
  $isAtNodeEnd
} from "@lexical/selection";
import { $getNearestNodeOfType, mergeRegister } from "@lexical/utils";
import {
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  REMOVE_LIST_COMMAND,
  $isListNode,
  ListNode
} from "@lexical/list";
import { createPortal } from "react-dom";
import {
  $createHeadingNode,
  $createQuoteNode,
  $isHeadingNode
} from "@lexical/rich-text";
import {
  $createCodeNode,
  $isCodeNode,
  getDefaultCodeLanguage,
  getCodeLanguages
} from "@lexical/code";
import { INSERT_IMAGE_COMMAND } from "./ImagesPlugin";
import useOutsideClick from "../../../hooks/useOutsideClick";
import useHandlePopoverClick from "../../../hooks/useHandlePopoverClick";

const LowPriority = 1;

const supportedBlockTypes = new Set([
  "paragraph",
  "quote",
  "code",
  "h1",
  "h2",
  "ul",
  "ol"
]);

const blockTypeToBlockName = {
  code: "Code Block",
  h1: "Large Heading",
  h2: "Small Heading",
  h3: "Heading",
  h4: "Heading",
  h5: "Heading",
  ol: "Numbered List",
  paragraph: "Normal",
  quote: "Quote",
  ul: "Bulleted List"
};

function Divider() {
  return <div className="divider" />;
}

function positionEditorElement(editor, rect) {
  if (rect === null) {
    editor.style.opacity = "0";
    editor.style.top = "-1000px";
    editor.style.left = "-1000px";
  } else {
    editor.style.opacity = "1";
    editor.style.top = `${rect.top + rect.height + window.pageYOffset + 10}px`;
    editor.style.left = `${rect.left + window.pageXOffset - editor.offsetWidth / 2 + rect.width / 2
      }px`;
  }
}

function FloatingLinkEditor({ editor }) {
  const editorRef = useRef(null);
  const inputRef = useRef(null);
  const mouseDownRef = useRef(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [isEditMode, setEditMode] = useState(false);
  const [lastSelection, setLastSelection] = useState(null);

  const updateLinkEditor = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const node = getSelectedNode(selection);
      const parent = node.getParent();
      if ($isLinkNode(parent)) {
        setLinkUrl(parent.getURL());
      } else if ($isLinkNode(node)) {
        setLinkUrl(node.getURL());
      } else {
        setLinkUrl("");
      }
    }
    const editorElem = editorRef.current;
    const nativeSelection = window.getSelection();
    const activeElement = document.activeElement;

    if (editorElem === null) {
      return;
    }

    const rootElement = editor.getRootElement();
    if (
      selection !== null &&
      !nativeSelection.isCollapsed &&
      rootElement !== null &&
      rootElement.contains(nativeSelection.anchorNode)
    ) {
      const domRange = nativeSelection.getRangeAt(0);
      let rect;
      if (nativeSelection.anchorNode === rootElement) {
        let inner = rootElement;
        while (inner.firstElementChild !== null) {
          inner = inner.firstElementChild;
        }
        rect = inner.getBoundingClientRect();
      } else {
        rect = domRange.getBoundingClientRect();
      }

      if (!mouseDownRef.current) {
        positionEditorElement(editorElem, rect);
      }
      setLastSelection(selection);
    } else if (!activeElement || activeElement.className !== "link-input") {
      positionEditorElement(editorElem, null);
      setLastSelection(null);
      setEditMode(false);
      setLinkUrl("");
    }

    return true;
  }, [editor]);

  useEffect(() => {
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateLinkEditor();
        });
      }),

      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          updateLinkEditor();
          return true;
        },
        LowPriority
      )
    );
  }, [editor, updateLinkEditor]);

  useEffect(() => {
    editor.getEditorState().read(() => {
      updateLinkEditor();
    });
  }, [editor, updateLinkEditor]);

  useEffect(() => {
    if (isEditMode && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditMode]);

  return (
    <div ref={editorRef} className="link-editor">
      {isEditMode ? (
        <input
          ref={inputRef}
          className="link-input"
          value={linkUrl}
          onChange={(event) => {
            setLinkUrl(event.target.value);
          }}
          onKeyDown={(event) => {
            if (event.key === "Enter") {
              event.preventDefault();
              if (lastSelection !== null) {
                if (linkUrl !== "") {
                  editor.dispatchCommand(TOGGLE_LINK_COMMAND, linkUrl);
                }
                setEditMode(false);
              }
            } else if (event.key === "Escape") {
              event.preventDefault();
              setEditMode(false);
            }
          }}
        />
      ) : (
        <>
          <div className="link-input">
            <a href={linkUrl} target="_blank" rel="noopener noreferrer">
              {linkUrl}
            </a>
            <div
              className="link-edit"
              role="button"
              tabIndex={0}
              onMouseDown={(event) => event.preventDefault()}
              onClick={() => {
                setEditMode(true);
              }}
            />
          </div>
        </>
      )}
    </div>
  );
}

function Select({ onChange, className, options, value }) {
  return (
    <select className={className} onChange={onChange} value={value}>
      <option hidden={true} value="" />
      {options.map((option) => (
        <option key={option} value={option}>
          {option}
        </option>
      ))}
    </select>
  );
}

function getSelectedNode(selection) {
  const anchor = selection.anchor;
  const focus = selection.focus;
  const anchorNode = selection.anchor.getNode();
  const focusNode = selection.focus.getNode();
  if (anchorNode === focusNode) {
    return anchorNode;
  }
  const isBackward = selection.isBackward();
  if (isBackward) {
    return $isAtNodeEnd(focus) ? anchorNode : focusNode;
  } else {
    return $isAtNodeEnd(anchor) ? focusNode : anchorNode;
  }
}

function BlockOptionsDropdownList({
  editor,
  blockType,
  toolbarRef,
  setShowBlockOptionsDropDown
}) {
  const dropDownRef = useRef(null);

  useEffect(() => {
    const toolbar = toolbarRef.current;
    const dropDown = dropDownRef.current;

    if (toolbar !== null && dropDown !== null) {
      const { top, left } = toolbar.getBoundingClientRect();
      dropDown.style.top = `${top + 40}px`;
      dropDown.style.left = `${left}px`;
    }
  }, [dropDownRef, toolbarRef]);

  useEffect(() => {
    const dropDown = dropDownRef.current;
    const toolbar = toolbarRef.current;

    if (dropDown !== null && toolbar !== null) {
      const handle = (event) => {
        const target = event.target;

        if (!dropDown.contains(target) && !toolbar.contains(target)) {
          setShowBlockOptionsDropDown(false);
        }
      };
      document.addEventListener("click", handle);

      return () => {
        document.removeEventListener("click", handle);
      };
    }
  }, [dropDownRef, setShowBlockOptionsDropDown, toolbarRef]);

  const formatParagraph = () => {
    if (blockType !== "paragraph") {
      editor.update(() => {
        const selection = $getSelection();

        if ($isRangeSelection(selection)) {
          $wrapNodes(selection, () => $createParagraphNode());
        }
      });
    }
    setShowBlockOptionsDropDown(false);
  };

  const formatLargeHeading = () => {
    if (blockType !== "h1") {
      editor.update(() => {
        const selection = $getSelection();

        if ($isRangeSelection(selection)) {
          $wrapNodes(selection, () => $createHeadingNode("h1"));
        }
      });
    }
    setShowBlockOptionsDropDown(false);
  };

  const formatSmallHeading = () => {
    if (blockType !== "h2") {
      editor.update(() => {
        const selection = $getSelection();

        if ($isRangeSelection(selection)) {
          $wrapNodes(selection, () => $createHeadingNode("h2"));
        }
      });
    }
    setShowBlockOptionsDropDown(false);
  };

  const formatBulletList = () => {
    if (blockType !== "ul") {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND);
    }
    setShowBlockOptionsDropDown(false);
  };

  const formatNumberedList = () => {
    if (blockType !== "ol") {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND);
    }
    setShowBlockOptionsDropDown(false);
  };

  const formatQuote = () => {
    if (blockType !== "quote") {
      editor.update(() => {
        const selection = $getSelection();

        if ($isRangeSelection(selection)) {
          $wrapNodes(selection, () => $createQuoteNode());
        }
      });
    }
    setShowBlockOptionsDropDown(false);
  };

  const formatCode = () => {
    if (blockType !== "code") {
      editor.update(() => {
        const selection = $getSelection();

        if ($isRangeSelection(selection)) {
          $wrapNodes(selection, () => $createCodeNode());
        }
      });
    }
    setShowBlockOptionsDropDown(false);
  };

  return (
    <div className="dropdown" ref={dropDownRef}>
      <button className="item" onClick={formatParagraph}>
        <span className="material-symbols-outlined">
          format_align_left
        </span>
        <span className="text">Normal</span>
      </button>
      <button className="item" onClick={formatLargeHeading}>
        <span className="material-symbols-outlined">
          format_h1
        </span>
        <span className="text">Large Heading</span>
      </button>
      <button className="item" onClick={formatSmallHeading}>
        <span className="material-symbols-outlined">
          format_h2
        </span>
        <span className="text">Small Heading</span>
      </button>
      <button className="item" onClick={formatBulletList}>
        <span className="material-symbols-outlined">
          format_list_bulleted
        </span>
        <span className="text">Bullet List</span>
      </button>
      <button className="item" onClick={formatNumberedList}>
        <span className="material-symbols-outlined">
          format_list_numbered
        </span>
        <span className="text">Numbered List</span>
      </button>
      <button className="item" onClick={formatQuote}>
        <span className="material-symbols-outlined">
          format_quote
        </span>
        <span className="text">Quote</span>
      </button>
      <button className="item" onClick={formatCode}>
        <span className="material-symbols-outlined">
          code
        </span>
        <span className="text">Code Block</span>
      </button>
    </div>
  );
}

function AlignDropdownList({ editor, blockType, alignmentRef, tergetElement, setShowAlignOptions }) {

  const rect = tergetElement.getBoundingClientRect();
  const position = { top: rect.bottom + 2, left: rect.left - 230 };

  useOutsideClick(alignmentRef, () => {
    setShowAlignOptions(false);
  });


  return (
    <div className="dropdown alignment-dropdown" ref={alignmentRef} style={position}>
      {/* 
      <button className="item" onClick={formatBulletList}>
        <span className="icon bullet-list" />
        <span className="text">Bullet List</span>
        {blockType === "ul" && <span className="active" />}
      </button> */}

      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "left");
          setShowAlignOptions(false);

        }}
        className="item"
        aria-label="Left Align"
      >
        <span className="material-symbols-outlined">
          format_align_left
        </span>
        <span className="text">Left</span>
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "center");
          setShowAlignOptions(false);

        }}
        className="item"
        aria-label="Center Align"
      >
        <span className="material-symbols-outlined">
          format_align_center
        </span>
        <span className="text">Center</span>
      </button>

      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "right");
          setShowAlignOptions(false);
        }}
        className="item"
        aria-label="Right Align"
      >
        <span className="material-symbols-outlined">
          format_align_right
        </span>
        <span className="text">Right</span>
      </button>

      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "justify");
          setShowAlignOptions(false);
        }}
        className="item"
        aria-label="Justify Align"
      >
        <span className="material-symbols-outlined">
          format_align_justify
        </span>
        <span className="text">Justify</span>
      </button>


    </div>
  )
}
function TransformDropdownList({ editor, blockType, transformRef, tergetElement, setShowTransform, isStrikethrough, isUnderline, isCode }) {

  const rect = tergetElement.getBoundingClientRect();
  const position = { top: rect.bottom + 2, left: rect.left - 230 };

  useOutsideClick(transformRef, () => {
    setShowTransform(false);
  });


  return (
    <div className="dropdown transform-dropdown" ref={transformRef} style={position}>
      {/* 
      <button className="item" onClick={formatBulletList}>
        <span className="icon bullet-list" />
        <span className="text">Bullet List</span>
        {blockType === "ul" && <span className="active" />}
      </button> */}

      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "underline");
          setShowTransform(false);
        }}
        className={"item " + (isUnderline ? "active" : "")}
        aria-label="Format Underline"
      >
        <span className="material-symbols-outlined">format_underlined</span> {/* Icon for underline */}
        <span className="text">Underline</span>
      </button>

      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "strikethrough");
          setShowTransform(false);

        }}
        className={"item " + (isStrikethrough ? "active" : "")}
        aria-label="Format Strikethrough"
      >
        <span className="material-symbols-outlined">strikethrough_s</span> {/* Icon for strikethrough */}
        <span className="text">Strikethrough</span>
      </button>

      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "code");
          setShowTransform(false);

        }}
        className={"item " + (isCode ? "active" : "")}
        aria-label="Insert Code"
      >
        <span className="material-symbols-outlined">code</span> {/* Icon for code */}
        <span className="text">Code</span>
      </button>

    </div>
  )
}

export default function ToolbarPlugin() {
  const [editor] = useLexicalComposerContext();
  const toolbarRef = useRef(null);
  const alignmentRef = useRef(null);
  const transformRef = useRef(null);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [blockType, setBlockType] = useState("paragraph");
  const [selectedElementKey, setSelectedElementKey] = useState(null);
  const [showBlockOptionsDropDown, setShowBlockOptionsDropDown] = useState(false);
  const [showAlignOptions, setShowAlignOptions] = useState(false);
  const [showTransform, setShowTransform] = useState(false);
  const [codeLanguage, setCodeLanguage] = useState("");
  const [isRTL, setIsRTL] = useState(false);
  const [isLink, setIsLink] = useState(false);
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [isCode, setIsCode] = useState(false);

  const [targetElement, setTargetElement] = useState(null);

  const { handlePopoverClick } = useHandlePopoverClick();

  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const anchorNode = selection.anchor.getNode();
      const element =
        anchorNode.getKey() === "root"
          ? anchorNode
          : anchorNode.getTopLevelElementOrThrow();
      const elementKey = element.getKey();
      const elementDOM = editor.getElementByKey(elementKey);
      if (elementDOM !== null) {
        setSelectedElementKey(elementKey);
        if ($isListNode(element)) {
          const parentList = $getNearestNodeOfType(anchorNode, ListNode);
          const type = parentList ? parentList.getTag() : element.getTag();
          setBlockType(type);
        } else {
          const type = $isHeadingNode(element)
            ? element.getTag()
            : element.getType();
          setBlockType(type);
          if ($isCodeNode(element)) {
            setCodeLanguage(element.getLanguage() || getDefaultCodeLanguage());
          }
        }
      }
      // Update text format
      setIsBold(selection.hasFormat("bold"));
      setIsItalic(selection.hasFormat("italic"));
      setIsUnderline(selection.hasFormat("underline"));
      setIsStrikethrough(selection.hasFormat("strikethrough"));
      setIsCode(selection.hasFormat("code"));
      setIsRTL($isParentElementRTL(selection));

      // Update links
      const node = getSelectedNode(selection);
      const parent = node.getParent();
      if ($isLinkNode(parent) || $isLinkNode(node)) {
        setIsLink(true);
      } else {
        setIsLink(false);
      }
    }
  }, [editor]);

  useEffect(() => {
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        (_payload, newEditor) => {
          updateToolbar();
          return false;
        },
        LowPriority
      ),
      editor.registerCommand(
        CAN_UNDO_COMMAND,
        (payload) => {
          setCanUndo(payload);
          return false;
        },
        LowPriority
      ),
      editor.registerCommand(
        CAN_REDO_COMMAND,
        (payload) => {
          setCanRedo(payload);
          return false;
        },
        LowPriority
      )
    );
  }, [editor, updateToolbar]);

  const codeLanguges = useMemo(() => getCodeLanguages(), []);
  const onCodeLanguageSelect = useCallback(
    (e) => {
      editor.update(() => {
        if (selectedElementKey !== null) {
          const node = $getNodeByKey(selectedElementKey);
          if ($isCodeNode(node)) {
            node.setLanguage(e.target.value);
          }
        }
      });
    },
    [editor, selectedElementKey]
  );

  const insertLink = useCallback(() => {
    if (!isLink) {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, "https://");
    } else {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
    }
  }, [editor, isLink]);

  const insertSampleImage = () => {
    const payload = {
      altText: "Sample Image",
      src: "https://images.pexels.com/photos/5656637/pexels-photo-5656637.jpeg?auto=compress&cs=tinysrgb&w=200",
    };
    editor.dispatchCommand(INSERT_IMAGE_COMMAND, payload);
  };

  const insertFromURL = () => {
    const payload = {
      altText: "URL Image",
      src: prompt("Enter Image URL", "https://example.com/sample.jpg"),
    };
    if (payload.src) {
      editor.dispatchCommand(INSERT_IMAGE_COMMAND, payload);
    }
  };


  return (
    <div className="toolbar" ref={toolbarRef}>
      {/* 
      <button
        onClick={insertSampleImage}
        className="toolbar-item spaced"
      >
        <span className="text">Insert Sample Image</span>
      </button>

      <button
        onClick={insertFromURL}
        className="toolbar-item spaced"
      >
        <span className="text">Insert from URL</span>
      </button> */}

      <button
        disabled={!canUndo}
        onClick={() => {
          editor.dispatchCommand(UNDO_COMMAND);
        }}
        className="toolbar-item spaced"
        aria-label="Undo"
      >
        <span className="material-symbols-outlined">
          undo
        </span>
      </button>
      <button
        disabled={!canRedo}
        onClick={() => {
          editor.dispatchCommand(REDO_COMMAND);
        }}
        className="toolbar-item"
        aria-label="Redo"
      >
        <span className="material-symbols-outlined">
          redo
        </span>
      </button>
      <Divider />
      {/* {supportedBlockTypes.has(blockType) && ( */}
      <>
        <button
          disabled={!supportedBlockTypes.has(blockType)}
          className="toolbar-item block-controls"
          onClick={() =>
            setShowBlockOptionsDropDown(!showBlockOptionsDropDown)
          }
          aria-label="Formatting Options"
        >
          {/* <span className={"icon block-type " + blockType} /> */}
          <span className="material-symbols-outlined match_case">
            match_case
          </span>

          <span className="material-symbols-outlined keyboard_arrow_down">
            keyboard_arrow_down
          </span>
        </button>
        {showBlockOptionsDropDown &&
          createPortal(
            <BlockOptionsDropdownList
              editor={editor}
              blockType={blockType}
              toolbarRef={toolbarRef}
              setShowBlockOptionsDropDown={setShowBlockOptionsDropDown}
            />,
            document.body
          )}
        <Divider />
      </>
      {/* )} */}
      {blockType === "code" ? (
        <>
          <Select
            className="toolbar-item code-language"
            onChange={onCodeLanguageSelect}
            options={codeLanguges}
            value={codeLanguage}
          />
          <i className="chevron-down inside" />
        </>
      ) : (
        <>
          <button
            onClick={() => {
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, "bold");
            }}
            className={"toolbar-item spaced " + (isBold ? "active" : "")}
            aria-label="Format Bold"
          >
            <span className="material-symbols-outlined">
              format_bold
            </span>
          </button>
          <button
            onClick={() => {
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, "italic");
            }}
            className={"toolbar-item spaced " + (isItalic ? "active" : "")}
            aria-label="Format Italics"
          >
            <span className="material-symbols-outlined">
              format_italic
            </span>
          </button>
          <button
            onClick={(e) => {
              setTargetElement(e.currentTarget);
              setShowTransform(!showTransform);
            }}
            aria-label="Format more"
            className="toolbar-item spaced"
            ref={transformRef}
            data-popover-trigger

          >
            <span className="material-symbols-outlined">
              more_horiz
            </span>
          </button>

          {
            showTransform && (
              createPortal(
                <TransformDropdownList
                  editor={editor}
                  blockType={blockType}
                  transformRef={transformRef}
                  tergetElement={targetElement}
                  setShowTransform={setShowTransform}
                  isStrikethrough={isStrikethrough}
                  isUnderline={isUnderline}
                  isCode={isCode}
                />,
                document.body
              )
            )
          }
          <Divider />

          <button
            onClick={insertLink}
            className={"toolbar-item spaced " + (isLink ? "active" : "")}
            aria-label="Insert Link"
          >
            <span className="material-symbols-outlined">
              link
            </span>
          </button>
          {isLink &&
            createPortal(<FloatingLinkEditor editor={editor} />, document.body)}
          <button
            onClick={(e) => {
              setTargetElement(e.currentTarget);
              setShowAlignOptions(!showAlignOptions);
            }}
            className="toolbar-item spaced align-controls"
            aria-label="Left Align"
            ref={alignmentRef}
            data-popover-trigger
          >
            <span className="material-symbols-outlined">
              add
            </span>

            <span className="material-symbols-outlined keyboard_arrow_down">
              keyboard_arrow_down
            </span>

          </button>

          {
            showAlignOptions && (
              createPortal(
                <AlignDropdownList
                  editor={editor}
                  blockType={blockType}
                  alignmentRef={alignmentRef}
                  tergetElement={targetElement}
                  setShowAlignOptions={setShowAlignOptions}
                />,
                document.body
              )
            )
          }

          {" "}
        </>
      )}

      <button
        onClick={(e) => handlePopoverClick(e, 'editorAttach', { type: 'IMAGE', editor })}
        id="popover-add-editor-image"
        className="toolbar-item spaced align-controls"
        aria-label="Add Image"
        data-popover-trigger
      >
        <span className="material-symbols-outlined">
          add_photo_alternate
        </span>
      </button>

      <button
        onClick={(e) => handlePopoverClick(e, 'editorAttach', { type: 'ATTACHMENT', editor })}
        id="popover-add-editor-attach"
        className="toolbar-item spaced align-controls"
        aria-label="Add Image"
        data-popover-trigger
      >
        <span className="material-symbols-outlined">
          attach_file_add
        </span>
      </button>

      <button
        onClick={(e) => handlePopoverClick(e, 'editorInfo')}
        id="popover-info"
        className="toolbar-item spaced align-controls"
        aria-label="Editor Info"
        data-popover-trigger
      >
        <span className="material-symbols-outlined">
          info
        </span>
      </button>

    </div>
  );
}

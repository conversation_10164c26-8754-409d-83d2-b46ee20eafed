/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  LexicalTypeaheadMenuPlugin,
  MenuOption,
  useBasicTypeaheadTriggerMatch
} from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import * as React from "react";
import * as ReactDOM from "react-dom";

import { $createMentionNode } from "../nodes/MentionNode";
import ImagePlaceholder from "../../Global/ImagePlaceholder";
import { mentionedMemberIds } from "../../../redux/Slices/mentionedSlice";

const PUNCTUATION =
  "\\.,\\+\\*\\?\\$\\@\\|#{}\\(\\)\\^\\-\\[\\]\\\\/!%'\"~=<>_:;";
const NAME = "\\b[A-Z][^\\s" + PUNCTUATION + "]";

const DocumentMentionsRegex = {
  NAME,
  PUNCTUATION
};

const PUNC = DocumentMentionsRegex.PUNCTUATION;

const TRIGGERS = ["@"].join("");

// Chars we expect to see in a mention (non-space, non-punctuation).
const VALID_CHARS = "[^" + TRIGGERS + PUNC + "\\s]";

// Non-standard series of chars. Each series must be preceded and followed by
// a valid char.
const VALID_JOINS =
  "(?:" +
  "\\.[ |$]|" + // E.g. "r. " in "Mr. Smith"
  " |" + // E.g. " " in "Josh Duck"
  "[" +
  PUNCTUATION +
  "]|" + // E.g. "-' in "Salier-Hellendag"
  ")";

const LENGTH_LIMIT = 75;

const AtSignMentionsRegex = new RegExp(
  "(^|\\s|\\()(" +
  "[" +
  TRIGGERS +
  "]" +
  "((?:" +
  VALID_CHARS +
  VALID_JOINS +
  "){0," +
  LENGTH_LIMIT +
  "})" +
  ")$"
);

// 50 is the longest alias length limit.
const ALIAS_LENGTH_LIMIT = 50;

// Regex used to match alias.
const AtSignMentionsRegexAliasRegex = new RegExp(
  "(^|\\s|\\()(" +
  "[" +
  TRIGGERS +
  "]" +
  "((?:" +
  VALID_CHARS +
  "){0," +
  ALIAS_LENGTH_LIMIT +
  "})" +
  ")$"
);

// At most, 5 suggestions are shown in the popup.
const SUGGESTION_LIST_LENGTH_LIMIT = 5;

const mentionsCache = new Map();

function useMentionLookupService(mentionString) {
  const [results, setResults] = useState([]);

  // Get boardMembers from Redux state
  const { boardMembers } = useSelector(state => state.member);

  const additionalSuggestions = [
    { name: "All members in the card", username: "card", img: "seakashdiu_img_url" }, // Replace with actual image URL
    { name: "All members in the board", username: "board", img: "akash_img_url" }
  ];

  const combinedMembers = useMemo(() => {
    return [...boardMembers, ...additionalSuggestions];
  }, [boardMembers]);

  useEffect(() => {
    const cachedResults = mentionsCache.get(mentionString);

    if (mentionString == null) {
      setResults([]);
      return;
    }

    if (cachedResults === null) {
      return;
    } else if (cachedResults !== undefined) {
      setResults(cachedResults);
      return;
    }

    mentionsCache.set(mentionString, null);

    // Replace dummyLookupService with boardMembers search
    const newResults = combinedMembers.filter(member => {
      if (!member) return false;
      return member?.name?.toLowerCase().includes(mentionString.toLowerCase());
    });


    mentionsCache.set(mentionString, newResults);
    setResults(newResults);

  }, [mentionString, combinedMembers]); // Add boardMembers as a dependency

  return results;
}

function checkForAtSignMentions(text, minMatchLength) {
  let match = AtSignMentionsRegex.exec(text);

  if (match === null) {
    match = AtSignMentionsRegexAliasRegex.exec(text);
  }
  if (match !== null) {
    const maybeLeadingWhitespace = match[1];

    const matchingString = match[3];
    if (matchingString.length >= minMatchLength) {
      return {
        leadOffset: match.index + maybeLeadingWhitespace.length,
        matchingString,
        replaceableString: match[2]
      };
    }
  }
  return null;
}

function getPossibleQueryMatch(text) {
  return checkForAtSignMentions(text, 0);
}

class MentionTypeaheadOption extends MenuOption {
  constructor(name, picture, username, id) {
    super(name);
    this.name = name;
    this.picture = picture;
    this.username = username; // Store username
    this.id = id; // Store id (or any other properties you need)
  }
}

function MentionsTypeaheadMenuItem({
  index,
  isSelected,
  onClick,
  onMouseEnter,
  option
}) {
  let className = "item";
  if (isSelected) {
    className += " selected";
  }
  return (
    <li
      key={option.key}
      tabIndex={-1}
      className={className}
      ref={option.setRefElement}
      role="option"
      aria-selected={isSelected}
      id={"typeahead-item-" + index}
      onMouseEnter={onMouseEnter}
      onClick={onClick}
    >
      <div className="picture">
        {option.picture}
      </div>

      <div className="userinfo">
        <span className="text">{option.name}</span>
        <span className="username">@{option.username}</span>
      </div>
    </li>
  );
}

export default function NewMentionsPlugin() {
  const [editor] = useLexicalComposerContext();

  const [queryString, setQueryString] = useState(null);

  const results = useMentionLookupService(queryString);

  const { cardMembers, boardMembers } = useSelector(state => state.member);

  const dispatch = useDispatch();

  const checkForSlashTriggerMatch = useBasicTypeaheadTriggerMatch("/", {
    minLength: 0
  });

  const options = useMemo(
    () =>
      results
        .map(result =>
          new MentionTypeaheadOption(
            result.name,
            result.profilePicture ? (
              <img
                className="mention-img"
                src={result.profilePicture}
                alt={result.name}
                style={{ width: "35px", height: "35px", borderRadius: "50%" }}
                loading="lazy"
              />
            ) : (
              <ImagePlaceholder key={result._id} size={35} member={result} text={result.username} />
            ),
            result.username, // Pass the username
            result._id // Pass the id
          )
        )
        .slice(0, SUGGESTION_LIST_LENGTH_LIMIT),
    [results]
  );

  const onSelectOption = useCallback(
    (selectedOption, nodeToReplace, closeMenu) => {
      const { name, username, id } = selectedOption;

      // Update mentioned IDs based on mention type
      if (username === 'card') {
        cardMembers.forEach(member => {
          dispatch(mentionedMemberIds(member._id));
        });
      } else if (username === 'board') {
        boardMembers.forEach(member => {
          dispatch(mentionedMemberIds(member._id));
        });
      } else {
        dispatch(mentionedMemberIds(id));
      }

      editor.update(() => {
        const mentionNode = $createMentionNode(name, username);
        if (nodeToReplace) {
          nodeToReplace.replace(mentionNode);
        }
        mentionNode.select();
        closeMenu();
      });
    },
    [editor, cardMembers, boardMembers, dispatch]
  );

  const checkForMentionMatch = useCallback(
    text => {
      const slashMatch = checkForSlashTriggerMatch(text, editor);
      if (slashMatch !== null) {
        return null;
      }
      return getPossibleQueryMatch(text);
    },
    [checkForSlashTriggerMatch, editor]
  );

  return (
    <LexicalTypeaheadMenuPlugin
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      triggerFn={checkForMentionMatch} // Corrected this line
      options={options}
      menuRenderFn={(
        anchorElementRef,
        { selectedIndex, selectOptionAndCleanUp, setHighlightedIndex }
      ) =>
        anchorElementRef.current && results.length
          ? ReactDOM.createPortal(
            <div className="typeahead-popover mentions-menu">
              <ul>
                {options.map((option, i) => (
                  <MentionsTypeaheadMenuItem
                    index={i}
                    isSelected={selectedIndex === i}
                    onClick={() => {
                      setHighlightedIndex(i);
                      selectOptionAndCleanUp(option);
                    }}
                    onMouseEnter={() => {
                      setHighlightedIndex(i);
                    }}
                    key={option.key}
                    option={option}
                  />
                ))}
              </ul>
            </div>,
            anchorElementRef.current
          )
          : null
      }
    />
  );
}

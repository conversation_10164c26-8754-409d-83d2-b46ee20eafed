.labels-container {
  width: calc(100% - 30px);
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  font-family: system-ui;
  width: 280px;
}
.labels-container .labels-header {
  margin-bottom: 20px;
}
.labels-container .labels-header h3 {
  margin: 0;
  font-size: 14px;
  text-align: center;
  font-weight: var(--popover-title-popover-font-weight);
}
.labels-container .labels-header .close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}
.labels-container .search-input {
  width: calc(100% - 15px);
  padding: 0 8px;
  margin-bottom: 15px;
  border-radius: var(--element-border-radius);
  outline: 1px solid var(--secondary-outline-color);
  background-color: var(--popover-background-color);
  color: var(--single-card-text-color);
  height: 35px;
  border: none;
}
.labels-container .search-input:focus {
  outline: 2px solid var(--focus-outline-color);
}
.labels-container .labels-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.labels-container .labels-list .label-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.labels-container .labels-list .label-item .label-color {
  width: 100%;
  height: 30px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.75;
  color: var(--label-text-color);
  font-weight: var(--label-font-weight);
  cursor: pointer;
  font-size: 14px;
  font-family: system-ui;
}
.labels-container .labels-list .label-item .label-color:hover {
  opacity: 0.6 !important;
}
.labels-container .labels-list .label-item .edit-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  width: 30px;
  height: 30px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.labels-container .labels-list .label-item .edit-button:hover {
  background-color: #302b2b;
}
.labels-container .labels-list .label-item .edit-button span {
  color: var(--single-card-text-color);
  font-size: 18px;
}
.labels-container .create-label-button,
.labels-container .colorblind-mode-button {
  width: 100%;
  margin-top: 15px;
  border: none;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  font-size: 14px;
  background-color: var(--single-card-side-action-bg-color);
  height: 35px;
  font-family: system-ui;
  color: var(--primary-text-color);
  font-weight: 600;
}
.labels-container .create-label-button:hover,
.labels-container .colorblind-mode-button:hover {
  background-color: var(--single-card-action-button-hover-color);
}

.edit-label-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
  max-width: 170px;
  margin-top: -10px;
}
.edit-label-modal .modal-header h3 {
  font-size: 14px;
  color: var(--single-card-text-color);
  font-weight: 600;
  font-family: system-ui;
}
.edit-label-modal .modal-header .back-button,
.edit-label-modal .modal-header .close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
}
.edit-label-modal .modal-header .back-button span {
  font-size: 18px;
  color: var(--single-card-text-color);
}
.edit-label-modal .label-preview {
  width: calc(100% - 20px);
  padding: 0 10px;
  background: #ddd;
  border-radius: var(--element-border-radius);
  text-align: center;
  margin-bottom: 15px;
  font-size: 16px;
  height: 35px;
  align-items: center;
  justify-content: center;
  display: flex;
  color: var(--label-text-color);
  font-weight: var(--label-font-weight);
  opacity: 0.75;
}
.edit-label-modal label {
  display: block;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 600;
}
.edit-label-modal .title-input {
  width: calc(100% - 20px);
  padding: 0 10px;
  background-color: var(--input-background-color);
  outline: 1px solid var(--secondary-outline-color);
  border-radius: var(--element-border-radius);
  color: var(--white-text-color-alternative);
  font-size: 14px;
  border: none;
  height: 35px;
  margin-bottom: 15px;
  color: var(--single-card-text-color);
  font-weight: 600;
}
.edit-label-modal .title-input:focus {
  outline: 2px solid var(--focus-outline-color);
}
.edit-label-modal .color-picker {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}
.edit-label-modal .color-picker .color-box {
  height: 34px;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  border: 2px solid transparent;
  opacity: 0.75;
}
.edit-label-modal .color-picker .color-box.selected {
  border: 2px solid transparent;
}
.edit-label-modal .remove-color-button {
  background: none;
  border: none;
  color: var(--single-card-text-color);
  cursor: pointer;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  width: 100%;
  padding: 5px;
  text-align: center;
  justify-content: center;
}
.edit-label-modal .remove-color-button:hover {
  text-decoration: underline;
}
.edit-label-modal .modal-footer {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}
.edit-label-modal .modal-footer .save-button,
.edit-label-modal .modal-footer .delete-button {
  width: 100px;
  padding: 10px;
  border: none;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  font-size: 14px;
}
.edit-label-modal .modal-footer .save-button {
  background-color: var(--single-card-side-action-bg-color);
  font-family: system-ui;
  color: var(--primary-text-color);
  font-weight: 600;
}
.edit-label-modal .modal-footer .save-button:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.edit-label-modal .modal-footer .delete-button {
  background: #dc3545;
  color: var(--white-text-color-alternative);
}/*# sourceMappingURL=AddCardLabels.css.map */
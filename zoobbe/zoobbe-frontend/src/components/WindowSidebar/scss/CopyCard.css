li.zoobbe-select-group {
  padding: 5px 12px;
}
li.zoobbe-select-group label {
  font-size: 12px;
  text-transform: uppercase;
}

li.zoobbe-select-option {
  font-size: 12px;
  font-family: system-ui;
}

ul.board-options {
  padding: 0;
  list-style: none;
  margin-left: -10px;
}

.copy-card {
  padding: 15px;
  width: 280px;
}
.copy-card h2 {
  font-size: 18px;
  margin-bottom: 20px;
  margin-top: 0;
  font-size: 14px;
  text-align: center;
  font-weight: var(--popover-title-popover-font-weight);
  font-family: system-ui;
}
.copy-card .group {
  margin-bottom: 20px;
}
.copy-card .group h3 {
  font-size: 12px;
  margin-top: 0;
}
.copy-card input,
.copy-card select {
  width: calc(100% - 20px);
  padding: 0 10px;
  background-color: var(--popover-background-color);
  border: 1px solid var(--secondary-outline-color);
  border-radius: var(--element-border-radius);
  color: var(--single-card-text-color);
  font-size: 14px;
  height: 35px;
  font-weight: var(--single-card-side-action-font-weight);
}
.copy-card input:focus,
.copy-card select:focus {
  outline: 0;
  box-shadow: none;
}
.copy-card .textarea-wrapper {
  width: calc(100% - 20px);
  padding: 6px 10px;
  background-color: var(--popover-background-color);
  outline: 1px solid var(--secondary-outline-color);
  border-radius: var(--element-border-radius);
  display: flex;
  align-items: center;
}
.copy-card .textarea-wrapper textarea {
  background-color: var(--popover-background-color);
  border: none;
  background-color: var(--popover-background-color);
  border: none;
  width: 100%;
  color: var(--single-card-text-color);
  font-size: 14px;
  font-weight: var(--single-card-side-action-font-weight);
  line-height: 1.2rem;
  padding: 0;
  resize: none;
  font-family: system-ui;
}
.copy-card .textarea-wrapper textarea:focus {
  outline: none;
}
.copy-card .textarea-wrapper.focused {
  outline: 2px solid var(--focus-outline-color);
}
.copy-card .zoobbe-select {
  width: 100%;
  margin-bottom: 0;
}
.copy-card .keep-members {
  display: flex;
  align-items: center;
  gap: 10px;
}
.copy-card .keep-members span.label {
  font-weight: 600;
  font-size: 14px;
  font-family: system-ui;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.copy-card button {
  padding: 10px 20px;
  background-color: var(--brand-color);
  border: none;
  border-radius: var(--element-border-radius);
  color: var(--brand-btn-primary-color);
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.copy-card button:hover {
  background-color: #125fcc;
}

.actionlists-group {
  display: flex;
  gap: 10px;
}
.actionlists-group .group.actionlists {
  width: calc(100% - 90px);
}
.actionlists-group .group.position {
  width: 80px;
}/*# sourceMappingURL=CopyCard.css.map */
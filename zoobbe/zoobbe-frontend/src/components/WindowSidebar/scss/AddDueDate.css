.add-due-date-popup {
  background-color: var(--popover-background-color);
  color: var(--single-card-text-color);
  padding: 20px;
  border-radius: 8px;
  width: 260px;
}
.add-due-date-popup .react-calendar {
  background: var(--popover-background-color);
  border: 1px solid #a0a096;
  font-family: system-ui;
  border: none;
}
.add-due-date-popup .react-calendar button.react-calendar__navigation__arrow.react-calendar__navigation__next2-button,
.add-due-date-popup .react-calendar button.react-calendar__navigation__arrow.react-calendar__navigation__prev2-button {
  display: none;
}
.add-due-date-popup .react-calendar button.react-calendar__navigation__arrow.react-calendar__navigation__next-button,
.add-due-date-popup .react-calendar button.react-calendar__navigation__arrow.react-calendar__navigation__prev-button {
  font-size: 25px;
  font-weight: 600;
}
.add-due-date-popup .zoobbe-select {
  margin: 0;
}
.add-due-date-popup .zoobbe-select .zoobbe-select-option {
  padding: 0px 12px;
  font-size: 14px;
  font-weight: 500;
  font-family: system-ui;
}
.add-due-date-popup .zoobbe-select-options {
  overflow: auto;
  height: 140px;
}
.add-due-date-popup .react-calendar__navigation button,
.add-due-date-popup .react-calendar__navigation button:enabled:hover,
.add-due-date-popup .react-calendar__navigation button:enabled:focus {
  background-color: var(--popover-background-color);
  color: var(--single-card-text-color);
  font-size: 15px;
  font-family: system-ui;
}
.add-due-date-popup .add-due-date-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  justify-content: center;
}
.add-due-date-popup .add-due-date-popup-header h3 {
  margin-bottom: 20px;
  margin-top: -5px;
  font-size: 14px;
  text-align: center;
  font-weight: var(--popover-title-popover-font-weight);
  font-family: system-ui;
}
.add-due-date-popup .add-due-date-popup-header .close-btn {
  background: none;
  border: none;
  color: var(--single-card-text-color);
  font-size: 1.5rem;
  cursor: pointer;
}
.add-due-date-popup .calendar-grid {
  margin-bottom: 20px;
  font-family: system-ui;
}
.add-due-date-popup .calendar-grid .react-calendar__tile--now {
  background: #3e4b58;
}
.add-due-date-popup .calendar-grid .react-calendar__tile--active {
  background: #0052cc;
  color: var(--white-text-color-alternative);
}
.add-due-date-popup .calendar-grid .react-calendar__month-view__days__day--weekend {
  color: #768390;
}
.add-due-date-popup .calendar-grid .react-calendar__tile {
  background: none;
  border-radius: var(--element-border-radius);
  text-align: center;
  color: var(--single-card-text-color);
  padding: 5px;
  font-family: system-ui;
  font-weight: 500;
}
.add-due-date-popup .calendar-grid .react-calendar__tile:hover {
  background-color: #282828;
}
.add-due-date-popup .react-calendar__month-view__weekdays abbr {
  text-decoration: none !important;
  text-transform: none;
}
.add-due-date-popup .date-inputs input:focus-visible {
  outline: 2px solid var(--brand-color);
}
.add-due-date-popup .date-inputs .input-group {
  margin-bottom: 10px;
}
.add-due-date-popup .date-inputs .input-group .start-date-checkbox,
.add-due-date-popup .date-inputs .input-group .due-date-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
  font-family: system-ui;
  gap: 5px;
}
.add-due-date-popup .date-inputs .input-group .start-date-checkbox .checkbox,
.add-due-date-popup .date-inputs .input-group .due-date-checkbox .checkbox {
  width: 10px;
  height: 10px;
}
.add-due-date-popup .date-inputs .input-group .start-date-checkbox .checkbox span.material-symbols-outlined.check-mark,
.add-due-date-popup .date-inputs .input-group .due-date-checkbox .checkbox span.material-symbols-outlined.check-mark {
  font-size: 13px;
}
.add-due-date-popup .date-inputs .input-group .start-date-checkbox input[type=checkbox],
.add-due-date-popup .date-inputs .input-group .due-date-checkbox input[type=checkbox] {
  margin-right: 10px;
}
.add-due-date-popup .date-inputs .input-group .start-date-checkbox label,
.add-due-date-popup .date-inputs .input-group .due-date-checkbox label {
  font-size: 14px;
  color: var(--single-card-text-color);
}
.add-due-date-popup .date-inputs .input-group input[type=text] {
  width: 100%;
  background-color: var(--popover-background-color);
  outline: 1px solid var(--outline-color);
  color: var(--single-card-text-color);
  padding: 5px 10px;
  border-radius: var(--element-border-radius);
  margin-bottom: 10px;
  border: none;
}
.add-due-date-popup .date-inputs .input-group input[type=text]:focus-visible {
  outline: 2px solid var(--brand-color) !important;
}
.add-due-date-popup .date-inputs .input-group input[type=text]:disabled {
  background-color: var(--popover-background-color);
  color: #768390;
}
.add-due-date-popup .date-inputs .input-group .due-date-field {
  display: flex;
  gap: 10px;
}
.add-due-date-popup .date-inputs .input-group .due-date-field input[type=text] {
  flex: 1;
}
.add-due-date-popup .date-inputs .input-group .due-date-field input[type=time] {
  max-width: 150px;
  background-color: var(--popover-background-color);
  outline: 1px solid var(--outline-color);
  color: var(--single-card-text-color);
  padding: 5px 10px;
  border-radius: var(--element-border-radius);
}
.add-due-date-popup .reminder {
  margin-bottom: 20px;
}
.add-due-date-popup .reminder label {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
  font-family: system-ui;
}
.add-due-date-popup .reminder select {
  width: 100%;
  background-color: #2c3e50;
  border: none;
  color: var(--single-card-text-color);
  padding: 5px;
  border-radius: var(--element-border-radius);
  margin-bottom: 10px;
}
.add-due-date-popup .reminder p {
  font-size: 0.85rem;
  color: #768390;
}
.add-due-date-popup .actions {
  display: flex;
  justify-content: space-between;
}
.add-due-date-popup .actions .save-btn,
.add-due-date-popup .actions .remove-btn {
  padding: 10px 20px;
  border: none;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  font-size: 1rem;
  width: 48%;
}
.add-due-date-popup .actions .save-btn {
  background-color: var(--brand-color);
  color: var(--brand-btn-primary-color);
}
.add-due-date-popup .actions .remove-btn {
  background-color: var(--single-card-text-color);
  color: #2c3e50;
}/*# sourceMappingURL=AddDueDate.css.map */
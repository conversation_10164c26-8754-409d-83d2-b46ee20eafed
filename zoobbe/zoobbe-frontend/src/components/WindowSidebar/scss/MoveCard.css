li.zoobbe-select-group {
  padding: 5px 12px;
}
li.zoobbe-select-group label {
  font-size: 12px;
  text-transform: uppercase;
}

li.zoobbe-select-option {
  font-size: 12px;
  font-family: system-ui;
}

ul.board-options {
  padding: 0;
  list-style: none;
  margin-left: -10px;
}

.move-card {
  padding: 15px;
  width: 280px;
}
.move-card h2 {
  font-size: 18px;
  margin-bottom: 20px;
  margin-top: 0;
  font-size: 14px;
  text-align: center;
  font-weight: var(--popover-title-popover-font-weight);
  font-family: system-ui;
}
.move-card .group {
  margin-bottom: 20px;
}
.move-card .group h3 {
  font-size: 12px;
  margin-top: 0;
}
.move-card input,
.move-card select {
  width: calc(100% - 20px);
  padding: 0 10px;
  background-color: #3b3b3b;
  border: 1px solid var(--outline-color);
  border-radius: var(--element-border-radius);
  color: var(--white-text-color-alternative);
  font-size: 14px;
  height: 35px;
}
.move-card input:focus,
.move-card select:focus {
  outline: 0;
  box-shadow: none;
}
.move-card .zoobbe-select {
  width: 100%;
  margin-bottom: 0;
}
.move-card button {
  padding: 0px 20px;
  background-color: var(--brand-color);
  border: none;
  border-radius: var(--element-border-radius);
  color: var(--brand-btn-primary-color);
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  height: 35px;
  min-width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.move-card button:hover {
  background-color: #125fcc;
}

.actionlists-group {
  display: flex;
  gap: 10px;
}
.actionlists-group .group.actionlists {
  width: calc(100% - 90px);
}
.actionlists-group .group.position {
  width: 80px;
}/*# sourceMappingURL=MoveCard.css.map */
import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { config } from '../../config';
import { togglePopover } from '../../redux/Slices/popoverSlice';
import { showToast } from '../../redux/Slices/toastSlice';

import './scss/DeleteCard.scss';
import { removeArchivedCard } from '../../redux/Slices/cardSlice';


const DeleteCard = ({ cardId }) => {
    const inputRef = useRef(null);
    const { card = {} } = useSelector((state) => state.card) || {};
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const handleDeleteCard = async () => {
        const token = localStorage.getItem('accessToken');
        try {
            const response = await fetch(config.API_URI + `/api/cards/delete/${cardId || card?.shortId}`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error('Failed to delete the card');
            }

            dispatch(removeArchivedCard(cardId || card?.shortId));

            const data = await response.json();
            dispatch(togglePopover({ contentId: null, position: { top: 0, left: 0 }, targetId: null }));

            if (card?.boardLink) {
                navigate(card.boardLink);
            }

            console.log('Card deleted successfully:', data);
        } catch (error) {
            console.error('Error deleting the card:', error);
        }
    };

    return (
        <div className="delete-card">
            <h2>Delete?</h2>

            <div className="group">
                <p className='warning-message'>Deleting this card will remove all actions from the activity feed, and you cannot re-open it. This action cannot be undone.</p>
                <button onClick={() => handleDeleteCard()}>Delete</button>
            </div>
        </div>
    );
};

export default DeleteCard;

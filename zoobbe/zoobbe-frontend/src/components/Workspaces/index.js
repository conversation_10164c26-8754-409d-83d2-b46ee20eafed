import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
// import './scss/index.scss';
// import './scss/indexVirtualized.scss';
import { toSlug } from '../../utils/helpers';
import { config } from '../../config';
import { fetchGuestsWorkspaces } from '../../redux/Slices/workspaceSlice';

const Workspaces = () => {
  const [workspaces, setWorkspaces] = useState([]);
  const [boards, setBoards] = useState([]);
  const [showFormForWorkspace, setShowFormForWorkspace] = useState(null);
  const [boardTitle, setBoardTitle] = useState('');
  // const [isValid, setIsValid] = useState(true);

  useEffect(() => {
    const fetchActionLists = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(config.API_URI + `/api/boards`, {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          credentials: 'include',
        });

        if (!response.ok) {
          // setIsValid(false);
          throw new Error('Failed to fetch boards');
        }

        const data = await response.json();
        setBoards(data);
      } catch (error) {
        console.error('Error fetching boards:', error);
      }
    };

    fetchActionLists();
  }, []);

  useEffect(() => {
    const fetchWorkspaces = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(config.API_URI + '/api/workspaces/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch workspaces');
        }

        const data = await response.json();

        // Set the state with the fetched data if it returns successfully
        setWorkspaces(data);
      } catch (error) {
        console.error('Error fetching workspaces:', error);
      }
    };

    fetchWorkspaces();
  }, []);

  const handleAddButton = (workspaceId) => {
    setShowFormForWorkspace(prevWorkspaceId => prevWorkspaceId === workspaceId ? null : workspaceId);
  };

  const handleAddBoard = async (e, workspaceId, shortId) => {
    e.preventDefault();

    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(config.API_URI + `/api/boards`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include',
        body: JSON.stringify({
          workspaceId,
          shortId,
          title: boardTitle,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add action list');
      }

      const data = await response.json();
      const newBoard = data.board;
      setBoards([...boards, newBoard]);

      // Update the workspace with the new board
      setWorkspaces(prevWorkspaces => prevWorkspaces.map(workspace => {
        if (workspace._id === workspaceId) {
          return { ...workspace, boards: [...workspace.boards, newBoard] };
        }
        return workspace;
      }));

      setBoardTitle('');
      setShowFormForWorkspace(null);
    } catch (error) {
      console.error('Error adding board:', error);
    }
  };

  const handleHideForm = () => {
    setShowFormForWorkspace(null);
  };

  return (
    <div className='zoobbe-workspace-container'>
      <h1>Your Workspaces</h1>
      {workspaces.map(workspace => (
        <div key={workspace._id} className='workspace'>
          <div className='workspace-header'>
            <div className='workspace-icon'>{workspace.name ? workspace.name.charAt(0) : ''}</div>
            <h2>{workspace.name}</h2>
            <div className='workspace-actions'>
              <Link to={'/w/wpdeveloper5186/Boards'} >Boards</Link>
              <Link to={'/w/wpdeveloper5186/Views'} >Views</Link>
              <Link to={'/w/wpdeveloper5186/Members'} >Members</Link>
              <Link to={'/w/wpdeveloper5186/Settings'} >Settings</Link>
              <Link to={'/w/wpdeveloper5186/Upgrade'} >Upgrade</Link>
            </div>
          </div>
          <div className='board-container'>
            {workspace.boards.map(board => (
              <div key={board._id} className='board'>
                <Link to={`/b/${board._id}/${toSlug(board.title)}`} className='board-link'>
                  {board.title}
                </Link>
              </div>
            ))}
          </div>

          {showFormForWorkspace === workspace._id ? (
            <form onSubmit={(e) => handleAddBoard(e, workspace._id, workspace.shortId)} className='board-form'>
              <textarea
                name="add-new-board"
                placeholder="Enter Board Title"
                value={boardTitle}
                onChange={(e) => setBoardTitle(e.target.value)}
              />
              <div className='footer-buttons'>
                <button type="submit" className='add-btn'>Add</button>
                <button type="button" onClick={handleHideForm}>
                  <span className="material-symbols-outlined">
                    close
                  </span>
                </button>
              </div>
            </form>
          ) : (
            <div className='create-button-container'>
              <button onClick={() => handleAddButton(workspace._id)} className='add-board-btn'>
                Create new board
              </button>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

export default Workspaces;

/* Custom styles for ActionListPND component */
.action-list-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.action-list-container {
  /* Make the scrollbar background transparent */
  // transition: all 0.3s ease;
  // background-color: transparent;
  flex: 1;

  /* Ensure the container has proper spacing */
  padding-bottom: 0;
  margin-bottom: 0;

  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--action-list-scrollbar-thumb-background, #222) var(--action-list-scrollbar-track-background, #171b1f);

  /* Webkit scrollbar styling (Chrome, Safari, etc.) */
  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--action-list-scrollbar-thumb-background, #222);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-track {
    background: var(--action-list-scrollbar-track-background, #171b1f);
  }
}

/* Add button at the bottom without sticky position */
.add-new-card-form-button {
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3;
  background-color: var(--actionlist-background-color);
  border-radius: 0 0 12px 12px;
  padding: 8px 8px 6px 8px;
  margin-top: auto;

  /* Add hover effect */
  .card-button-container {
    .add-card-button {
      &:hover {
        background-color: var(--add-card-button-hover-background-color);
      }
    }
  }
}

/* Style for the add card form */
.add-new-card-form {
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 3;
  background-color: var(--actionlist-background-color);
  border-radius: 0 0 12px 12px;
  padding: 8px 8px 6px 8px;
}

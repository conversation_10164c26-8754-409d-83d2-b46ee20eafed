.sbm-share-board-modal {
  background-color: var(--popover-background-color);
  color: var(--white-text-color-alternative);
  padding: 20px;
  border-radius: 8px;
  width: 500px;
}
.sbm-share-board-modal .zoobbe-select {
  width: 140px;
}
.sbm-share-board-modal .sbm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sbm-share-board-modal .sbm-header h3 {
  margin: 0;
}
.sbm-share-board-modal .sbm-header .sbm-close-button {
  background: none;
  border: none;
  color: var(--white-text-color-alternative);
  font-size: 20px;
  cursor: pointer;
}
.sbm-share-board-modal .sbm-input-group {
  display: flex;
  align-items: start;
  margin: 20px 0 15px;
  gap: 10px;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: self-start;
  background: var(--popover-background-color);
  width: calc(100% - 140px);
  padding: 5px;
  border-radius: var(--element-border-radius);
  gap: 5px;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input:focus {
  background-color: var(--ds-background-input, var(--white-text-color-alternative));
  outline: 2px solid var(--outline-color);
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input input {
  flex: 1;
  border-radius: var(--element-border-radius);
  background-color: var(--popover-background-color);
  color: var(--white-text-color-alternative);
  border: none;
  height: 25px;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input input:focus {
  box-shadow: none;
  outline: none;
  border-color: var(--outline-color);
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-selected-email {
  display: inline-flex;
  align-items: center;
  background-color: #2c3338;
  border-radius: var(--element-border-radius);
  padding: 5px;
  color: var(--primary-text-color);
  font-size: 14px;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-selected-email button {
  background: none;
  border: none;
  color: var(--white-text-color-alternative);
  font-size: 12px;
  cursor: pointer;
  display: flex;
  padding-right: 0;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-selected-email button span {
  font-size: 14px;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-search-results {
  position: absolute;
  top: calc(100% + 3px);
  left: 0px;
  background-color: var(--popover-background-color);
  border: 1px solid var(--popover-border-color);
  border-radius: var(--element-border-radius);
  width: calc(100% - 22px);
  z-index: 1000;
  padding: 10px;
  max-height: 500px;
  overflow: auto;
  box-shadow: var(--popover-box-shadow);
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-search-results .no-user-message {
  font-size: 13.5px;
  line-height: 1.6;
  font-family: system-ui;
  padding: 0 8px;
  font-weight: 500;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-search-results .sbm-search-result {
  padding: 10px;
  cursor: pointer;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-search-results .sbm-search-result:hover {
  background-color: #555;
}
.sbm-share-board-modal .sbm-input-group .sbm-email-input .sbm-search-results .sbm-search-result span {
  display: block;
}
.sbm-share-board-modal .sbm-input-group select {
  padding: 10px;
  margin-left: 10px;
  border: 1px solid #555;
  border-radius: var(--element-border-radius);
  background-color: #40444b;
  color: var(--white-text-color-alternative);
  min-height: 42px;
}
.sbm-share-board-modal .sbm-input-group .zoobbe-select-trigger {
  height: 35px;
  font-size: 14px;
  padding: 1px 15px;
}
.sbm-share-board-modal .sbm-input-group .zoobbe-select .zoobbe-select-trigger span.arrow-down {
  top: 6px;
}
.sbm-share-board-modal .sbm-input-group .zoobbe-select .zoobbe-select-option {
  font-size: 14px;
}
.sbm-share-board-modal .sbm-input-group .zoobbe-select-trigger.active {
  color: var(--primary-text-color);
  border: none;
}
.sbm-share-board-modal .sbm-input-group .zoobbe-select .zoobbe-select-option {
  padding: 0px 12px;
}
.sbm-share-board-modal .sbm-invite-button {
  padding: 0px 20px;
  background-color: var(--brand-color);
  border: none;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  font-size: 14px;
  height: 35px;
  margin: 0;
  width: 100%;
  margin-bottom: 20px;
  color: var(--brand-btn-primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}
.sbm-share-board-modal .sbm-message-input {
  width: calc(100% - 20px);
  padding: 10px;
  border-radius: var(--element-border-radius);
  border: 1px solid var(--outline-color);
  background: var(--popover-background-color);
  color: var(--white-text-color-alternative);
  margin-bottom: 20px;
  font-family: system-ui;
}
.sbm-share-board-modal .sbm-message-input:focus-visible {
  outline: 2px solid #388bff;
}
.sbm-share-board-modal .sbm-invite-link {
  display: flex;
  align-items: center;
  color: #cfcfcf;
  font-size: 14px;
  margin-bottom: 15px;
}
.sbm-share-board-modal .sbm-invite-link .invite-link-info {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sbm-share-board-modal .sbm-invite-link .invite-link-icon {
  font-size: 18px;
  color: #cfcfcf;
}
.sbm-share-board-modal .sbm-invite-link .invite-link-text {
  color: #cfcfcf;
}
.sbm-share-board-modal .sbm-invite-link .invite-link-actions {
  display: flex;
  gap: 8px;
}
.sbm-share-board-modal .sbm-invite-link .link-action {
  background: none;
  border: none;
  color: #1a73e8;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  text-decoration: none;
}
.sbm-share-board-modal .sbm-invite-link .link-action:hover {
  text-decoration: underline;
}
.sbm-share-board-modal .sbm-invite-link .sbm-create-link-button {
  background: none;
  border: none;
  color: #1a73e8;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  text-decoration: none;
}
.sbm-share-board-modal .sbm-invite-link .sbm-create-link-button:hover {
  text-decoration: underline;
}
.sbm-share-board-modal .sbm-tabs {
  display: flex;
  margin-bottom: 20px;
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #3d4042;
}
.sbm-share-board-modal .sbm-tabs button {
  background: none;
  border: none;
}
.sbm-share-board-modal .sbm-tabs .sbm-tab {
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: var(--single-card-text-color);
}
.sbm-share-board-modal .sbm-tabs .sbm-tab.active {
  color: #579dff;
  border-bottom: 2px solid #579dff;
}
.sbm-share-board-modal .sbm-workspace-members h4,
.sbm-share-board-modal .sbm-requests h4 {
  margin: 0 0 10px 0;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member,
.sbm-share-board-modal .sbm-requests .sbm-member {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .shared-members-name,
.sbm-share-board-modal .sbm-requests .sbm-member .shared-members-name {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-right: auto;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .shared-members-name span.member-status,
.sbm-share-board-modal .sbm-requests .sbm-member .shared-members-name span.member-status {
  font-size: 12px;
  font-family: system-ui;
  color: rgba(154, 160, 166, 0.5019607843);
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .shared-members-name span.member-username,
.sbm-share-board-modal .sbm-requests .sbm-member .shared-members-name span.member-username {
  font-size: 12px;
  font-family: system-ui;
  color: rgba(154, 160, 166, 0.5019607843);
  font-weight: 600;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .re-invite,
.sbm-share-board-modal .sbm-requests .sbm-member .re-invite {
  cursor: pointer;
  margin-right: 20px;
  display: flex;
  align-items: center;
  transition: 0.3s;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .re-invite:hover span,
.sbm-share-board-modal .sbm-requests .sbm-member .re-invite:hover span {
  color: #5f6467;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .re-invite span,
.sbm-share-board-modal .sbm-requests .sbm-member .re-invite span {
  font-size: 20px;
  transition: 0.3s;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .re-invite.loading,
.sbm-share-board-modal .sbm-requests .sbm-member .re-invite.loading {
  pointer-events: none;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .re-invite.loading span,
.sbm-share-board-modal .sbm-requests .sbm-member .re-invite.loading span {
  color: #5f6467;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .members-options,
.sbm-share-board-modal .sbm-requests .sbm-member .members-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  position: relative;
  border-radius: var(--element-border-radius);
  outline: 1px solid var(--outline-color);
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
  font-family: system-ui;
  font-size: 14px;
  height: 35px;
  color: var(--primary-text-color);
  padding: 0 15px;
  text-transform: capitalize;
  gap: 15px;
  padding-right: 10px;
  min-width: 100px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .members-options span.material-symbols-outlined,
.sbm-share-board-modal .sbm-requests .sbm-member .members-options span.material-symbols-outlined {
  font-size: 20px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .zoobbe-select,
.sbm-share-board-modal .sbm-requests .sbm-member .zoobbe-select {
  margin: 0;
  width: 120px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .zoobbe-select .admin-role,
.sbm-share-board-modal .sbm-requests .sbm-member .zoobbe-select .admin-role {
  margin-right: 10px;
  font-weight: bold;
  margin-bottom: 0;
  width: 120px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .zoobbe-select .admin-role .zoobbe-select-trigger,
.sbm-share-board-modal .sbm-requests .sbm-member .zoobbe-select .admin-role .zoobbe-select-trigger {
  padding: 0px 12px;
  background-color: #292b2f;
  font-weight: 400;
  width: 90px;
  color: #8c8d8e;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .zoobbe-select .admin-role .zoobbe-select-trigger span,
.sbm-share-board-modal .sbm-requests .sbm-member .zoobbe-select .admin-role .zoobbe-select-trigger span {
  right: 10px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .zoobbe-select .admin-role .zoobbe-select-trigger span.arrow-down,
.sbm-share-board-modal .sbm-requests .sbm-member .zoobbe-select .admin-role .zoobbe-select-trigger span.arrow-down {
  top: 8px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .zoobbe-select .admin-role .zoobbe-select-option,
.sbm-share-board-modal .sbm-requests .sbm-member .zoobbe-select .admin-role .zoobbe-select-option {
  padding: 8px 12px;
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  font-family: system-ui;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .members-options.active,
.sbm-share-board-modal .sbm-requests .sbm-member .members-options.active {
  outline: 2px solid var(--brand-color);
  color: var(--brand-color);
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .avatar,
.sbm-share-board-modal .sbm-requests .sbm-member .avatar {
  display: inline-flex;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .sbm-member-image,
.sbm-share-board-modal .sbm-workspace-members .sbm-member .image-placeholder,
.sbm-share-board-modal .sbm-requests .sbm-member .sbm-member-image,
.sbm-share-board-modal .sbm-requests .sbm-member .image-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .sbm-member-name,
.sbm-share-board-modal .sbm-requests .sbm-member .sbm-member-name {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .sbm-member-role,
.sbm-share-board-modal .sbm-requests .sbm-member .sbm-member-role {
  margin-right: 10px;
}
.sbm-share-board-modal .sbm-workspace-members .sbm-member .sbm-member-role-dropdown,
.sbm-share-board-modal .sbm-requests .sbm-member .sbm-member-role-dropdown {
  padding: 5px;
  border: 1px solid #555;
  border-radius: var(--element-border-radius);
  background-color: #40444b;
  color: var(--white-text-color-alternative);
}/*# sourceMappingURL=ShareBoard.css.map */
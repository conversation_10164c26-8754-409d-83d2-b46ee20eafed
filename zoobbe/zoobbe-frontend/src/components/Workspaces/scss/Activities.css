.activities {
  margin-left: -45px;
}
.activities .comment-section {
  margin-left: 0;
}
.activities .activity {
  display: flex;
  align-items: flex-start;
  padding: 10px 0 0 10px;
  border-radius: 8px;
  gap: 10px;
  margin-bottom: 15px;
}
.activities .activity .activity__profile-img {
  border-radius: 50%;
  margin-right: 10px;
  width: 35px;
  height: 35px;
}
.activities .activity .activity__content {
  flex: 1;
}
.activities .activity .activity__content .activity__header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  margin-bottom: 5px;
  color: var(--primary-text-color);
  font-weight: 500;
}
.activities .activity .activity__content .activity__header strong {
  color: var(--single-card-text-color);
}
.activities .activity .activity__content .activity__header .activity__details {
  word-break: break-word;
}
.activities .activity .activity__content .activity__header .activity__details a {
  font-weight: 700;
  text-decoration: none;
  color: var(--brand-color);
}
.activities .activity .activity__content span {
  font-size: 12px;
  color: #a1a1a1;
  font-weight: 500;
}/*# sourceMappingURL=Activities.css.map */
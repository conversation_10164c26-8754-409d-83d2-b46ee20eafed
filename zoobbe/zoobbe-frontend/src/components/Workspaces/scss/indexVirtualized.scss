// html body{
//   overflow: hidden;
// }

// .zoobbe-board > div {
//   height: 100vh;
// }

// .ReactVirtualized__Grid {
//   height: 100vh!important;
// }

// .ReactVirtualized__Grid__innerScrollContainer {
//   max-height: 100vh!important;
//   height: 100vh!important;
//   overflow: hidden;
// }

.zoobbe-workspace-board-container > div {
  height: 100% !important;
  width: 100% !important;
}
.zoobbe-workspace-board-container {
  --card-details-bg: #f0f1f4;
  --card-details-bg: #343b42;

  padding: 20px 0;
  // background-color: #ddd;

  .hidden {
    display: none !important;
  }

  .zoobbe-board {
    // display: flex;
    // flex-direction: row;
    // gap: 20px;
    // overflow-x: auto;
    padding-bottom: 20px;
    // for vertualized

    .zoobbe-action-list {
      // flex: 0 0 280px; // Each column will be 280px wide // for vertualized
      box-sizing: border-box; // Ensure padding and border are included in the element's width
      // background: #040404; // for vertualized
      border-radius: 12px 12px 0 0;
      // padding: 15px 8px; // for vertualized
      // display: flex;
      // flex-direction: column;
      position: relative; // Ensure relative positioning for absolute child elements
      margin-bottom: 20px; // Space between column
      height: max-content;
      border-radius: 12px;
      overflow: hidden;
      height: auto !important;

      .list-header {
        flex: 0 0 auto; // Prevent it from stretching
        margin-bottom: 0px;
      }

      .action-list-inner {
        background: #040404;
        border-radius: 12px;
        // for vertualized
      }

      .action-list-container {
        flex: 1 1 auto;
        overflow-y: auto;
        max-height: calc(100vh - 280px);
        padding-bottom: 0px;
        margin-bottom: 0px;
        // padding: 20px 10px;

        .action-card-list {
          padding: 0 8px 8px;
          padding-bottom: 0px;
        }
      }

      .add-new-card-form {
        flex: 0 0 auto;
        background: #060607;
        padding: 6px 8px 6px 10px;
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 0 0 12px 12px;
        z-index: 2;
      }

      .card-wrapper {
        border: 2px solid transparent;

        &:hover {
          border-color: #017bff;
        }
      }

      .card-pemalink-wrapper {
        text-decoration: none;
        color: inherit;
        margin-bottom: 3px;
        display: block;
        border-radius: 8px;
        position: relative;

        &:first-child {
          margin-top: 5px;
        }
        // &:last-child {
        //   margin-bottom: 5px;
        // }

        .card-pemalink {
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          cursor: pointer;
        }

        &:hover {
          .edit-card {
            display: block;
          }
        }
        &:last-child {
          margin-bottom: 0;
        }

        .edit-card {
          position: absolute;
          z-index: 1;
          right: 5px;
          top: 5px;
          cursor: pointer;
          display: none;

          span {
            color: var(--single-card-text-color);
            font-size: 16px;
            padding: 5px;
            border-radius: 50px;
            transition: 0.3s;

            &:hover {
              background: #060606;
              color: #017bff;
            }
          }
        }
      }
      .card-pemalink-wrapper.edit-mode:hover {
        border-color: transparent;
      }
    }

    // &:first-child {
    //   // margin-left: 20px; // for vertualized
    // }

    // &:last-child {
    //   // margin-right: 20px; // for vertualized
    // }
  }

  .action-list-button-container {
    max-width: 280px;
    margin-left: 10px;
  }
}
.list-header {
  text-align: left;
  margin-bottom: 15px;
  padding: 5px 20px;
  flex: 0 0 auto;
  background: #060607;
  border-radius: 12px 12px 0 0;

  .list-title {
    display: flex;
    align-items: start;
    justify-content: space-between;
    // margin: 8px 0;
    h2 {
      color: var(--single-card-text-color);
      word-break: break-word;
      cursor: text;
    }
  }

  h2 {
    margin: 0;
    font-size: 15px;
    // font-weight: 500;
  }

  p {
    color: var(--single-card-text-color);
    font-size: 12px;
    margin-bottom: 0px;
    margin-top: 5px;
  }
}

.card-pemalink-wrapper {
  .zoobbe-card {
    background: var(--editor-background-color);
    border-radius: 8px;
    padding: 12px;
    // margin-bottom: 15px;
    position: relative;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      span.card-option-icon {
        display: inline-flex;
        cursor: pointer;
        position: absolute;
        right: 13px;
        top: 8px;
        z-index: 1;

        span {
          color: var(--single-card-text-color);
        }
      }
    }

    .avatars {
      display: flex;
      display: flex;
      align-items: center;

      .image-placeholder,
      img {
        margin-right: -5px;
      }
    }

    p {
      margin-bottom: 0px;
      margin-top: 0;
      font-size: 14px;
      line-height: 1.6;
      color: var(--single-card-text-color);
      word-break: break-word;
      font-weight: 600;
      font-family: system-ui;
      max-width: calc(100% - 15px);
    }

    .card-labels {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 10px;
    }

    .label {
      padding: 3px 12px;
      border-radius: 3px;
      color: var(--white-text-color-alternative);
      font-size: 0.75em;
      display: inline-block;
    }

    .zoobbe-card-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .zoobbe-card-front-badges span svg {
        height: 14px;
      }

      .zoobbe-card-front-badges,
      .zoobbe-card-front-badges span.badge-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        gap: 4px;
        span {
          font-size: 12.5px;
          color: var(--single-card-text-color);
          font-weight: 600;
        }
      }

      span.badge-card-checklists.badge-icon {
        background: #6c757d;
        border-radius: 10px;
        padding: 2px 4px;
      }

      span.badge-card-attachments.badge-icon .material-icons {
        transform: rotate(45deg);
      }

      span.badge-card-checklists {
        gap: 5px;

        span {
          font-size: 14px;
        }
      }
      .zoobbe-card-front-badges {
        gap: 10px;
      }
    }
  }
}

.feature-request {
  .label {
    background-color: #28a745;
  }
}

.improvements {
  .label {
    background-color: #ff7f0e;
  }
}

.avatars {
  img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    // border: 1px solid #a39ecf;
  }
}

// Workspaces navbar
.zoobbe-workspace-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #0b66ff0d;
  padding: 10px 20px;
  color: var(--white-text-color-alternative);

  .zoobbe-navbar-left,
  .zoobbe-navbar-right {
    display: flex;
    align-items: center;
    position: relative;
  }

  .zoobbe-workspace-name {
    font-weight: bold;
  }

  .zoobbe-star {
    margin-left: 10px;
    cursor: pointer;
  }

  .zoobbe-workspace {
    margin-left: 20px;
    cursor: pointer;
  }

  .zoobbe-board-button,
  .zoobbe-power-ups,
  .zoobbe-filters,
  .zoobbe-clear,
  .zoobbe-share {
    background-color: #6c757d;
    border: none;
    color: var(--white-text-color-alternative);
    padding: 5px 10px;
    margin-left: 10px;
    cursor: pointer;
    border-radius: 3px;
    height: 28px;
    font-size: 14px;
    font-family: system-ui;
    font-weight: 400;
  }

  .filter-content {
    display: flex;
    align-items: center;
    background: #6c757d;
    border-radius: var(--element-border-radius);

    button {
      margin: 0;
    }

    button.cards-count {
      /* background: none; */
      border: none;
      border-radius: 50px;
      min-width: 15px;
      height: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
    }
  }

  .zoobbe-filters {
    display: flex;
    align-items: center;
    position: relative;
    gap: 5px;
  }

  .zoobbe-members {
    display: flex;
    align-items: center;
    margin-left: 20px;

    .image-placeholder {
      margin-left: -10px;
      border: 2px solid #121728;
      user-select: none;
    }

    img {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin-left: -10px;
      border: 2px solid #121728;
      cursor: pointer;
    }

    .zoobbe-more-members {
      margin-left: 5px;
      cursor: pointer;
    }
  }

  .zoobbe-share {
    margin-left: 10px;
  }
}
// Card details styles
.zoobbe-card-modal-container {
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0 / 87%);
  position: fixed;
  left: 0;
  top: 0;
  overflow: auto; // Allow scrolling on the container
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;

  .hidden {
    display: none !important;
  }

  .card-heading {
    display: flex;
    align-items: start;
    gap: 15px;
    margin-left: -35px;
  }

  textarea.zoobbe-card-title-textarea {
    border-radius: var(--element-border-radius);
    box-shadow: none;
    font-size: 20px;
    font-weight: 600;
    margin: -6px -10px;
    min-height: 24px;
    padding: 6px 10px;
    resize: none;
    width: calc(100% - 10px);
    border: none;
    overflow: hidden;
    font-family: system-ui;
    background-color: #060606;
    margin-bottom: 5px;
    color: var(--single-card-text-color);

    &:focus {
      box-shadow: inset 0 0 0 2px var(--ds-border-focused, #388bff);
      outline: 0;
    }
  }

  .zoobbe-card-details {
    background-color: #060607;
    width: 720px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin: 40px 0;
    height: auto;
    max-width: 100%;
    position: absolute;
    top: 0;
    min-height: 600px;
    padding-left: 60px;
    color: var(--single-card-text-color);

    .close-card-container {
      text-align: right;
      display: flex;
      justify-content: end;
      .close-card {
        padding: 5px;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 0.3s;
        cursor: pointer;
        position: relative;
        top: -10px;
        right: -5px;
        color: var(--single-card-text-color);
        &:hover {
          background: var(--editor-background-color);
        }
      }
    }

    .ql-toolbar.ql-snow + .ql-container.ql-snow,
    .ql-editor {
      min-height: 250px;
      border-radius: 0 0 8px 8px;
      font-size: 16px;
      border: none;
      background-color: var(--editor-background-color);
      margin: 0;
    }
    .ql-editor.ql-blank::before {
      color: var(--single-card-text-color);
    }
    .ql-toolbar.ql-snow {
      padding: 8px;
      border-radius: 8px 8px 0 0;
      border-bottom-color: #faf1f1;
      background-color: var(--editor-toolbar-background-color);
    }
    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
      border-color: #444;
    }
    &-header {
      h2 {
        font-size: 20px;
        margin: 0;
        line-height: 1.4;
      }
    }

    &-status {
      color: var(--single-card-text-color);
      margin-bottom: 25px;

      span {
        font-weight: bold;
      }
    }

    &-body {
      margin-top: 5px;

      .zoobbe-card-details-info {
        display: flex;
        justify-content: space-between;
        gap: 20px;

        .zoobbe-card-details-info-left {
          display: flex;
          flex-direction: column;
          gap: 15px;
          width: calc(100% - 180px);

          .card-details-top {
            display: flex;
            align-items: self-start;
            flex-wrap: wrap;
            gap: 30px;
          }

          h5 {
            padding: 0;
            margin: 0;
            margin-bottom: 15px;
            font-size: 15px;

            color: var(--single-card-text-color);
            display: block;
            font-size: 12px;
            font-weight: 600;
            line-height: 16px;
            line-height: 20px;
            margin: 0 8px 5px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .quill-container {
            height: auto;
            border: 2px solid transparent;
            position: relative;
            width: calc(100% + 4px);
            margin-left: -2px;
            margin-top: 4px;

            // &:focus {
            //   box-shadow: inset 0 0 0 2px var(--ds-border-focused, #388bff);
            //   border-radius: 8px;
            // }
          }
          .editor-focused {
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
            border: 2px solid var(--brand-color);
          }
        }

        .zoobbe-card-details-info-right {
          width: 180px;

          .window-sidebar {
            ul {
              padding: 0;
              margin: 0;
              list-style: none;
            }
            h3 {
              color: var(--single-card-text-color);
              font-size: 12px;
              font-weight: 600;
              line-height: 16px;
              line-height: 20px;
              margin-top: 0;
            }
            button {
              display: flex;
              align-items: center;
              width: 100%;
              margin-top: 8px;
              padding: 10px 10px;
              gap: 6px;
              cursor: pointer;
              font-size: 14px;
              font-family: system-ui;
              font-weight: 500;
              color: var(--single-card-text-color);
              background-color: var(--editor-background-color);
              transition: 0.2s;

              &:hover {
                background-color: #1d1e21;
              }
              .material-icons {
                font-size: 18px;
              }
            }

            button#card-back-delete-card-button {
              background-color: #ff000094;
              &:hover {
                background-color: #ff000072;
              }
            }
          }
          .button-link,
          button {
            background-color: var(--ds-background-neutral, #091e420f);
            border: none;
            border-radius: 3px;
            color: var(--ds-text, #172b4d);
            user-select: none;
          }
        }
      }

      .zoobbe-card-members {
        // span {
        //   margin-right: 5px;
        // }
        .member-list {
          display: inline-flex;
          align-items: center;
          gap: 5px;
          position: relative;
          .avatars {
            display: flex;
            align-items: center;
            position: relative;

            // .image-placeholder {
            //   border: 1px solid #a39ecf;
            // }
          }
          .add-member-to-card {
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: var(--popover-background-color);
            border-radius: 50%;
            cursor: pointer;
            span {
              color: var(--single-card-text-color);
              font-size: 20px;
            }
          }
        }

        img {
          width: 33px;
          height: 33px;
          border-radius: 50%;
        }
      }

      .zoobbe-labels {
        .zoobbe-label {
          background-color: #d1453b;
          color: var(--white-text-color-alternative);
          padding: 2px 5px;
          border-radius: 3px;
          font-size: 0.9em;

          &.zoobbe-bug-fix {
            display: flex;
            position: relative;
            min-height: 35px;
            margin-bottom: 0;
            padding: 0px 12px;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 600;
            line-height: normal;
            align-items: center;
            min-width: 35px;
            border-radius: var(--element-border-radius);
            justify-content: center;
            cursor: pointer;
          }
        }
        .add-labels-to-card {
          display: flex;
          align-items: center;
          width: 35px;
          height: 35px;
          background: var(--popover-background-color);
          justify-content: center;
          border-radius: var(--element-border-radius);
          cursor: pointer;
          transition: 0.2s;
          user-select: none;

          span {
            font-size: 20px;
          }

          &:hover {
            background-color: var(--popover-background-color);
          }
        }
      }

      .zoobbe-notifications {
        .notifications-content {
          display: flex;
          align-items: center;
          gap: 5px;
          background: var(--popover-background-color);
          height: 35px;
          padding: 0 12px;
          border-radius: var(--element-border-radius);
          font-weight: 500;
          cursor: pointer;

          span.material-icons-outlined {
            font-size: 16px;
          }
        }
        .zoobbe-watching {
          font-size: 0.9em;
        }
      }

      .duedate-content {
        display: flex;
        align-items: center;
        gap: 0;

        .duedate {
          display: flex;
          align-items: center;
          gap: 5px;
          background: var(--editor-background-color);
          height: 35px;
          padding: 0 6px 0 8px;
          border-radius: var(--element-border-radius);
          font-weight: 500;
          cursor: pointer;
          font-size: 14px;
          color: var(--single-card-text-color);

          span.completed-text {
            background: green;
            color: var(--white-text-color-alternative);
            padding: 0 6px;
            border-radius: var(--element-border-radius);
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-family: system-ui;
          }
          span.overdue-text {
            background: #ff6b6b;
            color: var(--white-text-color-alternative);
            padding: 0 6px;
            border-radius: var(--element-border-radius);
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-family: system-ui;
          }
        }
      }
    }

    .zoobbe-description,
    .zoobbe-attachments,
    .zoobbe-activity,
    .comment-section {
      .card-description-content {
        cursor: pointer;
        word-break: break-word;
      }
      h3 {
        margin-bottom: 15px;
        position: relative;
        display: flex;
        align-items: center;
        gap: 15px;
        margin-left: -40px;
        font-size: 16px;
        font-weight: 600;
        font-family: system-ui;
        color: var(--single-card-text-color);
      }

      a {
        color: #0079bf;
        text-decoration: none;
        word-break: break-all;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    &-footer {
      margin-top: 20px;
      border-top: 1px solid var(--single-card-text-color);
      padding-top: 10px;
    }
  }
}
/* For Webkit Browsers (Chrome, Safari) */
.action-list-container::-webkit-scrollbar {
  width: 4px; /* width of the entire scrollbar */
}

.action-list-container::-webkit-scrollbar-track {
  background: transparent; /* color of the scrollbar track */
}

.action-list-container::-webkit-scrollbar-thumb {
  background-color: var(--scrolbar-thumb-background-color); /* color of the scrollbar thumb */
  border-radius: 10px; /* roundness of the scrollbar thumb */
}

.action-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--scrolbar-thumb-background-color); /* color of the scrollbar thumb when hovered */
}

/* For Firefox */
.action-list-container {
  scrollbar-width: thin;
  scrollbar-color: transparent;
}

/* For Internet Explorer and Edge */
.action-list-container::-ms-scrollbar {
  width: 4px;
}

.action-list-container::-ms-scrollbar-track {
  background: transparent;
}

.action-list-container::-ms-scrollbar-thumb {
  // background-color: #dddaff;
  border-radius: 10px;
}

.zoobbe-description.currently-editing .quill-container {
  // box-shadow: inset 0 0 0 2px var(--ds-border-focused, #388bff);
  border-radius: 8px;
  // background-color: var(--white-text-color-alternative);
}
.zoobbe-description.currently-editing .ql-toolbar.ql-snow {
  border: none;
  // border-bottom: 1px solid #c5c6f7;
}
// add action list button styles

.action-list-button-container {
  display: flex;
  align-items: self-start;
  justify-content: center;
  flex: 0 0 280px;

  .add-list-button {
    display: flex;
    align-items: center;
    background-color: rgb(6 6 6);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-size: 16px;
    color: var(--white-text-color-alternative);
    width: 100%;
    cursor: pointer;
    transition: background-color 0.3s ease;
    gap: 8px;

    span.material-symbols-outlined {
      font-size: 20px;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.3);
    }

    .plus-icon {
      margin-right: 10px;
      font-size: 20px;
    }
  }
}

.card-button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 10px;

  .add-card-button {
    display: flex;
    align-items: center;
    border: none;
    border-radius: 20px;
    padding: 8px 10px;
    font-size: 14px;
    color: #2d3e50;
    cursor: pointer;
    background: none;
    width: 100%;
    gap: 5px;
    font-size: 15px;
    color: var(--single-card-text-color);

    span.material-symbols-outlined {
      font-size: 18px;
    }

    &:hover {
      background-color: rgb(23 27 32);
      transition: background-color 0.3s ease;
    }

    .plus-icon {
      margin-right: 10px;
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 0;
    }
  }

  .card-icon {
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    span.material-symbols-outlined {
      font-size: 18px;
      margin-right: 5px;
      color: var(--single-card-text-color);
    }

    svg {
      width: 20px;
      height: 20px;
      fill: #2d3e50;
    }
  }
}

// Not found page style

.zoobbe-board-not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 80px);
  p {
    width: 500px;
    line-height: 1.6;
    font-size: 24px;
    color: #ddd;
  }
}
// index.scss

$primary-color: #0079bf;
$secondary-color: #61bd4f;
$text-color: var(--scrolbar-thumb-background-color);
$background-color: #f4f5f7;
$card-background: var(--white-text-color-alternative);
$border-radius: 5px;
$box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

.zoobbe-workspace-container {
  // width: 90%;
  margin: 0 auto;
  padding: 20px;

  h1 {
    color: $text-color;
    font-size: 2em;
    margin-bottom: 20px;
  }
}

.workspace {
  background-color: $card-background;
  padding: 20px;
  border-radius: $border-radius;
  // box-shadow: $box-shadow;
  margin-bottom: 40px;

  .workspace-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .workspace-icon {
      background-color: $primary-color;
      color: var(--white-text-color-alternative);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2em;
      margin-right: 15px;
    }

    h2 {
      flex-grow: 1;
      color: $primary-color;
      font-size: 1.5em;
    }

    .workspace-actions {
      display: flex;
      gap: 10px;

      .action-btn {
        background-color: #e0e0e0;
        border: none;
        padding: 5px 10px;
        border-radius: $border-radius;
        cursor: pointer;
        font-size: 0.9em;

        &:hover {
          background-color: darken(#e0e0e0, 10%);
        }
      }

      .upgrade-btn {
        background-color: #6c5ce7;
        color: var(--white-text-color-alternative);

        &:hover {
          background-color: darken(#6c5ce7, 10%);
        }
      }
    }
  }

  .board-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .board {
      background-color: $background-color;
      border-radius: $border-radius;
      // box-shadow: $box-shadow;
      width: calc(33% - 20px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: center;

      .board-link {
        color: $primary-color;
        text-decoration: none;
        font-size: 1.2em;
        padding: 20px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .add-board-btn {
    background-color: purple;
    color: var(--white-text-color-alternative);
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    margin-top: 15px;

    &:hover {
      background-color: darken(#e0e0e0, 10%);
    }
  }

  .board-form {
    textarea {
      padding: 10px;
      border-radius: $border-radius;
      border: 1px solid #ccc;
      font-size: 1em;
      margin-bottom: 10px;
      resize: none;
    }

    .add-btn {
      background-color: $secondary-color;
      color: var(--white-text-color-alternative);
      border: none;
      padding: 10px;
      border-radius: $border-radius;
      cursor: pointer;
      font-size: 1em;

      &:hover {
        background-color: darken($secondary-color, 10%);
      }
    }
  }
}

.zoobbe-board,
.zoobbe-action-list,
.workspace {
  $form-background: #f9f9f9;
  $form-shadow-color: rgba(0, 0, 0, 0.1);
  $form-border-radius: 5px;

  $form-input-border: #ddd;
  $form-input-focus-border: #017bff;
  $form-input-focus-shadow: rgba(0, 123, 255, 0.5);

  $form-button-bg: var(--brand-color);
  $form-button-color: var(--white-text-color-alternative);
  $form-button-hover-bg: var(--hover-brand-color);
  $form-button-hover-color: var(--popover-background-color);
  $form-button-hover-bg-light: var(--popover-background-color);

  $form-button-svg-fill: $form-button-bg;
  $form-button-svg-hover-fill: $form-button-hover-bg;

  $card-details-background: #060606;

  form {
    display: flex;
    align-items: self-start;
    gap: 10px;
    background-color: #060606;
    padding: 10px;
    border-radius: 5px;
    flex-direction: column;
    height: max-content;
    flex: 0 0 260px;

    textarea {
      padding: 10px;
      border: 2px solid #060606;
      border-radius: 5px;
      font-size: 14px;
      resize: none;
      font-family: system-ui;
      width: calc(100% - 22px);
      height: 20px;
      background-color: var(--popover-background-color);
      color: var(--single-card-text-color);
      resize: none;
      overflow: hidden;
      min-height: 20px;

      &:focus {
        border-color: $form-input-focus-border;
        outline: none;
      }
      .add-new-list {
        font-size: 15px;
        font-weight: 600;
      }
    }

    .footer-buttons {
      display: flex;
      gap: 10px;

      span.material-symbols-outlined {
        color: var(--single-card-text-color);
      }
    }
    button {
      background-color: $form-button-bg;
      color: $form-button-color;
      border: none;
      padding: 5px 15px;
      border-radius: $form-border-radius;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;

      &:hover {
        background-color: $form-button-hover-bg;
        color: $form-button-hover-color;
      }

      &[type="button"] {
        background-color: transparent;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: $form-button-hover-bg-light;
        }

        svg {
          fill: $form-button-svg-fill;
          transition: fill 0.3s;
          width: 20px;

          &:hover {
            fill: $form-button-svg-hover-fill;
          }
        }
      }

      &[type="submit"]:hover {
        background-color: $form-button-hover-bg;
      }
    }
  }
}

.zoobbe-action-list {
  form {
    padding: 0;
    box-shadow: none;
    background: none;
    textarea {
      width: calc(100% - 30px);
      min-height: 36px;
      // height: auto;
      margin: 0;
      padding: 8px 12px;
      overflow: hidden;
      overflow-y: auto;
      border: none;
      border-radius: 8px;
      background-color: var(--ds-surface-raised, var(--popover-background-color));
      box-shadow: var(
        --ds-shadow-raised,
        0px 1px 1px rgba(9, 30, 66, 0.2509803922),
        0px 0px 1px rgba(9, 30, 66, 0.3098039216)
      );
      resize: none;
      overflow-wrap: break-word;
      font-family: system-ui;
      border: 2px solid transparent;
      overflow: hidden;
      resize: none; // Prevent manual resizing
      line-height: 1.4;
      font-weight: 600;
    }
  }
}
.update-list-title,
.update-list-title::placeholder {
  display: inline;
  width: auto;
  background: #060606;
  border: none;
  color: var(--single-card-text-color);
  font-size: 15px;
  font-weight: 600;
  resize: none;
  overflow: hidden;
  border: 2px solid transparent;
  min-height: 20px;
  font-family: system-ui;

  &::placeholder {
    font-family: system-ui;
    color: var(--single-card-text-color);
    font-size: 15px;
    font-weight: 600;
  }
}
.update-list-title {
  border-radius: 6px;
  width: 100%;
  line-height: 1.4;
  &:focus {
    outline: none;
    box-shadow: none;
    border: 2px solid #017bff;
  }
}

span.card-filters-count {
  color: #6c757d;
  font-size: 12px;
  font-family: system-ui;
}

.workspace {
  form {
    max-width: max-content;
    margin-top: 10px;
  }
}

.action-list-option {
  position: relative;
  display: flex;
  align-items: center;

  .action-list-option-icon {
    cursor: pointer;
    display: inline-flex;
    margin-top: 4px;

    span {
      color: var(--single-card-text-color);
    }
  }

  .list-options {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--white-text-color-alternative);
    /* border: 1px solid #ccc; */
    border-radius: var(--element-border-radius);
    box-shadow:  var(--popover-box-shadow);
    overflow: hidden;
    transition:
      max-height 0.3s ease,
      opacity 0.3s ease,
      visibility 0.3s;
    z-index: 10;

    a {
      display: block;
      padding: 10px 15px;
      color: var(--scrolbar-thumb-background-color);
      text-decoration: none;

      &:hover {
        background-color: #f0f0f0;
      }
    }
  }

  &:focus .list-options,
  &:active .list-options,
  .list-options:hover {
    max-height: 200px; // Adjust this value as needed
    opacity: 1;
    visibility: visible;
  }
}

.zoobbe-action-list {
  box-sizing: border-box; // Ensure padding and border are included in the element's width
  border-radius: 12px;
  position: relative; // Ensure relative positioning for absolute child elements
  margin-bottom: 20px; // Space between column
  overflow: hidden;
  height: auto !important;
  will-change: transform; // Optimize for animations
  transform: translateZ(0); // Force GPU acceleration
  backface-visibility: hidden; // Prevent flickering during animations
  perspective: 1000; // Enhance 3D performance

  .list-header {
    flex: 0 0 auto; // Prevent it from stretching
    margin-bottom: 0px;
    z-index: 1; // Ensure header stays above scrolling content
  }

  .action-list-inner {
    background: #040404;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .action-list-container {
    flex: 1 1 auto;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 280px);
    padding-bottom: 0px;
    margin-bottom: 0px;
    will-change: transform; // Optimize for animations

    // Optimize scrollbar appearance
    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--action-list-scrollbar-thumb-background, #222);
      border-radius: 10px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    // Firefox scrollbar (modern browsers)
    @supports (scrollbar-width: thin) {
      scrollbar-width: thin;
      scrollbar-color: var(--action-list-scrollbar-thumb-background, #222) transparent;
    }

    .action-card-list {
      padding: 0 8px 8px;
      padding-bottom: 0px;
      position: relative;
    }
  }

  .add-new-card-form {
    flex: 0 0 auto;
    background: #060607;
    padding: 6px 8px 6px 10px;
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0 0 12px 12px;
    z-index: 2;
  }

  .card-pemalink-wrapper {
    text-decoration: none;
    color: inherit;
    margin-bottom: 3px;
    display: block;
    border-radius: 8px;
    border: 2px solid transparent;
    position: relative;

    &:first-child {
      margin-top: 5px;
    }
    // &:last-child {
    //   margin-bottom: 5px;
    // }

    .card-pemalink {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      cursor: pointer;
    }

    &:hover {
      border-color: #017bff;

      .edit-card {
        display: block;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }

    .edit-card {
      position: absolute;
      z-index: 1;
      right: 5px;
      top: 5px;
      cursor: pointer;
      display: none;

      span {
        color: var(--single-card-text-color);
        font-size: 16px;
        padding: 5px;
        border-radius: 50px;
        transition: 0.3s;

        &:hover {
          background: #060606;
          color: #017bff;
        }
      }
    }
  }
  .card-pemalink-wrapper.edit-mode:hover {
    border-color: transparent;
  }
}

// need for both
div[data-rbd-placeholder-context-id] {
  // background: var(--workspace-background-color);
  border-radius: 8px;
  border: 2px dotted var(--brand-color);
}

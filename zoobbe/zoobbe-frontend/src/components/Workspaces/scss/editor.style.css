span.ql-formats button:hover {
  background: #101415;
}

.zoobbe-card-no-description {
  height: 50px;
  background: var(--single-card-side-action-bg-color);
  padding: 10px 12px;
  cursor: pointer;
  border-radius: var(--editor-border-radius);
  transition: 0.2s;
}
.zoobbe-card-no-description:hover {
  background-color: var(--single-card-action-button-hover-color);
}

.zoobbe-description,
.ql-editor,
.comment-section {
  /* Reset default margins and paddings */
  font-size: 15px;
  border: none;
  line-height: 1.4rem;
  margin-bottom: 15px;
  /* Style headings */
  /* Style paragraphs */
  /* Style links */
  /* Style lists */
  /* Style blockquote */
  /* Style code blocks */
  /* Style tables */
  /* Style forms */
  /* Style buttons */
}
.zoobbe-description h1,
.zoobbe-description h2,
.zoobbe-description h3,
.zoobbe-description h4,
.zoobbe-description h5,
.zoobbe-description h6,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6,
.comment-section h1,
.comment-section h2,
.comment-section h3,
.comment-section h4,
.comment-section h5,
.comment-section h6 {
  font-family: system-ui;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--primary-text-color);
}
.zoobbe-description h1,
.ql-editor h1,
.comment-section h1 {
  font-size: 2.5rem;
  line-height: 1.2;
}
.zoobbe-description h2,
.ql-editor h2,
.comment-section h2 {
  font-size: 2rem;
  line-height: 1.3;
}
.zoobbe-description h3,
.ql-editor h3,
.comment-section h3 {
  font-size: 1.75rem;
  line-height: 1.4;
}
.zoobbe-description h4,
.ql-editor h4,
.comment-section h4 {
  font-size: 1.5rem;
  line-height: 1.5;
}
.zoobbe-description h5,
.ql-editor h5,
.comment-section h5 {
  font-size: 1.25rem;
  line-height: 1.6;
}
.zoobbe-description h6,
.ql-editor h6,
.comment-section h6 {
  font-size: 1rem;
  line-height: 1.7;
}
.zoobbe-description p,
.ql-editor p,
.comment-section p {
  margin-bottom: 10px;
  word-break: break-word;
  font-weight: 500;
  font-size: 15px;
  font-family: system-ui;
  color: var(--single-card-text-color);
  margin-top: 0;
}
.zoobbe-description a,
.ql-editor a,
.comment-section a {
  color: var(--brand-color);
  text-decoration: none;
}
.zoobbe-description a:hover,
.ql-editor a:hover,
.comment-section a:hover {
  text-decoration: underline;
}
.zoobbe-description ul,
.zoobbe-description ol,
.ql-editor ul,
.ql-editor ol,
.comment-section ul,
.comment-section ol {
  margin-bottom: 15px;
  word-break: break-word;
  font-weight: 500;
  font-size: 15px;
  font-family: system-ui;
  color: var(--primary-text-color);
}
.zoobbe-description li,
.ql-editor li,
.comment-section li {
  margin-bottom: 5px;
  line-height: 1.6rem;
}
.zoobbe-description blockquote,
.ql-editor blockquote,
.comment-section blockquote {
  margin: 0;
  padding: 10px 20px;
  background-color: #f9f9f9;
  border-left: 5px solid #ccc;
}
.zoobbe-description pre,
.ql-editor pre,
.comment-section pre {
  background-color: #2b2c2d;
  padding: 10px;
  border: 1px solid rgba(154, 160, 166, 0.0588235294);
  border-radius: var(--element-border-radius);
  overflow-x: auto;
  line-height: 1.6rem;
}
.zoobbe-description code,
.ql-editor code,
.comment-section code {
  font-family: Consolas, Monaco, "Andale Mono", monospace;
  line-height: 1.6rem;
}
.zoobbe-description table,
.ql-editor table,
.comment-section table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}
.zoobbe-description th,
.zoobbe-description td,
.ql-editor th,
.ql-editor td,
.comment-section th,
.comment-section td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}
.zoobbe-description th,
.ql-editor th,
.comment-section th {
  background-color: #f2f2f2;
}
.zoobbe-description input,
.zoobbe-description textarea,
.ql-editor input,
.ql-editor textarea,
.comment-section input,
.comment-section textarea {
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: var(--element-border-radius);
  box-sizing: border-box;
}
.zoobbe-description textarea,
.ql-editor textarea,
.comment-section textarea {
  min-height: 100px;
  resize: vertical;
}
.zoobbe-description button,
.ql-editor button,
.comment-section button {
  padding: 0px 20px;
  background-color: var(--brand-color);
  color: var(--brand-btn-primary-color);
  border: none;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  height: 35px;
}
.zoobbe-description button:hover,
.ql-editor button:hover,
.comment-section button:hover {
  background-color: #0056b3;
}
.zoobbe-description .editor-action-buttons,
.ql-editor .editor-action-buttons,
.comment-section .editor-action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 12px;
}
.zoobbe-description .editor-action-buttons button,
.ql-editor .editor-action-buttons button,
.comment-section .editor-action-buttons button {
  font-weight: 500;
  font-size: 15px;
  transition: 0.3s;
  background-color: var(--single-card-side-action-bg-color);
  color: var(--single-card-text-color);
}
.zoobbe-description .editor-action-buttons button:hover,
.ql-editor .editor-action-buttons button:hover,
.comment-section .editor-action-buttons button:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.zoobbe-description .editor-action-buttons button.cancel-editing-content,
.ql-editor .editor-action-buttons button.cancel-editing-content,
.comment-section .editor-action-buttons button.cancel-editing-content {
  background: none;
  color: var(--single-card-text-color);
}
.zoobbe-description .editor-action-buttons button.cancel-editing-content:hover,
.ql-editor .editor-action-buttons button.cancel-editing-content:hover,
.comment-section .editor-action-buttons button.cancel-editing-content:hover {
  background-color: var(--single-card-action-button-hover-color);
}

.mention-popover {
  background-color: var(--popover-background-color);
  border: 1px solid var(--popover-border-color);
  border-radius: 8px;
  box-shadow: var(--popover-box-shadow);
  z-index: 1000;
  padding: 8px;
}

.mention-suggestion {
  padding: 8px 12px;
  cursor: pointer;
}

.mention-suggestion:hover {
  background-color: #f0f0f0;
}/*# sourceMappingURL=editor.style.css.map */
.notifications-page .notifications-icon {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.notifications-page .notifications-container {
  position: relative;
}
.notifications-page .notifications-container .notifications-icon {
  transition: 0.3s;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.notifications-page .notifications-container .notifications-icon:hover {
  background: var(--notification-background-color);
}
.notifications-page .notifications-container .notifications-icon.active {
  background: var(--notification-background-color);
  color: var(--brand-color);
}
.notifications-page .profile-container .profile-image {
  display: flex;
  align-items: center;
}
.notifications-page .notifications-panel {
  background-color: var(--popover-background-color);
  padding: 16px;
  width: 520px;
  border-radius: 8px;
  color: var(--white-text-color-alternative);
  right: -50px;
  top: 40px;
  z-index: 2;
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
  box-shadow: var(--popover-box-shadow);
  margin: 40px auto;
}
.notifications-page .notifications-panel .spin-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
}
.notifications-page .notifications-panel .header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-direction: column;
  gap: 30px;
}
.notifications-page .notifications-panel .header button {
  background: none;
  border: none;
  color: var(--white-text-color-alternative);
  text-align: right;
  cursor: pointer;
}
.notifications-page .notifications-panel .header h2 {
  font-size: 18px;
  margin: 0;
}
.notifications-page .notifications-panel .header .actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.notifications-page .notifications-panel .header .actions span.show-unread-checkbox {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.notifications-page .notifications-panel .header .actions span.show-unread-checkbox input[type=checkbox] {
  margin-left: 8px;
}
.notifications-page .notifications-panel .header .actions button {
  background: none;
  border: none;
  color: var(--white-text-color-alternative);
  font-size: 14px;
  margin-left: 16px;
  cursor: pointer;
}
.notifications-page .notifications-panel .header .actions .checkbox {
  border: 2px solid #2e3646;
}
.notifications-page .notifications-panel .notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.notifications-page .notifications-panel .notification-list .notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--popover-background-color);
  position: relative;
  margin-bottom: 8px;
  gap: 10px;
  border: 1px solid transparent;
  cursor: pointer;
}
.notifications-page .notifications-panel .notification-list .notification-item:hover {
  border: 1px solid #232a32;
}
.notifications-page .notifications-panel .notification-list .notification-item.unread {
  font-weight: bold;
}
.notifications-page .notifications-panel .notification-list .notification-item a.target-link {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
}
.notifications-page .notifications-panel .notification-list .notification-item .user-avatar {
  width: 40px;
  height: 40px;
}
.notifications-page .notifications-panel .notification-list .notification-item .user-avatar img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content {
  flex-grow: 1;
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .initiator {
  margin-bottom: 5px;
  display: inline-block;
  color: var(--brand-color);
  text-decoration: none;
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .initiator:hover {
  text-decoration: underline;
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .content {
  margin: 0 0 4px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  margin-right: 5px;
  color: var(--primary-text-color);
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .content a {
  color: var(--brand-color);
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .content p {
  display: inline;
  word-break: break-word;
  color: var(--primary-text-color);
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .target-type {
  color: #1a73e8;
  text-decoration: none;
  margin-right: 5px;
  font-size: 14px;
  font-weight: 400;
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .comment-box {
  background-color: #ff4444;
  color: var(--white-text-color-alternative);
  border-radius: var(--element-border-radius);
  padding: 4px 8px;
  margin-top: 4px;
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .comment-box .comment-user {
  font-weight: bold;
}
.notifications-page .notifications-panel .notification-list .notification-item .notification-content .notification-date {
  font-size: 12px;
  color: #888888;
  font-weight: 300;
  font-family: system-ui;
}
.notifications-page .notifications-panel .notification-list .notification-item .unread-indicator {
  width: 10px;
  height: 10px;
  background-color: var(--brand-color);
  border-radius: 50%;
  position: absolute;
  top: 12px;
  right: 12px;
}
.notifications-page .notifications-panel .notification-list li.prev-notification-link {
  text-align: center;
  margin-top: 8px;
}
.notifications-page .notifications-panel .notification-list li.prev-notification-link a {
  padding: 10px 5px;
  text-decoration: none;
  color: var(--white-text-color-alternative);
  border-radius: 5px;
  background: #414244;
  display: block;
  font-weight: 500;
  font-size: 15px;
}/*# sourceMappingURL=Notifications.css.map */
/* Optimized scrolling styles for better performance */

/* Board container optimizations */
.zoobbe-workspace-board-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}

/* Scrollbar styling for the board container */
.zoobbe-workspace-board-container::-webkit-scrollbar {
  height: 8px;
}

.zoobbe-workspace-board-container::-webkit-scrollbar-thumb {
  background-color: var(--primary-text-color);
  border-radius: 10px;
}

.zoobbe-workspace-board-container::-webkit-scrollbar-track {
  background: transparent;
}

/* Firefox scrollbar styling */
@supports (scrollbar-width: thin) {
  .zoobbe-workspace-board-container {
    scrollbar-color: var(--primary-text-color) transparent;
  }
}

/* Optimize board rendering */
.zoobbe-board {
  will-change: transform;
  transform: translateZ(0); /* Force GPU acceleration */
}

/* Ensure smooth scrolling during drag and drop */
.zoobbe-workspace-board-container.scrolling {
  pointer-events: auto; /* Ensure pointer events work during scrolling */
}

/* Improve list item rendering during scrolling */
.zoobbe-action-list {
  transform-style: preserve-3d; /* Improve rendering quality */
  backface-visibility: hidden; /* Prevent flickering */
}

/* Improve scrolling performance */
@supports (overscroll-behavior-x: none) {
  html, body {
    overscroll-behavior-x: none; /* Prevent overscroll effects in browsers that support it */
  }
}

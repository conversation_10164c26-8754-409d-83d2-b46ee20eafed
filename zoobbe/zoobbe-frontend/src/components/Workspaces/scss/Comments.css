.comment-section {
  margin-left: -45px;
}
.comment-section .comment {
  display: flex;
  align-items: flex-start;
  padding: 10px 0 0 10px;
  border-radius: 8px;
  gap: 10px;
}
.comment-section .comment .image-placeholder {
  margin-right: 10px;
}
.comment-section .comment .card-comment-content {
  background-color: var(--single-card-side-action-bg-color);
  padding: 8px 12px;
  border-radius: var(--editor-border-radius);
  margin-top: 5px;
}
.comment-section .comment .write-comment-field {
  cursor: pointer;
  transition: 0.2s;
}
.comment-section .comment .write-comment-field:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.comment-section .comment__profile-img {
  border-radius: 50%;
  margin-right: 10px;
  width: 35px;
  height: 35px;
}
.comment-section .comment .comment-editor-field {
  margin-top: -4px;
}
.comment-section .comment__content {
  flex: 1;
  max-width: 100%;
}
.comment-section .comment__content .comment__header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 15px;
}
.comment-section .comment__content .comment__header span {
  font-size: 12px;
  color: #416068;
  font-family: system-ui;
  font-weight: 700;
}
.comment-section .comment__content .comment__link {
  color: #1e90ff;
  text-decoration: none;
}
.comment-section .comment__content .comment__link:hover {
  text-decoration: underline;
}
.comment-section .comment__content .comment__actions {
  margin-top: 5px;
}
.comment-section .comment__content .comment__actions .comment__action-link {
  font-size: 12px;
  color: #a1a1a1;
  text-decoration: none;
  margin-right: 10px;
}
.comment-section .comment__content .comment__actions .comment__action-link:hover {
  text-decoration: underline;
}

.comment-section .comment__actions {
  display: block;
}

.comment-section .quill-container {
  border-radius: var(--editor-border-radius);
}

.comment-section .ql-toolbar.ql-snow {
  border: none;
}

.comment-section .quill-container .ql-container,
.comment-section .quill-container .ql-container .ql-editor {
  min-height: 80px !important;
  margin: 0;
}

.card-comment-content p {
  padding: 0;
  margin-top: 0;
}
.card-comment-content p:last-child {
  margin: 0;
}/*# sourceMappingURL=Comments.css.map */
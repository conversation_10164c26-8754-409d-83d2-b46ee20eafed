html body {
  overflow-x: hidden;
}

body.modal-open {
  overflow: hidden;
}

.zoobbe-workspace-board-container {
  --card-details-bg: #f0f1f4;
  --card-details-bg: #1d3954;
  margin-top: 20px;
}
.zoobbe-workspace-board-container .hidden {
  display: none !important;
}
.zoobbe-workspace-board-container .zoobbe-board {
  display: flex;
  flex-direction: row;
  gap: 20px;
  overflow-x: auto;
  padding: 0;
  margin: 0;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list {
  flex: 0 0 300px;
  box-sizing: border-box;
  border-radius: 12px 12px 0 0;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  border-radius: 12px;
  will-change: transform;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .action-list-content {
  max-height: calc(100% - 15px);
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .action-list-inner {
  overflow: hidden;
  border-radius: 15px;
  border: 2px solid #060606;
  cursor: move;
  max-height: 100%;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .list-header {
  flex: 0 0 auto;
  margin-bottom: 0px;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .action-list-container {
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
  scrollbar-width: none; /* For Firefox */
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .action-list-container::-webkit-scrollbar {
  display: none; /* For Chrome, Safari, and Edge */
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .action-list-container {
  flex: 1 1 auto;
  overflow-y: auto;
  padding-bottom: 0px;
  margin-bottom: 0px;
  background-color: #060606;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .action-list-container .action-card-list {
  padding: 0 6px 6px;
  padding-bottom: 0px !important;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .add-new-card-form-button {
  flex: 0 0 auto;
  background: #060607;
  padding: 8px 8px 6px 8px;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 0 0 12px 12px;
  z-index: 2;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .add-new-card-form {
  flex: 0 0 auto;
  background: #060607;
  padding: 8px 6px 6px 8px;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 0 0 12px 12px;
  z-index: 2;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper {
  text-decoration: none;
  color: inherit;
  margin-bottom: 3px;
  display: block;
  border-radius: 10px;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper:first-child {
  margin-top: 5px;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper .card-pemalink {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper:hover {
  border-color: var(--focus-outline-color);
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper:hover .edit-card {
  display: block;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper:last-child {
  margin-bottom: 0;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper .edit-card {
  position: absolute;
  z-index: 1;
  right: 5px;
  top: 5px;
  cursor: pointer;
  display: none;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper .edit-card span {
  color: var(--single-card-text-color);
  font-size: 16px;
  padding: 5px;
  border-radius: 50px;
  transition: 0.3s;
  background-color: #171b20;
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper .edit-card span:hover {
  background: var(--single-card-action-button-hover-color);
}
.zoobbe-workspace-board-container .zoobbe-board .zoobbe-action-list .card-pemalink-wrapper.edit-mode:hover {
  border-color: transparent;
}
.zoobbe-workspace-board-container .zoobbe-board:first-child {
  margin-left: 20px;
}

.list-header {
  text-align: left;
  margin-bottom: 15px;
  padding: 5px 20px;
  flex: 0 0 auto;
  background: #060607;
}
.list-header .list-title {
  display: flex;
  align-items: start;
  justify-content: space-between;
}
.list-header .list-title h2 {
  color: var(--single-card-text-color);
  word-break: break-word;
  cursor: text;
}
.list-header h2 {
  margin: 0;
  font-size: 15px;
}
.list-header p {
  color: var(--single-card-text-color);
  font-size: 12px;
  margin-bottom: 0px;
  margin-top: 5px;
}

.card-pemalink-wrapper .zoobbe-card {
  background: #171b1f;
  border-radius: 6px;
  padding: 12px;
  position: relative;
  overflow: hidden;
}
.card-pemalink-wrapper .zoobbe-card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-pemalink-wrapper .zoobbe-card .card-header span.card-option-icon {
  display: inline-flex;
  cursor: pointer;
  position: absolute;
  right: 13px;
  top: 8px;
  z-index: 1;
}
.card-pemalink-wrapper .zoobbe-card .card-header span.card-option-icon span {
  color: var(--single-card-text-color);
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-cover-image {
  margin-bottom: 6px;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-cover-image img.cover-image {
  width: calc(100% + 24px);
  margin-left: -12px;
  margin-top: -12px;
}
.card-pemalink-wrapper .zoobbe-card .avatars {
  display: flex;
  display: flex;
  align-items: center;
}
.card-pemalink-wrapper .zoobbe-card p {
  margin-bottom: 0px;
  margin-top: 0;
  font-size: 14px;
  line-height: 1.4;
  color: var(--single-card-text-color);
  word-break: break-word;
  font-weight: 600;
  font-family: system-ui;
  max-width: calc(100% - 15px);
}
.card-pemalink-wrapper .zoobbe-card .card-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
  width: calc(100% - 15px);
}
.card-pemalink-wrapper .zoobbe-card .label {
  padding: 0px 12px;
  border-radius: 3px;
  color: var(--label-text-color);
  font-size: 0.75em;
  display: inline-block;
  font-family: system-ui;
  min-height: 5px;
  min-width: 20px;
  background-color: #d1453b;
  opacity: 0.75;
  font-weight: var(--label-font-weight);
  height: 20px;
  display: flex;
  align-items: center;
  margin-bottom: 0px;
  color: var(--color-white);
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  flex-wrap: wrap;
  gap: 10px;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer .zoobbe-card-front-badges span svg {
  height: 14px;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer .zoobbe-card-front-badges,
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer .zoobbe-card-front-badges span.badge-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  gap: 4px;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer .zoobbe-card-front-badges span,
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer .zoobbe-card-front-badges span.badge-icon span {
  font-size: 12.5px;
  color: var(--single-card-text-color);
  font-weight: 600;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer span.badge-card-checklists.badge-icon {
  background: #6c757d;
  border-radius: 10px;
  padding: 2px 4px;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer span.badge-card-checklists.badge-icon span {
  color: var(--label-text-color);
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer span.badge-card-attachments.badge-icon .material-icons {
  transform: rotate(45deg);
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer span.badge-card-checklists {
  gap: 5px;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer span.badge-card-checklists span {
  font-size: 14px;
}
.card-pemalink-wrapper .zoobbe-card .zoobbe-card-footer .zoobbe-card-front-badges {
  gap: 10px;
}

.action-card-list > div:first-child .card-pemalink-wrapper {
  margin-top: -5px !important;
}

.action-card-list > div:last-child .card-pemalink-wrapper {
  margin-bottom: 0px !important;
}

.feature-request .label {
  background-color: #28a745;
}

.improvements .label {
  background-color: #ff7f0e;
}

.avatars img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.zoobbe-workspace-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(108, 117, 125, 0.3607843137);
  padding: 10px 20px;
  color: var(--color-white);
  position: relative;
  z-index: 2 !important;
}
.zoobbe-workspace-navbar > * {
  z-index: 1;
}
.zoobbe-workspace-navbar::before {
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: rgba(0, 0, 0, 0.2); /* Semi-transparent overlay */
}
.zoobbe-workspace-navbar .zoobbe-navbar-left,
.zoobbe-workspace-navbar .zoobbe-navbar-right {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0;
  margin: 0;
  list-style: none;
}
.zoobbe-workspace-navbar .zoobbe-navbar-left {
  gap: 20px;
}
.zoobbe-workspace-navbar .zoobbe-navbar-left li.zoobbe-board-visibility {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  border: none;
  padding: 0px 10px;
  border-radius: 3px;
  height: 32px;
  font-family: system-ui;
  transition: 0.1s;
  font-weight: 600;
  font-family: var(--primary-font-family);
}
.zoobbe-workspace-navbar .zoobbe-navbar-left li.zoobbe-board-visibility span.material-symbols-outlined {
  font-size: 16px;
}
.zoobbe-workspace-navbar .zoobbe-navbar-left li.zoobbe-board-visibility.active {
  color: var(--primary-text-color);
  background-color: rgba(11, 102, 255, 0.0509803922);
}
.zoobbe-workspace-navbar .zoobbe-workspace-name {
  font-weight: bold;
}
.zoobbe-workspace-navbar .zoobbe-star {
  margin-left: 10px;
  cursor: pointer;
  display: flex;
}
.zoobbe-workspace-navbar .zoobbe-star span {
  font-size: 16px;
}
.zoobbe-workspace-navbar .zoobbe-workspace {
  margin-left: 20px;
  cursor: pointer;
}
.zoobbe-workspace-navbar .zoobbe-board-button,
.zoobbe-workspace-navbar .zoobbe-power-ups,
.zoobbe-workspace-navbar .zoobbe-filters,
.zoobbe-workspace-navbar .zoobbe-clear,
.zoobbe-workspace-navbar .zoobbe-share {
  background-color: var(--primary-text-color);
  border: none;
  color: var(--primary-color);
  padding: 0px 10px;
  margin-left: 10px;
  cursor: pointer;
  border-radius: 3px;
  height: 32px;
  font-size: 14px;
  font-family: system-ui;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: 0.1s;
  font-weight: 600;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
.zoobbe-workspace-navbar .zoobbe-board-button:hover,
.zoobbe-workspace-navbar .zoobbe-power-ups:hover,
.zoobbe-workspace-navbar .zoobbe-filters:hover,
.zoobbe-workspace-navbar .zoobbe-clear:hover,
.zoobbe-workspace-navbar .zoobbe-share:hover {
  background-color: #f4f9ff;
}
.zoobbe-workspace-navbar .zoobbe-board-button span,
.zoobbe-workspace-navbar .zoobbe-power-ups span,
.zoobbe-workspace-navbar .zoobbe-filters span,
.zoobbe-workspace-navbar .zoobbe-clear span,
.zoobbe-workspace-navbar .zoobbe-share span {
  font-size: 16px;
}
.zoobbe-workspace-navbar .filter-content {
  display: flex;
  align-items: center;
  background: var(--primary-text-color);
  border-radius: var(--element-border-radius);
  gap: 5px;
}
.zoobbe-workspace-navbar .filter-content button {
  margin: 0;
}
.zoobbe-workspace-navbar .filter-content button.cards-count {
  /* background: none; */
  border: none;
  border-radius: 50px;
  min-width: 15px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}
.zoobbe-workspace-navbar .zoobbe-filters {
  display: flex;
  align-items: center;
  position: relative;
  gap: 5px;
}
.zoobbe-workspace-navbar .zoobbe-members {
  display: flex;
  align-items: center;
  margin-left: 20px;
}
.zoobbe-workspace-navbar .zoobbe-members .image-placeholder {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.zoobbe-workspace-navbar .zoobbe-members img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
.zoobbe-workspace-navbar .zoobbe-members .zoobbe-more-members {
  margin-left: 5px;
  cursor: pointer;
}
.zoobbe-workspace-navbar .zoobbe-share {
  margin-left: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.zoobbe-workspace-navbar .zoobbe-share span.material-symbols-outlined {
  font-size: 18px;
}
.zoobbe-workspace-navbar button.zoobbe-board-more {
  display: flex;
  align-items: center;
  background: transparent;
  background: none;
  border: none;
  color: var(--color-white);
  cursor: pointer;
  justify-content: end;
  padding: 0;
  margin-left: 20px;
}

.zoobbe-card-wrapper {
  z-index: 2 !important;
}

.zoobbe-card-modal-container {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.65);
  position: fixed;
  left: 0;
  top: 0;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  align-items: flex-start;
}
.zoobbe-card-modal-container .hidden {
  display: none !important;
}
.zoobbe-card-modal-container .card-heading {
  display: flex;
  align-items: start;
  gap: 15px;
  margin-left: -35px;
}
.zoobbe-card-modal-container .card-heading .textarea-wrapper {
  width: calc(100% - 80px);
  padding: 5px 0;
  border-radius: var(--element-border-radius);
  margin: -6px -10px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.zoobbe-card-modal-container .card-heading .textarea-wrapper textarea.zoobbe-card-title-textarea {
  border-radius: var(--element-border-radius);
  box-shadow: none;
  font-size: 20px;
  font-weight: 600;
  padding: 0px 5px;
  resize: none;
  width: calc(100% - 20px);
  border: none;
  overflow: hidden;
  font-family: system-ui;
  background-color: var(--single-card-background-color);
  color: var(--single-card-text-color);
}
.zoobbe-card-modal-container .card-heading .textarea-wrapper textarea.zoobbe-card-title-textarea:focus {
  outline: 0;
}
.zoobbe-card-modal-container .card-heading .textarea-wrapper.focused {
  outline: 2px solid var(--focus-outline-color);
}
.zoobbe-card-modal-container .zoobbe-card-details {
  background-color: var(--single-card-background-color);
  width: 760px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: auto;
  max-width: 100%;
  position: relative;
  top: 0;
  min-height: 600px;
  color: var(--single-card-text-color);
  margin: 48px;
}
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-card-cover-image {
  position: relative;
  position: relative;
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  max-height: 180px;
}
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-card-cover-image img.cover-image {
  max-height: 180px;
  text-align: center;
  width: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-card-content {
  padding: 20px;
  padding-left: 60px;
  padding-top: 15px;
}
.zoobbe-card-modal-container .zoobbe-card-details .close-card-container {
  text-align: right;
  display: flex;
  justify-content: end;
  position: absolute;
  right: 15px;
  top: 20px;
  z-index: 1;
}
.zoobbe-card-modal-container .zoobbe-card-details .close-card-container .close-card {
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
  cursor: pointer;
  position: relative;
  top: -10px;
  right: -5px;
  color: var(--single-card-text-color);
}
.zoobbe-card-modal-container .zoobbe-card-details .close-card-container .close-card:hover {
  background-color: var(--single-card-side-action-bg-color);
}
.zoobbe-card-modal-container .zoobbe-card-details .ql-toolbar.ql-snow + .ql-container.ql-snow,
.zoobbe-card-modal-container .zoobbe-card-details .ql-editor {
  min-height: 250px;
  border-radius: 0 0 8px 8px;
  font-size: 16px;
  border: none;
  background-color: #101415;
}
.zoobbe-card-modal-container .zoobbe-card-details .ql-editor.ql-blank::before {
  color: var(--single-card-text-color);
  font-style: normal;
  font-family: system-ui;
  font-size: 15px;
}
.zoobbe-card-modal-container .zoobbe-card-details .ql-toolbar.ql-snow {
  padding: 8px;
  border-radius: 8px 8px 0 0;
  border-bottom-color: #faf1f1;
  background-color: #181818;
}
.zoobbe-card-modal-container .zoobbe-card-details .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: #444;
}
.zoobbe-card-modal-container .zoobbe-card-details-header h2 {
  font-size: 20px;
  margin: 0;
  line-height: 1.4;
}
.zoobbe-card-modal-container .zoobbe-card-details-status {
  color: var(--single-card-text-color);
  margin-bottom: 25px;
  margin-top: -5px;
}
.zoobbe-card-modal-container .zoobbe-card-details-status span.actionlist {
  font-weight: bold;
  cursor: pointer;
  margin-left: 5px;
  font-size: 15px;
  text-decoration: underline;
}
.zoobbe-card-modal-container .zoobbe-card-details-status span.actionlist:hover {
  text-decoration: underline;
}
.zoobbe-card-modal-container .zoobbe-card-details-body {
  margin-top: 5px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-left {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: calc(100% - 180px);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-left .card-details-top {
  display: flex;
  align-items: self-start;
  flex-wrap: wrap;
  gap: 20px 30px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-left h5 {
  padding: 0;
  margin: 0;
  margin-bottom: 15px;
  font-size: 15px;
  color: var(--single-card-text-color);
  display: block;
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
  line-height: 20px;
  margin: 0 8px 5px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-left .quill-container {
  height: auto;
  position: relative;
  width: calc(100% + 4px);
  margin-left: -2px;
  margin-top: 4px;
  background-color: #101415;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-left .editor-focused {
  outline: 2px solid var(--brand-color);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right {
  width: 180px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .window-sidebar ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .window-sidebar h3 {
  color: var(--single-card-text-color);
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
  line-height: 20px;
  margin-top: 0;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .window-sidebar button {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 8px;
  padding: 10px 10px;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  font-family: system-ui;
  font-weight: var(--single-card-side-action-font-weight);
  color: var(--single-card-text-color);
  background-color: var(--single-card-side-action-bg-color);
  transition: 0.2s;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .window-sidebar button:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .window-sidebar button .material-symbols-outlined {
  font-size: 18px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .button-link,
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right button {
  background-color: var(--ds-background-neutral, rgba(9, 30, 66, 0.0588235294));
  border: none;
  border-radius: 3px;
  color: var(--ds-text, #172b4d);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-members .member-list {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  position: relative;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-members .member-list .avatars {
  display: flex;
  align-items: center;
  position: relative;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-members .member-list .add-member-to-card {
  width: 35px;
  height: 35px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--single-card-side-action-bg-color);
  border-radius: 50%;
  cursor: pointer;
  transition: 0.2s;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-members .member-list .add-member-to-card:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-members .member-list .add-member-to-card span {
  color: var(--single-card-text-color);
  font-size: 22px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-members img {
  width: 33px;
  height: 33px;
  border-radius: 50%;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-labels .zoobbe-label {
  background-color: #d1453b;
  color: var(--primary-text-color);
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 0.9em;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-labels .zoobbe-label.zoobbe-bug-fix {
  display: flex;
  position: relative;
  min-height: 35px;
  margin-bottom: 0;
  padding: 0px 12px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  align-items: center;
  min-width: 35px;
  border-radius: var(--element-border-radius);
  justify-content: center;
  cursor: pointer;
  opacity: 0.75;
  color: var(--label-text-color);
  font-weight: var(--label-font-weight);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-labels .zoobbe-label.zoobbe-bug-fix:hover {
  opacity: 0.6;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-labels .add-labels-to-card {
  display: flex;
  align-items: center;
  width: 35px;
  height: 35px;
  background-color: var(--single-card-side-action-bg-color);
  justify-content: center;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  transition: 0.2s;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  transition: 0.2s;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-labels .add-labels-to-card:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-labels .add-labels-to-card span {
  font-size: 22px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-notifications .notifications-content {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: var(--single-card-side-action-bg-color);
  height: 35px;
  padding: 0 12px 0 10px;
  border-radius: var(--element-border-radius);
  font-weight: 500;
  cursor: pointer;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-notifications .notifications-content:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-notifications .notifications-content span.material-icons-outlined {
  font-size: 16px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-notifications .notifications-content .watching-checkmark {
  background: #353b40;
  border-radius: 2px;
  margin-left: 5px;
  margin-right: -7px;
  width: 28px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-notifications .notifications-content .watching-checkmark span.material-symbols-outlined {
  font-size: 16px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-notifications .zoobbe-watching {
  font-size: 0.9em;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .duedate-content {
  display: flex;
  align-items: center;
  gap: 8px;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .duedate-content .checkbox-container {
  padding: 0;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .duedate-content .duedate {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: var(--single-card-side-action-bg-color);
  height: 35px;
  padding: 0 6px 0 8px;
  border-radius: var(--element-border-radius);
  font-weight: 500;
  cursor: pointer;
  font-size: 14px;
  color: var(--single-card-text-color);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .duedate-content .duedate:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.zoobbe-card-modal-container .zoobbe-card-details-body .duedate-content .duedate span.completed-text {
  background: green;
  color: var(--label-text-color);
  padding: 0 6px;
  border-radius: var(--element-border-radius);
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-family: system-ui;
}
.zoobbe-card-modal-container .zoobbe-card-details-body .duedate-content .duedate span.overdue-text {
  background: #ff6b6b;
  color: var(--label-text-color);
  padding: 0 6px;
  border-radius: var(--element-border-radius);
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-family: system-ui;
}
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-description .card-description-content,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-attachments .card-description-content,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-activity .card-description-content,
.zoobbe-card-modal-container .zoobbe-card-details .comment-section .card-description-content {
  word-break: break-word;
  line-height: 1.6rem;
}
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-description h3,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-attachments h3,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-activity h3,
.zoobbe-card-modal-container .zoobbe-card-details .comment-section h3 {
  margin-bottom: 15px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
  margin-left: -40px;
  font-size: 16px;
  font-weight: var(--single-section-header-font-weight);
  font-family: system-ui;
  color: var(--single-card-text-color);
}
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-description a,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-attachments a,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-activity a,
.zoobbe-card-modal-container .zoobbe-card-details .comment-section a {
  color: #0079bf;
  text-decoration: none;
  word-break: break-all;
}
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-description a:hover,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-attachments a:hover,
.zoobbe-card-modal-container .zoobbe-card-details .zoobbe-activity a:hover,
.zoobbe-card-modal-container .zoobbe-card-details .comment-section a:hover {
  text-decoration: underline;
}
.zoobbe-card-modal-container .zoobbe-card-details .comment-section h3 {
  margin-left: 5px;
}
.zoobbe-card-modal-container .zoobbe-card-details .description-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.zoobbe-card-modal-container .zoobbe-card-details .description-header .edit-description-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  border: none;
  padding: 0px 14px 0 10px;
  background: var(--single-card-side-action-bg-color);
  margin-left: 10px;
  cursor: pointer;
  color: var(--single-card-text-color);
  border-radius: 3px;
  transition: 0.2s;
  height: 35px;
  font-family: system-ui;
  font-weight: 500;
}
.zoobbe-card-modal-container .zoobbe-card-details .description-header .edit-description-button:hover {
  background-color: var(--single-card-action-button-hover-color);
}
.zoobbe-card-modal-container .zoobbe-card-details .description-header .edit-description-button span {
  color: var(--single-card-text-color);
  font-size: 18px;
}
.zoobbe-card-modal-container .zoobbe-card-details .activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.zoobbe-card-modal-container .zoobbe-card-details .activity-header button {
  font-size: 14px;
  border: none;
  padding: 0px 12px;
  background: var(--single-card-side-action-bg-color);
  margin-left: 10px;
  cursor: pointer;
  color: var(--single-card-text-color);
  border-radius: 3px;
  transition: 0.2s;
  height: 35px;
  font-family: system-ui;
  font-weight: 500;
}
.zoobbe-card-modal-container .zoobbe-card-details .activity-header button:hover {
  background-color: var(--single-card-action-button-hover-color);
}

/* For Webkit Browsers (Chrome, Safari) */
.action-list-container::-webkit-scrollbar {
  width: 4px; /* width of the entire scrollbar */
}

.action-list-container::-webkit-scrollbar-track {
  background: transparent; /* color of the scrollbar track */
}

.action-list-container::-webkit-scrollbar-thumb {
  background-color: var(--scrolbar-thumb-background-color); /* color of the scrollbar thumb */
  border-radius: 10px; /* roundness of the scrollbar thumb */
}

.action-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--scrolbar-thumb-background-color); /* color of the scrollbar thumb when hovered */
}

/* For Firefox */
.action-list-container {
  scrollbar-width: thin;
  scrollbar-color: transparent;
}

/* For Internet Explorer and Edge */
.action-list-container::-ms-scrollbar {
  width: 4px;
}

.action-list-container::-ms-scrollbar-track {
  background: transparent;
}

.action-list-container::-ms-scrollbar-thumb {
  border-radius: 10px;
}

.zoobbe-description.currently-editing .quill-container {
  border-radius: var(--editor-border-radius);
}

.zoobbe-description.currently-editing .ql-toolbar.ql-snow {
  border: none;
}

.action-list-button-container {
  display: flex;
  align-items: self-start;
  justify-content: center;
}
.action-list-button-container .add-list-button {
  display: flex;
  align-items: center;
  background-color: rgb(6, 6, 6);
  border: none;
  border-radius: 15px;
  padding: 0px 15px;
  font-size: 16px;
  color: var(--primary-text-color);
  width: 100%;
  cursor: pointer;
  transition: background-color 0.3s ease;
  gap: 8px;
  height: 45px;
}
.action-list-button-container .add-list-button span.material-symbols-outlined {
  font-size: 20px;
}
.action-list-button-container .add-list-button:hover {
  background-color: #08283b;
}
.action-list-button-container .add-list-button .plus-icon {
  margin-right: 10px;
  font-size: 20px;
}

.card-button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 10px;
}
.card-button-container .add-card-button {
  display: flex;
  align-items: center;
  border: none;
  border-radius: 8px;
  padding: 8px 10px;
  font-size: 14px;
  color: #2d3e50;
  cursor: pointer;
  background: none;
  width: 100%;
  gap: 5px;
  font-size: 15px;
  color: var(--single-card-text-color);
}
.card-button-container .add-card-button span.material-symbols-outlined {
  font-size: 18px;
}
.card-button-container .add-card-button:hover {
  background-color: var(--add-card-button-hover-background-color);
  transition: background-color 0.3s ease;
}
.card-button-container .add-card-button .plus-icon {
  margin-right: 10px;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 0;
}
.card-button-container .card-icon {
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-button-container .card-icon span.material-symbols-outlined {
  font-size: 18px;
  margin-right: 5px;
  color: var(--single-card-text-color);
}
.card-button-container .card-icon svg {
  width: 20px;
  height: 20px;
  fill: #2d3e50;
}

.zoobbe-board-not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 80px);
}
.zoobbe-board-not-found p {
  width: 500px;
  line-height: 1.6;
  font-size: 24px;
  color: #ddd;
}

.zoobbe-workspace-container {
  margin: 0 auto;
  padding: 20px;
}
.zoobbe-workspace-container h1 {
  color: var(--scrolbar-thumb-background-color);
  font-size: 2em;
  margin-bottom: 20px;
}

.workspace {
  background-color: var(--primary-text-color);
  padding: 20px;
  border-radius: 5px;
  margin-bottom: 40px;
}
.workspace .workspace-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.workspace .workspace-header .workspace-icon {
  background-color: #0079bf;
  color: var(--primary-text-color);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
  margin-right: 15px;
}
.workspace .workspace-header h2 {
  flex-grow: 1;
  color: #0079bf;
  font-size: 1.5em;
}
.workspace .workspace-header .workspace-actions {
  display: flex;
  gap: 10px;
}
.workspace .workspace-header .workspace-actions .action-btn {
  background-color: #e0e0e0;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9em;
}
.workspace .workspace-header .workspace-actions .action-btn:hover {
  background-color: #c7c7c7;
}
.workspace .workspace-header .workspace-actions .upgrade-btn {
  background-color: #6c5ce7;
  color: var(--primary-text-color);
}
.workspace .workspace-header .workspace-actions .upgrade-btn:hover {
  background-color: #4430e0;
}
.workspace .board-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.workspace .board-container .board {
  background-color: #f4f5f7;
  border-radius: 5px;
  width: calc(33% - 20px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-align: center;
}
.workspace .board-container .board .board-link {
  color: #0079bf;
  text-decoration: none;
  font-size: 1.2em;
  padding: 20px;
}
.workspace .board-container .board .board-link:hover {
  text-decoration: underline;
}
.workspace .add-board-btn {
  background-color: purple;
  color: var(--primary-text-color);
  border: none;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  margin-top: 15px;
}
.workspace .add-board-btn:hover {
  background-color: #c7c7c7;
}
.workspace .board-form textarea {
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 1em;
  margin-bottom: 10px;
  resize: none;
}
.workspace .board-form .add-btn {
  background-color: #61bd4f;
  color: var(--primary-text-color);
  border: none;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
}
.workspace .board-form .add-btn:hover {
  background-color: #4b9e3b;
}

.zoobbe-board form,
.zoobbe-action-list form,
.workspace form {
  display: flex;
  align-items: self-start;
  gap: 10px;
  background-color: #060606;
  padding: 10px;
  border-radius: 5px;
  flex-direction: column;
  height: -moz-max-content;
  height: max-content;
  flex: 0 0 260px;
}
.zoobbe-board form textarea,
.zoobbe-action-list form textarea,
.workspace form textarea {
  padding: 10px;
  border: 2px solid #060606;
  border-radius: 5px;
  font-size: 14px;
  resize: none;
  font-family: system-ui;
  width: calc(100% - 22px);
  height: 20px;
  background-color: var(--popover-background-color);
  color: var(--single-card-text-color);
  resize: none;
  overflow: hidden;
  min-height: 20px;
}
.zoobbe-board form textarea:focus,
.zoobbe-action-list form textarea:focus,
.workspace form textarea:focus {
  border-color: #017bff;
  outline: none;
}
.zoobbe-board form textarea .add-new-list,
.zoobbe-action-list form textarea .add-new-list,
.workspace form textarea .add-new-list {
  font-size: 15px;
  font-weight: 600;
}
.zoobbe-board form .footer-buttons,
.zoobbe-action-list form .footer-buttons,
.workspace form .footer-buttons {
  display: flex;
  gap: 10px;
}
.zoobbe-board form .footer-buttons span.material-symbols-outlined,
.zoobbe-action-list form .footer-buttons span.material-symbols-outlined,
.workspace form .footer-buttons span.material-symbols-outlined {
  color: var(--single-card-text-color);
}
.zoobbe-board form button,
.zoobbe-action-list form button,
.workspace form button {
  background-color: var(--brand-color);
  color: var(--brand-btn-primary-color);
  border: none;
  padding: 5px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}
.zoobbe-board form button:hover,
.zoobbe-action-list form button:hover,
.workspace form button:hover {
  background-color: #0056b3;
  color: var(--color-white);
}
.zoobbe-board form button[type=button],
.zoobbe-action-list form button[type=button],
.workspace form button[type=button] {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}
.zoobbe-board form button[type=button]:hover,
.zoobbe-action-list form button[type=button]:hover,
.workspace form button[type=button]:hover {
  background-color: var(--popover-background-color);
}
.zoobbe-board form button[type=button] svg,
.zoobbe-action-list form button[type=button] svg,
.workspace form button[type=button] svg {
  fill: var(--brand-color);
  transition: fill 0.3s;
  width: 20px;
}
.zoobbe-board form button[type=button] svg:hover,
.zoobbe-action-list form button[type=button] svg:hover,
.workspace form button[type=button] svg:hover {
  fill: #0056b3;
}
.zoobbe-board form button[type=submit]:hover,
.zoobbe-action-list form button[type=submit]:hover,
.workspace form button[type=submit]:hover {
  background-color: #0056b3;
}

.zoobbe-action-list .action-list-button-wrapper form {
  background: #060606;
  border-radius: 15px;
  width: auto !important;
}
.zoobbe-action-list form {
  padding: 0;
  box-shadow: none;
  background: none;
}
.zoobbe-action-list form textarea {
  width: calc(100% - 30px);
  min-height: 36px;
  margin: 0;
  padding: 8px 12px;
  overflow: hidden;
  overflow-y: auto;
  border: none;
  border-radius: 8px;
  background-color: var(--ds-surface-raised, var(--popover-background-color));
  box-shadow: var(--ds-shadow-raised, 0px 1px 1px rgba(9, 30, 66, 0.2509803922), 0px 0px 1px rgba(9, 30, 66, 0.3098039216));
  resize: none;
  overflow-wrap: break-word;
  font-family: system-ui;
  border: 2px solid transparent;
  overflow: hidden;
  resize: none;
  line-height: 1.4;
  font-weight: 600;
}
.zoobbe-action-list form textarea.add-new-list {
  min-height: 20px !important;
  font-family: system-ui;
}

.update-list-title::-moz-placeholder {
  display: inline;
  width: auto;
  background: #060606;
  border: none;
  color: var(--single-card-text-color);
  font-size: 15px;
  font-weight: 600;
  resize: none;
  overflow: hidden;
  border: 2px solid transparent;
  min-height: 20px;
  font-family: system-ui;
}

.update-list-title,
.update-list-title::placeholder {
  display: inline;
  width: auto;
  background: #060606;
  border: none;
  color: var(--single-card-text-color);
  font-size: 15px;
  font-weight: 600;
  resize: none;
  overflow: hidden;
  border: 2px solid transparent;
  min-height: 20px;
  font-family: system-ui;
}
.update-list-title::-moz-placeholder, .update-list-title::-moz-placeholder::placeholder {
  font-family: system-ui;
  color: var(--single-card-text-color);
  font-size: 15px;
  font-weight: 600;
}
.update-list-title::placeholder,
.update-list-title::placeholder::placeholder {
  font-family: system-ui;
  color: var(--single-card-text-color);
  font-size: 15px;
  font-weight: 600;
}

.update-list-title {
  border-radius: 6px;
  width: 100%;
  line-height: 1.45;
  color: var(--primary-text-color);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
.update-list-title:focus {
  outline: none;
  box-shadow: none;
  border: 2px solid #017bff;
}

span.card-filters-count {
  color: #6c757d;
  font-size: 12px;
  font-family: system-ui;
}

.workspace form {
  max-width: -moz-max-content;
  max-width: max-content;
  margin-top: 10px;
}

.action-list-option {
  position: relative;
  display: flex;
  align-items: center;
}
.action-list-option .action-list-option-icon {
  cursor: pointer;
  display: inline-flex;
  margin-top: 4px;
}
.action-list-option .action-list-option-icon span {
  color: var(--single-card-text-color);
}
.action-list-option .list-options {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--primary-text-color);
  /* border: 1px solid #ccc; */
  border-radius: var(--element-border-radius);
  box-shadow: var(--popover-box-shadow);
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease, visibility 0.3s;
  z-index: 10;
}
.action-list-option .list-options a {
  display: block;
  padding: 10px 15px;
  color: var(--scrolbar-thumb-background-color);
  text-decoration: none;
}
.action-list-option .list-options a:hover {
  background-color: #f0f0f0;
}
.action-list-option:focus .list-options, .action-list-option:active .list-options,
.action-list-option .list-options:hover {
  max-height: 200px;
  opacity: 1;
  visibility: visible;
}

#zoobbe-board .scrolling .card-pemalink-wrapper:hover {
  border-color: transparent;
}
#zoobbe-board .scrolling .card-pemalink-wrapper:hover .edit-card {
  display: none;
}

#zoobbe-board {
  position: relative;
  width: 100%;
  height: calc(100vh - 50px); /* Full viewport height */
  background-image: var(--background-url); /* Use custom CSS property */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 0; /* Place it below all content */
  display: flex;
  flex-direction: column;
  /* Apply the background image dynamically */
  /* Ensure content is not affected by pseudo-elements */
}
#zoobbe-board::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2); /* Semi-transparent overlay */
  z-index: 0; /* Same layer as the background */
}
#zoobbe-board > * {
  position: relative;
  z-index: 1;
}

@media only screen and (max-width: 768px) {
  .zoobbe-card-modal-container .zoobbe-card-details {
    max-width: calc(100% - 60px);
  }
  .zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info {
    flex-direction: column;
  }
  .zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-left {
    width: 100%;
  }
  .zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right {
    width: 100%;
  }
  .zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .window-sidebar ul {
    display: grid;
    width: 100%; /* Make sure the grid spans the full width of the container */
    grid-template-columns: repeat(2, 1fr); /* 3 equal columns */
    gap: 8px;
  }
  .zoobbe-card-modal-container .zoobbe-card-details-body .zoobbe-card-details-info .zoobbe-card-details-info-right .window-sidebar button {
    margin-top: 0;
  }
}/*# sourceMappingURL=index.css.map */
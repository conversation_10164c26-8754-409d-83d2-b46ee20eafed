.notifications-page {
    .notifications-icon {
        cursor: pointer;
        user-select: none;
    }

    .notifications-container {
        position: relative;

        .notifications-icon {
            transition: 0.3s;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;

            &:hover {
                background: var(--notifications-icon-hover-color);
            }
        }

        .notifications-icon.active {
            background: var(--notifications-icon-hover-color);
            color: var(--brand-color);
        }
    }

    .profile-container {
        .profile-image {
            display: flex;
            align-items: center;
        }
    }

    .notifications-panel {
        background-color: var(--notifications-panel-background-color);
        padding: 16px;
        width: 520px;
        border-radius: 8px;
        color: var(--notifications-panel-text-color);
        right: -50px;
        top: 40px;
        z-index: 2;
        -webkit-user-select: none;
        user-select: none;
        box-shadow: var(--popover-box-shadow);
        margin: 40px auto;

        .spin-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 150px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            flex-direction: column;
            gap: 30px;

            button {
                background: none;
                border: none;
                color: var(--notifications-panel-text-color);
                text-align: right;
                cursor: pointer;
            }

            h2 {
                font-size: 18px;
                margin: 0;
            }

            .actions {
                display: flex;
                align-items: center;
                justify-content: space-between;

                span.show-unread-checkbox {
                    display: flex;
                    align-items: center;
                    font-size: 14px;

                    input[type="checkbox"] {
                        margin-left: 8px;
                    }
                }

                button {
                    background: none;
                    border: none;
                    color: var(--notifications-panel-text-color);
                    font-size: 14px;
                    margin-left: 16px;
                    cursor: pointer;
                }

                .checkbox {
                    border: 2px solid var(-checkbox-border-color);
                }
            }
        }

        .notification-list {
            list-style: none;
            padding: 0;
            margin: 0;

            .notification-item {
                display: flex;
                align-items: flex-start;
                padding: 12px;
                border-radius: 8px;
                background-color: var(--notifications-panel-background-color);
                position: relative;
                margin-bottom: 8px;
                gap: 10px;
                border: 1px solid transparent;
                cursor: pointer;

                &:hover {
                    border: 1px solid var(--notification-item-hover-border-color);
                }

                &.unread {
                    font-weight: bold;
                }

                a.target-link {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    z-index: 1;
                    left: 0;
                    top: 0;
                }

                .user-avatar {
                    width: 40px;
                    height: 40px;

                    img {
                        width: 35px;
                        height: 35px;
                        border-radius: 50%;
                        background-color: #fff;
                    }
                }

                .notification-content {
                    flex-grow: 1;

                    .initiator {
                        margin-bottom: 5px;
                        display: inline-block;
                        color: var(--brand-color);
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }
                    }

                    .content {
                        margin: 0 0 4px;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 1.6;
                        margin-right: 5px;
                        color: var(--primary-text-color);

                        a {
                            color: var(--brand-color);
                        }

                        p {
                            display: inline;
                            word-break: break-word;
                            color: var(--primary-text-color);
                        }
                    }

                    .target-type {
                        color: var(--notification-item-unread-color);
                        text-decoration: none;
                        margin-right: 5px;
                        font-size: 14px;
                        font-weight: 400;
                    }

                    .comment-box {
                        background-color: var(--comment-box-background-color);
                        color: var(--white-text-color-alternative);
                        border-radius: var(--element-border-radius);
                        padding: 4px 8px;
                        margin-top: 4px;

                        .comment-user {
                            font-weight: bold;
                        }
                    }

                    .notification-date {
                        font-size: 12px;
                        color: var(--notification-date-color);
                        font-weight: 300;
                        font-family: system-ui;
                    }
                }

                .unread-indicator {
                    width: 10px;
                    height: 10px;
                    background-color: var(--brand-color);
                    border-radius: 50%;
                    position: absolute;
                    top: 12px;
                    right: 12px;
                }
            }

            li.prev-notification-link {
                text-align: center;
                margin-top: 8px;

                a {
                    padding: 10px 5px;
                    text-decoration: none;
                    color: var(--notifications-panel-text-color);
                    border-radius: 5px;
                    background: var(--prev-notification-link-background-color);
                    display: block;
                    font-weight: 500;
                    font-size: 15px;
                }
            }
        }
    }
}

div#boardVisibility {
  margin-top: 10px;
}
div#boardVisibility .change-visibility {
  background-color: var(--popover-background-color);
  color: #e4e4e4;
  border-radius: 8px;
  max-width: 400px;
  padding: 16px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
div#boardVisibility .change-visibility__header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}
div#boardVisibility .change-visibility__header h3 {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  font-family: system-ui;
  margin: 0;
  color: var(--single-card-text-color);
}
div#boardVisibility .change-visibility__options {
  list-style: none;
  margin: 0;
  padding: 0;
}
div#boardVisibility .change-visibility__options .change-visibility__option {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  cursor: pointer;
  transition: background 0.3s;
  gap: 10px;
}
div#boardVisibility .change-visibility__options .change-visibility__option:hover {
  background: #242628;
}
div#boardVisibility .change-visibility__options .change-visibility__option.selected {
  background: rgba(11, 102, 255, 0.0509803922);
  pointer-events: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
div#boardVisibility .change-visibility__options .change-visibility__option.disabled {
  cursor: not-allowed;
  color: #9e9e9e;
}
div#boardVisibility .change-visibility__options .change-visibility__option.disabled:hover {
  background: none;
}
div#boardVisibility .change-visibility__options .change-visibility__option .material-symbols-outlined {
  font-size: 1.2rem;
  color: var(--primary-text-color);
}
div#boardVisibility .change-visibility__options .change-visibility__option .details h3 {
  margin: 0;
  font-size: 0.85em;
  font-weight: 600;
  color: var(--primary-text-color);
}
div#boardVisibility .change-visibility__options .change-visibility__option .details p {
  margin: 5px 0 0;
  font-size: 0.85rem;
  color: rgba(159, 173, 188, 0.7803921569);
  font-weight: 500;
  line-height: 1.4em;
}/*# sourceMappingURL=BoardVisibility.css.map */
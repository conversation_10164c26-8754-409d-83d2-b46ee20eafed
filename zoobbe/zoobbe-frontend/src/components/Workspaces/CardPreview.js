
const CardPreview = () => {


    return (
        // <div
        //     className={`card-pemalink-wrapper`}
        //     ref={cardRef}
        //     style={style}
        // >
        //     <div className="zoobbe-card" style={{ opacity: 0.4 }}>
        //         <div className="card-header">
        //             {labels?.some(label => label.enabled) && (
        //                 <div className="card-labels">
        //                     {labels
        //                         .filter(label => label.enabled)
        //                         .map(label => (
        //                             <span
        //                                 key={label._id}
        //                                 style={{ backgroundColor: label.color }}
        //                                 className={`label ${toSlug(label.text)}-label`}
        //                                 onClick={() => console.log(label.text)}
        //                             >
        //                             </span>
        //                         ))}
        //                 </div>
        //             )}
        //         </div>

        //         <p>{cardTitle}</p>

        //         {shouldRenderFooter && (
        //             <div className="zoobbe-card-footer">
        //                 <div className="zoobbe-card-front-badges">
        //                     {watchers?.length > 0 && watchers.some(watcher => watcher._id === user?.user?._id) && (
        //                         <span className="badge-card-subscribe badge-icon">
        //                             <span className="material-icons-outlined">visibility</span>
        //                         </span>
        //                     )}
        //                     {description && (
        //                         <span className="badge-card-description badge-icon">
        //                             <span className="material-icons-outlined">reorder</span>
        //                         </span>
        //                     )}
        //                     {comments?.length > 0 && (
        //                         <span className="badge-card-comments badge-icon">
        //                             <span className="material-icons-outlined">mode_comment</span>
        //                             <span className="comments-count count-number">{comments.length}</span>
        //                         </span>
        //                     )}
        //                     {checklistsStatus[1] > 0 && (
        //                         <span className="badge-card-checklists badge-icon">
        //                             <span className="material-symbols-outlined">task_alt</span>
        //                             <span className="checklists-count count-number">{checklistsStatus[0]}/{checklistsStatus[1]}</span>
        //                         </span>
        //                     )}
        //                     {attachments?.length > 0 && (
        //                         <span className="badge-card-attachments badge-icon">
        //                             <span className="material-symbols-outlined">attach_file</span>
        //                             <span className="attachment-count count-number">{attachments.length}</span>
        //                         </span>
        //                     )}
        //                 </div>
        //                 {members.length > 0 && (
        //                     <div className="avatars">
        //                         <MemberImage cardId={_id} members={members} size={28} />
        //                     </div>
        //                 )}
        //             </div>
        //         )}
        //     </div>
        //     <div className="edit-card">
        //         <span className="material-symbols-outlined">edit</span>
        //     </div>
        // </div>

        <div>Titlte</div>
    )
}

export default CardPreview;
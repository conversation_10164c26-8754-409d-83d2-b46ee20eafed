import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { addMemberOptimistic, fetchMembers, removeMemberOptimistic, addMember, removeMember } from '../../redux/Slices/memberSlice';
import { fetchActivities } from '../../redux/Slices/activitiesSlice';
import { uploadAttachment } from '../../redux/Slices/attachmentSlice';
import useHandlePopoverClick from '../../hooks/useHandlePopoverClick';
import { config } from '../../config';
import { useNavigate } from 'react-router-dom';
import { removeArchivedCard } from '../../redux/Slices/cardSlice';

const CardDetailsSidebar = ({ cardId, boardId, isArchived, boardLink, setBoard }) => {
    const dispatch = useDispatch();
    const { user } = useSelector((state) => state.user);
    const { boardMembers, cardMembers } = useSelector(state => state.member);

    const [error, setError] = useState(null);
    const [archived, setArchived] = useState(isArchived);
    const [isDeleted, setIsDeleted] = useState(false);
    const [isJoining, setIsJoining] = useState(false);
    const [isLeaving, setIsLeaving] = useState(false);
    const fileInputRef = useRef(null);

    const navigate = useNavigate();

    const handleJoinToCard = async (e, member) => {
        e.preventDefault();
        setIsJoining(true);
        const memberId = member.user._id;

        // Optimistic update
        dispatch(addMemberOptimistic({ cardId, member: member.user }));

        try {
            await dispatch(addMember({ cardId, memberId })).unwrap();
            // dispatch(fetchActivities({ cardId }));
        } catch (error) {
            setError('Failed to add member.');
        } finally {
            setIsJoining(false);
        }
    };

    const handleLeaveToCard = async (e, member) => {
        e.preventDefault();
        setIsLeaving(true);
        const memberId = member.user._id;

        // Optimistic update
        dispatch(removeMemberOptimistic({ cardId, member: member.user }));

        try {
            await dispatch(removeMember({ cardId, memberId })).unwrap();
            // dispatch(fetchActivities({ cardId }));
        } catch (error) {
            setError('Failed to add member.');
        } finally {
            setIsLeaving(false);
        }
    };


    const handleArchiveCard = async (archivedStatus) => {
        setArchived(archivedStatus); // Optimistically update local state

        const token = localStorage.getItem('accessToken');
        try {
            const response = await fetch(config.API_URI + `/api/cards/archive/${cardId}`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                credentials: 'include',
                body: JSON.stringify({ archived: archivedStatus }),
            });

            if (!response.ok) {
                throw new Error('Failed to archive the card');
            }

            const data = await response.json();
            console.log('Card archive status updated:', data);

            setBoard((prevBoard) => ({
                ...prevBoard,
                actionLists: prevBoard.actionLists.map((list) => {
                    if (list._id === data.updatedCard.actionList._id) {
                        const updatedCard = data.updatedCard;

                        const newCards = archivedStatus
                            ? list.cards.filter((card) => card.shortId !== cardId)
                            : [...list.cards];

                        if (!archivedStatus) {
                            const insertIndex = newCards.findIndex((card) => card.order > updatedCard.order);
                            if (insertIndex === -1) {
                                newCards.push(updatedCard);
                            } else {
                                newCards.splice(insertIndex, 0, updatedCard);
                            }
                        }

                        return {
                            ...list,
                            cards: newCards.sort((a, b) => a.order - b.order),
                        };
                    }
                    return list;
                }),
            }));


        } catch (error) {
            setArchived(!archivedStatus); // Revert UI on error
            console.error('Error archiving the card:', error);
        }
    };


    const handleDeleteCard = async () => {
        const token = localStorage.getItem('accessToken');
        try {
            const response = await fetch(config.API_URI + `/api/cards/delete/${cardId}`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error('Failed to delete the card');
            }

            const data = await response.json();
            setIsDeleted(true); // Update local state to reflect card deletion instantly
            navigate(boardLink);
            console.log('Card deleted successfully:', data);
        } catch (error) {
            console.error('Error deleting the card:', error);
        }
    };

    const handleFileUpload = (e) => {
        const file = e.target.files[0];
        if (file) {
            dispatch(uploadAttachment({ file, cardId }));
        }
    };

    const { handlePopoverClick } = useHandlePopoverClick();

    const memberIds = cardMembers?.map(member => member?._id);
    const isUserMember = user && user.user ? memberIds?.includes(user?.user?._id) : false;

    if (isDeleted) return null; // Don't render the sidebar if the card is deleted

    const actionButtons = [
        {
            title: isUserMember ? 'Leave' : 'Join',
            icon: isUserMember ? 'person_remove' : 'person_add',
            text: isJoining
                ? 'Joining'
                : isLeaving
                    ? 'Leaving'
                    : isUserMember
                        ? 'Leave'
                        : 'Join',
            onClick: (e) =>
                isUserMember ? handleLeaveToCard(e, user) : handleJoinToCard(e, user),
            id: 'popover-card-back-toggle-member-button',
            item_class: isJoining || isLeaving ? 'full-deactivated' : ''
        },
        {
            title: 'Members',
            icon: 'people_outline',
            text: 'Members',
            onClick: (e) => handlePopoverClick(e, 'cardMemberesPopover'),
            id: 'popover-card-back-members-button'
        },
        {
            title: 'Labels',
            icon: 'label_outline',
            text: 'Labels',
            onClick: (e) => handlePopoverClick(e, 'addLabels'),
            id: 'popover-card-back-labels-button'
        },
        {
            title: 'Checklist',
            icon: 'checklist',
            text: 'Checklist',
            onClick: (e) => handlePopoverClick(e, 'addChecklist'),
            id: 'popover-card-back-checklist-button'
        },
        {
            title: 'Dates',
            icon: 'hourglass_top',
            text: 'Dates',
            onClick: (e) => handlePopoverClick(e, 'addDueDate'),
            id: 'popover-card-back-dates-button'
        },
        {
            title: 'Attachment',
            icon: 'attach_file',
            text: 'Attachment',
            // onClick: () => fileInputRef.current.click(),
            onClick: (e) => handlePopoverClick(e, 'attachment'),
            id: 'popover-card-back-attachment-button'
        },
        {
            title: 'Cover',
            icon: 'video_label',
            text: 'Cover',
            onClick: (e) => handlePopoverClick(e, 'attachment', { type: 'COVER' }),
            id: 'popover-card-back-cover-button'
        },
        {
            title: 'Move',
            icon: 'arrow_right_alt',
            text: 'Move',
            onClick: (e) => handlePopoverClick(e, 'moveCard'),
            id: 'popover-card-back-move-card-button'
        },
        {
            title: 'Copy',
            icon: 'copy_all',
            text: 'Copy',
            onClick: (e) => handlePopoverClick(e, 'copyCard'),
            id: 'popover-card-back-copy-card-button'
        },
        {
            title: 'Make template',
            icon: 'web_asset',
            text: 'Make template',
            onClick: () => console.log('Make template button clicked'),
            id: 'popover-card-back-make-template-button',
            item_class: 'action-button-item full-deactivated'
        },
        !archived && {
            title: 'Archive',
            icon: 'archive',
            text: 'Archive',
            onClick: () => handleArchiveCard(true),
            id: 'popover-card-back-archive-button'
        },
        archived && {
            title: 'Send to board',
            icon: 'refresh',
            text: 'Send to board',
            onClick: () => handleArchiveCard(false),
            id: 'popover-card-back-send-to-board-button'
        },
        archived && {
            title: 'Delete',
            icon: 'delete_outline',
            text: 'Delete',
            // onClick: () => handleDeleteCard(),
            onClick: (e) => handlePopoverClick(e, 'deleteCard'),

            id: 'popover-card-back-delete-card-button'
        },
        {
            title: 'Share',
            icon: 'share',
            text: 'Share',
            onClick: (e) => handlePopoverClick(e, 'shareCard'),
            id: 'popover-card-back-share-button'
        }
    ].filter(Boolean);

    return (
        <div className="window-sidebar">
            <div className="window-module u-clearfix">
                <h3 className="mod-no-top-margin js-sidebar-add-heading">Add to card</h3>
                <ul className="action-buttons-list">
                    {actionButtons.map((button, index) => (
                        <li key={index} className={`${button.item_class || 'action-button-item'}`}>
                            <button
                                className="button-with-icon"
                                type="button"
                                onClick={button.onClick}
                                title={button.title}
                                id={button.id}
                                data-testid={button.id}
                                data-popover-trigger
                            >
                                <span className="material-symbols-outlined">{button.icon}</span>
                                {button.text}
                            </button>

                            {button.text === 'Attachment' && (
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    style={{ display: 'none' }}
                                    onChange={handleFileUpload}
                                />
                            )}
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default CardDetailsSidebar;

import React, { useState } from 'react';
import './RecentBoards.scss';

const boards = [
    { name: 'wefwe', workspace: 'Zoobbe', image: 'https://via.placeholder.com/40' },
    { name: 'Teest', workspace: 'MY Workspace', image: 'https://via.placeholder.com/40' },
    { name: 'asxs', workspace: 'Zoobbe', image: 'https://via.placeholder.com/40' },
    { name: 'iuifwe', workspace: '<PERSON>bbe', image: 'https://via.placeholder.com/40' },
    { name: 'Task Board', workspace: 'Akash', image: 'https://via.placeholder.com/40' },
    { name: 'Zoobbed', workspace: 'MY Workspace', image: 'https://via.placeholder.com/40' },
    { name: 'casc', workspace: 'Zoobbe', image: 'https://via.placeholder.com/40' },
    { name: 'bj', workspace: 'Zoobbe', image: 'https://via.placeholder.com/40' },
    { name: 'EmbedPress Development', workspace: '<PERSON>bbe', image: 'https://via.placeholder.com/40' },
    { name: 'xCloud Roadmap', workspace: 'Private Workspace', image: 'https://via.placeholder.com/40' },
];

const GlobalSearch = () => {
    const [search, setSearch] = useState('');

    const filteredBoards = boards.filter(board =>
        board.name.toLowerCase().includes(search.toLowerCase())
    );

    return (
        <div className="recent-boards">
            <input
                type="text"
                className="search-input"
                placeholder="Search boards"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
            />
            <ul className="board-list">
                {filteredBoards.map((board, index) => (
                    <li key={index} className="board-item">
                        <img src={board.image} alt={`${board.name} cover`} className="board-image" loading="lazy" />
                        <div className="board-info">
                            <div className="board-name">{board.name}</div>
                            <div className="board-workspace">{board.workspace}</div>
                        </div>
                    </li>
                ))}
            </ul>
            <div className="advanced-search">
                <a href="#">Advanced search</a>
            </div>
        </div>
    );
};

export default GlobalSearch;

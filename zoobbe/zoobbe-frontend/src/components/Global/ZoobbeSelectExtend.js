import React, { useState, useRef } from 'react';
import useOutsideClick from '../../hooks/useOutsideClick';

const ZoobbeSelectExtend = ({ options, defaultSelectedOption, onSelect, className = '' }) => {
    const isValidDefaultIndex =
        typeof defaultSelectedOption === 'number' &&
        defaultSelectedOption >= 0 &&
        defaultSelectedOption < options.length;

    const [selectedOption, setSelectedOption] = useState(
        isValidDefaultIndex ? options[defaultSelectedOption] : options[0] || undefined
    );

    const [isOpen, setIsOpen] = useState(false);
    const selectRef = useRef();

    useOutsideClick(selectRef, () => {
        setIsOpen(false);
    });

    const handleOptionClick = (option) => {
        setSelectedOption(option);
        setIsOpen(false);
        onSelect(option);
    };

    // Group options by workspaceId
    const groupedOptions = options.reduce((acc, option) => {
        if (!option.workspaceId) return acc; // Skip if workspaceId is missing

        const workspaceGroup = acc.find(group => group.workspaceId === option.workspaceId);

        if (workspaceGroup) {
            workspaceGroup.boards.push(option);
        } else {
            acc.push({
                workspaceId: option.workspaceId,
                workspaceName: option.workspaceName,
                boards: [option]
            });
        }

        return acc;
    }, []);

    return (
        <div className={`zoobbe-select ${className}`} ref={selectRef}>
            <div
                className={`zoobbe-select-trigger${isOpen ? ' active' : ''}`}
                onClick={() => setIsOpen(!isOpen)}
            >
                {selectedOption?.label || 'Select an option'}
                <span className="arrow-down">∟</span>
            </div>
            {isOpen && (
                <ul className="zoobbe-select-options">
                    {groupedOptions.map(group => (
                        <li key={group.workspaceId} className="zoobbe-select-group">
                            <label className="zoobbe-select-group-label">
                                {group.workspaceName} {/* Workspace name */}
                            </label>
                            <ul className='board-options'>
                                {group.boards.map(board => (
                                    <li
                                        key={board.value}
                                        className={`zoobbe-select-option${selectedOption?.value === board?.value ? ' selected' : ''}`}
                                        onClick={() => handleOptionClick(board)}
                                    >
                                        <div className="selectable-option">
                                            {board.label} {/* Board title */}
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default ZoobbeSelectExtend;

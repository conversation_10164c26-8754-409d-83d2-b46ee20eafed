import { useEffect, useState } from "react";

import './scss/lightbox.scss';

function Lightbox({ imageUrl, onClose }) {
  const [scale, setScale] = useState(1);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") onClose();
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3));
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 1));

  return (
    <div className="lightbox-overlay" onClick={onClose}>
      <div className="lightbox-content">
        <button className="close-btn" onClick={onClose}>✕</button>
        <img
          src={imageUrl}
          className="lightbox-image"
          style={{ transform: `scale(${scale})` }}
          onClick={(e) => e.stopPropagation()}
        />
        <div className="lightbox-controls">
          <button onClick={zoomIn}>➕ Zoom In</button>
          <button onClick={zoomOut}>➖ Zoom Out</button>
        </div>
      </div>
    </div>
  );
}

export default Lightbox;

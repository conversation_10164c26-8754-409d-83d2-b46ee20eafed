import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { togglePopover } from '../../redux/Slices/popoverSlice';
import useOutsideClick from '../../hooks/useOutsideClick';
import { getContentById } from '../../redux/ContentRegistry/popover';
import usePopoverPositionUpdater from '../../hooks/usePopoverPositionUpdater';
import './Popover.scss';

const PopOver = () => {
    const { isVisible, contentId, position, targetId, props } = useSelector((state) => state.popover);

    const dispatch = useDispatch();
    const popoverRef = useRef();

    useOutsideClick(popoverRef, () => {
        dispatch(togglePopover({ contentId: null, position: { top: 0, left: 0 }, targetId: null }));
    });


    usePopoverPositionUpdater(targetId, popoverRef);


    if (!isVisible) {
        return null;
    }

    const content = getContentById(contentId, props);

    const isClosed = props.isClosed !== undefined ? props.isClosed : true;

    return (
        <div
            className="zoobbe-popover"
            style={{ top: position.top, left: position.left }}
            ref={popoverRef}
            id={contentId}
        >
            {
                isClosed && (
                    <div className="zoobbe-popover__header">
                        <button
                            className="zoobbe-popover__close-btn"
                            onClick={() => {
                                dispatch(togglePopover({ contentId: null, position: { top: 0, left: 0 }, targetId: null }));
                            }}
                        >
                            <svg className="icon-close" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e3e3e3"><path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z" /></svg>
                        </button>
                    </div>
                )
            }

            <div className="zoobbe-popover__content">
                {content}
            </div>
        </div>
    );
};

export default PopOver;

import React, { useEffect } from "react";
import "./scss/Tooltip.css";

/**
 * Tooltip component that adds tooltip functionality to elements with data-tooltip-content attribute
 */
const Tooltip = () => {
    useEffect(() => {
        // Create tooltip element
        const createTooltipElement = (content, position) => {
            const tooltip = document.createElement('div');
            tooltip.className = `tooltip tooltip-${position}`;

            // Support HTML content if needed
            if (content.includes('<') && content.includes('>')) {
                tooltip.innerHTML = content;
            } else {
                tooltip.textContent = content;
            }

            return tooltip;
        };

        // Calculate tooltip position
        const calculatePosition = (element, tooltip, position) => {
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();

            let top, left;

            switch (position) {
                case "top":
                    // Position above the element, centered horizontally
                    top = rect.top - tooltipRect.height - 2;
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                    break;
                case "bottom":
                    // Position below the element, centered horizontally
                    top = rect.bottom + 2;
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                    break;
                case "left":
                    // Position to the left of the element, centered vertically
                    top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                    left = rect.left - tooltipRect.width - 2;
                    break;
                case "right":
                    // Position to the right of the element, centered vertically
                    top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                    left = rect.right + 2;
                    break;
                default:
                    // Default to bottom position
                    top = rect.bottom + 2;
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
            }

            // Adjust for scroll
            top += window.scrollY;
            left += window.scrollX;

            // Ensure tooltip stays within viewport
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Prevent tooltip from going off the right edge
            if (left + tooltipRect.width > viewportWidth - 5) {
                left = viewportWidth - tooltipRect.width - 5;
            }

            // Prevent tooltip from going off the left edge
            if (left < 5) {
                left = 5;
            }

            // Prevent tooltip from going off the bottom edge
            if (top + tooltipRect.height > viewportHeight - 5) {
                top = viewportHeight - tooltipRect.height - 5;
            }

            // Prevent tooltip from going off the top edge
            if (top < 5) {
                top = 5;
            }

            return { top, left };
        };

        // Show tooltip
        const showTooltip = (element) => {
            // Don't create multiple tooltips for the same element
            if (element._tooltip) return;

            const content = element.getAttribute('data-tooltip-content');
            const position = element.getAttribute('data-tooltip-position') || 'bottom';

            // Create tooltip
            const tooltip = createTooltipElement(content, position);

            // Add to DOM but keep invisible for measurement
            tooltip.style.visibility = 'hidden';
            document.body.appendChild(tooltip);

            // Store reference to tooltip
            element._tooltip = tooltip;

            // Position tooltip after it's in the DOM so we can measure it
            const { top, left } = calculatePosition(element, tooltip, position);
            tooltip.style.top = `${top}px`;
            tooltip.style.left = `${left}px`;

            // Make tooltip visible with animation
            requestAnimationFrame(() => {
                tooltip.style.visibility = 'visible';
                tooltip.classList.add('tooltip-visible');
            });
        };

        // Hide tooltip
        const hideTooltip = (element) => {
            if (!element._tooltip) return;

            const tooltip = element._tooltip;

            // Add transition end listener to remove tooltip after animation
            const handleTransitionEnd = () => {
                if (tooltip.parentNode) {
                    document.body.removeChild(tooltip);
                }
                tooltip.removeEventListener('transitionend', handleTransitionEnd);
                element._tooltip = null;
            };

            tooltip.addEventListener('transitionend', handleTransitionEnd);

            // Start hide animation
            tooltip.classList.remove('tooltip-visible');
        };

        // Handle mouseenter
        const handleMouseEnter = (e) => {
            const element = e.currentTarget;

            // Clear any existing timer
            if (element._tooltipTimer) {
                clearTimeout(element._tooltipTimer);
            }

            // Show tooltip after delay
            element._tooltipTimer = setTimeout(() => {
                showTooltip(element);
            }, 150);
        };

        // Handle mouseleave
        const handleMouseLeave = (e) => {
            const element = e.currentTarget;

            // Clear show timer if it exists
            if (element._tooltipTimer) {
                clearTimeout(element._tooltipTimer);
                element._tooltipTimer = null;
            }

            // Hide tooltip
            hideTooltip(element);
        };

        // Set up tooltip elements
        const setupTooltips = () => {
            const elements = document.querySelectorAll('[data-tooltip-content]');

            elements.forEach(element => {
                // Skip if already set up
                if (element._tooltipInitialized) return;

                element.addEventListener('mouseenter', handleMouseEnter);
                element.addEventListener('mouseleave', handleMouseLeave);

                // Mark as initialized
                element._tooltipInitialized = true;
            });

            return elements;
        };

        // Set up initial tooltips
        const tooltipElements = setupTooltips();

        // Set up mutation observer for dynamically added elements
        const observer = new MutationObserver(() => {
            setupTooltips();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Clean up
        return () => {
            observer.disconnect();

            tooltipElements.forEach(element => {
                element.removeEventListener('mouseenter', handleMouseEnter);
                element.removeEventListener('mouseleave', handleMouseLeave);

                // Remove any active tooltips
                if (element._tooltip && element._tooltip.parentNode) {
                    document.body.removeChild(element._tooltip);
                }

                // Clear any active timers
                if (element._tooltipTimer) {
                    clearTimeout(element._tooltipTimer);
                }

                // Clean up properties
                delete element._tooltip;
                delete element._tooltipTimer;
                delete element._tooltipInitialized;
            });
        };
    }, []);

    // This component doesn't render anything itself
    return null;
};

export default Tooltip;

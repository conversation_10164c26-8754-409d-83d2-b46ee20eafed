import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { showSnackBar, hideSnackBar } from '../../redux/Slices/snackbarSlice';

const NetworkStatus = () => {
    const dispatch = useDispatch();
    
    const checkConnectionSpeed = async () => {
        try {
            // Use Navigation Timing API to check page load performance
            const navigation = performance?.getEntriesByType('navigation')[0];
            const connection = navigator?.connection || navigator?.mozConnection || navigator?.webkitConnection;
            
            if (connection?.downlink) {
                // downlink is in Mbps
                if (connection.downlink < 1) {
                    dispatch(showSnackBar({ 
                        message: 'Slow internet connection detected', 
                        type: 'warning'
                    }));
                } else if (connection.downlink > 1 && connection.effectiveType === '4g') {
                    dispatch(hideSnackBar());
                }
            } else if (navigation) {
                // Fallback to checking page load time
                const loadTime = navigation.duration;
                if (loadTime > 3000) { // If page load takes more than 3 seconds
                    dispatch(showSnackBar({ 
                        message: 'Slow internet connection detected', 
                        type: 'warning'
                    }));
                }
            }
        } catch (error) {
            console.error('Error checking connection speed:', error);
        }
    };

    useEffect(() => {
        const handleOnline = () => {
            dispatch(showSnackBar({ 
                message: 'Internet connection restored', 
                type: 'success'
            }));
            setTimeout(() => dispatch(hideSnackBar()), 3000);
            checkConnectionSpeed(); // Check speed when connection is restored
        };

        const handleOffline = () => {
            dispatch(showSnackBar({ 
                message: 'No internet connection', 
                type: 'error'
            }));
        };

        const handleConnectionChange = () => {
            if (navigator.onLine) {
                checkConnectionSpeed();
            }
        };

        // Initial check
        if (navigator.onLine) {
            checkConnectionSpeed();
        }

        // Set up event listeners
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        
        // Listen for connection changes if the API is available
        if (navigator?.connection) {
            navigator.connection.addEventListener('change', handleConnectionChange);
        }

        // Periodically check connection speed while online
        const speedCheckInterval = setInterval(() => {
            if (navigator.onLine) {
                checkConnectionSpeed();
            }
        }, 30000); // Check every 30 seconds

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
            if (navigator?.connection) {
                navigator.connection.removeEventListener('change', handleConnectionChange);
            }
            clearInterval(speedCheckInterval);
        };
    }, [dispatch]);

    return null;
};

export default NetworkStatus;
// components/DynamicPopover.js

import React, { useState } from 'react';

import "./scss/MembersSearchResult.scss";


const MembersSearchResult = ({ members, onClick }) => {

    return (
        <div
            className="members-search-result">
            {members.map((suggestion, index) => {

                return (
                    <div
                        key={index}
                        className={`mention-suggestion ${index === selectedSuggestionIndex ? "selected" : ""}`}
                        onClick={() => handleMentionClick(suggestion)}
                        style={{ display: "flex", alignItems: "center", padding: "5px 10px", cursor: "pointer" }}
                    >
                        {suggestion.profilePicture ? (
                            <img src={suggestion.profilePicture} alt={suggestion.name} style={{ width: "35px", height: "35px", borderRadius: "50%" }} loading="lazy" />
                        ) : (
                            <div style={{ width: "35px", height: "35px", borderRadius: "50%", backgroundColor: "#ccc" }} />
                        )}
                        <div>
                            <div className="suggession-name" style={{ fontWeight: index === selectedSuggestionIndex ? "600" : "600" }}>{suggestion.name}</div>
                            <div className="suggession-handler" >{`@${suggestion.username}`}</div>
                        </div>
                    </div>
                )
            })}
        </div>

    );
};

export default MembersSearchResult;


import React, { useState, useRef } from 'react';
import useOutsideClick from '../../hooks/useOutsideClick';

const SelectBoard = ({ options, onSelect, className = '' }) => {
    const [selectedOption, setSelectedOption] = useState(null);
    const [isOpen, setIsOpen] = useState(false);
    const selectRef = useRef();

    useOutsideClick(selectRef, () => {
        setIsOpen(false);
    });

    const handleOptionClick = (option) => {
        if (!option.isWorkspace && !option.isBoard) { // Only action lists (or selectable options)
            setSelectedOption(option);
            setIsOpen(false);
            onSelect(option); // Notify parent of the selected option
        }
    };

    return (
        <ul className="zoobbe-select-options">
            {options.map((option, index) => (
                <li
                    key={index}
                    className={`zoobbe-select-option${selectedOption?.value === option?.value ? ' selected' : ''}`}
                >
                    <div
                        onClick={() => handleOptionClick(option)}
                        className={option.isWorkspace || option.isBoard ? 'group-label' : 'selectable-option'}
                        style={
                            option.isWorkspace
                                ? { fontWeight: 'bold', pointerEvents: 'none' }
                                : option.isBoard
                                    ? { fontStyle: 'italic', pointerEvents: 'none' }
                                    : {}
                        }
                    >
                        {option.label}
                    </div>
                </li>
            ))}
        </ul>
    );
};

export default SelectBoard;

import React from 'react';
import './scss/Checkbox.scss';

const Checkbox = ({ checked, onChange, label = "" }) => {
    const uniqueId = `checkbox-${Math.random().toString(36).substr(2, 9)}`;

    const Container = label ? 'label' : 'div';

    return (
        <Container className="checkbox-container" htmlFor={uniqueId} onClick={() => onChange(!checked)}>
            <input
                type="checkbox"
                id={uniqueId}
                checked={checked}
                onChange={() => onChange(!checked)}
            />
            <div className={`checkbox ${checked ? 'checked' : ''}`}>
                {checked && (
                    <span className="material-symbols-outlined check-mark">
                        check
                    </span>
                )}
            </div>
            {label && <span className="checkbox-label">{label}</span>}
        </Container>
    );
};

export default Checkbox;

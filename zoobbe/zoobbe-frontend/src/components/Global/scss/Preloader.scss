.zoobbe-preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #1c1e21; // Dark background like Facebook
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out, visibility 0.5s ease-out;

  &.hidden {
    opacity: 0;
    visibility: hidden;
  }

  .zoobbe-preloader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0;
    box-sizing: border-box;
  }

  .zoobbe-preloader-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, calc(-50% - 60px));
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      height: 60px;
      padding: 10px;
    }
  }

  .zoobbe-preloader-from {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #b0b3b8; // Facebook's footer text color
    font-family: Arial, sans-serif;
    font-size: 12px;
    letter-spacing: 0.5px;

    span {
      margin: 2px 0;
    }

    .zoobbe-text {
      font-weight: bold;
      color: #0966FF;
      font-size: 14px;
    }
  }
}

// Light mode support
@media (prefers-color-scheme: light) {
  .zoobbe-preloader {
    background-color: #f0f2f5; // Facebook's light mode background

    .zoobbe-preloader-from {
      color: #65676b; // Facebook's light mode footer text
    }
  }
}

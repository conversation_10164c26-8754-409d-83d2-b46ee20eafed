.zoobbe-header__icons .notifications-icon {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.zoobbe-header__icons .notifications-container {
  position: relative;
}
.zoobbe-header__icons .notifications-container .notifications-icon {
  transition: 0.3s;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #3a3737;
}
.zoobbe-header__icons .notifications-container .notifications-icon .material-symbols-outlined {
  font-size: 20px;
  color: #ccc;
}
.zoobbe-header__icons .notifications-container .notifications-icon .notification-counter {
  position: absolute;
  font-size: 11px;
  left: 20px;
  top: -5px;
  color: var(--color-white);
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  background: red;
  justify-content: center;
  border-radius: 50px;
}
.zoobbe-header__icons .notifications-container .notifications-icon.active {
  background: #3a3737;
}
.zoobbe-header__icons .notifications-container .notifications-icon.active span.material-symbols-outlined {
  color: var(--brand-color);
}
.zoobbe-header__icons .profile-container .profile-image {
  display: flex;
  align-items: center;
}
.zoobbe-header__icons .profile-container .profile-image .image-placeholder {
  border: 5px solid #3a3737;
}
.zoobbe-header__icons .profile-container .profile-image .image-placeholder.active {
  border-color: #233b5b;
}
.zoobbe-header__icons .notifications-panel {
  background-color: var(--popover-background-color);
  padding: 16px;
  width: 320px;
  border-radius: 8px;
  color: var(--white-text-color-alternative);
  position: absolute;
  right: -50px;
  top: 40px;
  z-index: 3;
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
  box-shadow: var(--popover-box-shadow);
  z-index: 4;
}
.zoobbe-header__icons .notifications-panel .spin-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
}
.zoobbe-header__icons .notifications-panel .header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-direction: column;
  gap: 30px;
}
.zoobbe-header__icons .notifications-panel .header button {
  background: none;
  border: none;
  color: var(--white-text-color-alternative);
  text-align: right;
  cursor: pointer;
}
.zoobbe-header__icons .notifications-panel .header h2 {
  font-size: 14px;
  margin: 0;
}
.zoobbe-header__icons .notifications-panel .header .actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.zoobbe-header__icons .notifications-panel .header .actions span.show-unread-checkbox {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.zoobbe-header__icons .notifications-panel .header .actions span.show-unread-checkbox input[type=checkbox] {
  margin-left: 8px;
}
.zoobbe-header__icons .notifications-panel .header .actions button {
  background: none;
  border: none;
  color: var(--white-text-color-alternative);
  font-size: 14px;
  margin-left: 16px;
  cursor: pointer;
}
.zoobbe-header__icons .notifications-panel .header .actions .checkbox {
  border: 2px solid #2e3646;
}
.zoobbe-header__icons .notifications-panel .notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--popover-background-color);
  position: relative;
  margin-bottom: 8px;
  gap: 10px;
  border: 1px solid transparent;
  cursor: pointer;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item:hover {
  border: 1px solid #232a32;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item.unread {
  font-weight: bold;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item a.target-link {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .user-avatar {
  width: 40px;
  height: 40px;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .user-avatar img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content {
  flex-grow: 1;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .initiator {
  margin-bottom: 5px;
  display: inline-block;
  color: var(--brand-color);
  text-decoration: none;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .initiator:hover {
  text-decoration: underline;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .content {
  margin: 0 0 4px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  margin-right: 5px;
  color: var(--primary-text-color);
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .content a {
  color: var(--brand-color);
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .content p {
  display: inline;
  word-break: break-word;
  color: var(--primary-text-color);
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .target-type {
  color: #1a73e8;
  text-decoration: none;
  margin-right: 5px;
  font-size: 14px;
  font-weight: 400;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .comment-box {
  background-color: #ff4444;
  color: var(--white-text-color-alternative);
  border-radius: var(--element-border-radius);
  padding: 4px 8px;
  margin-top: 4px;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .comment-box .comment-user {
  font-weight: bold;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .notification-content .notification-date {
  font-size: 12px;
  color: #888888;
  font-weight: 300;
  font-family: system-ui;
}
.zoobbe-header__icons .notifications-panel .notification-list .notification-item .unread-indicator {
  width: 10px;
  height: 10px;
  background-color: var(--brand-color);
  border-radius: 50%;
  position: absolute;
  top: 12px;
  right: 12px;
}
.zoobbe-header__icons .notifications-panel .notification-list li.prev-notification-link {
  text-align: center;
  margin-top: 8px;
}
.zoobbe-header__icons .notifications-panel .notification-list li.prev-notification-link a {
  padding: 10px 5px;
  text-decoration: none;
  color: var(--white-text-color-alternative);
  border-radius: 5px;
  background: #3a3838;
  display: block;
  font-weight: 500;
  font-size: 15px;
}

.notification-empty-content {
  text-align: center;
  color: #5d5d5d;
  height: 240px;
  align-items: center;
  align-content: center;
  width: 200px;
  justify-content: center;
  margin: auto;
}
.notification-empty-content span.material-symbols-outlined {
  font-size: 72px;
  font-weight: 400;
}
.notification-empty-content h6 {
  padding: 0;
  margin-top: 10px;
  font-size: 15px;
  font-family: system-ui;
  margin-bottom: 0;
}
.notification-empty-content p {
  font-size: 14px;
  margin-bottom: 0;
  margin-top: 8px;
  line-height: 1.4;
  font-weight: 500;
}/*# sourceMappingURL=Notifications.css.map */
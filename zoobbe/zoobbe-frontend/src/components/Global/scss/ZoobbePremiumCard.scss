.zoobbe-premium-card {
    background-color: var(--workspace-icon-background-color);
    border-radius: 8px;
    padding: 16px;
    color: var(--primary-text-color);
    position: relative;
    overflow: hidden;
    font-family:
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Roboto,
        Oxygen,
        Ubuntu,
        Cantarell,
        "Open Sans",
        "Helvetica Neue",
        sans-serif;
    padding-top: 30px;

    .title {
        font-size: .925rem;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--primary-text-color);
        margin-top: 0;
        padding: 0 !important;
    }

    .description {
        font-size: 0.8375rem;
        margin-bottom: 12px;
        line-height: 1.6;
        margin-top: 0;
    }

    .start-trial-link {
        font-size: 0.9375rem;
        color: var(--brand-color);
        text-decoration: none;
        cursor: pointer;
        &:hover {
            text-decoration: underline;
        }
    }

    .icon-container {
        position: absolute;
        top: 8px;
        right: 8px;
        border-radius: 6px;
        padding: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            position: relative;
            z-index: 1;
            color: var(--primary-text-color);
        }

        svg {
            fill: var(--primary-text-color);
        }

        &::after {
            content: "";
            position: absolute;
            z-index: 1;
            right: -30px;
            top: -34px;
            width: 120px;
            height: 80px;
            transform: rotate(140deg);
            border-radius: 12px;
            background-color: var(--premium-shape-color);
        }
    }
}

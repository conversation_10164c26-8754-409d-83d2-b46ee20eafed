.members-search-result .mention-suggestion.selected {
  background-color: #f0f0f0;
}
.members-search-result .suggession-name,
.members-search-result .suggession-handler {
  font-size: 14px;
  line-height: 1.4;
  color: var(--single-card-text-color);
}
.members-search-result .suggession-handler {
  font-size: 12px;
  color: #8b9aaa;
  font-weight: 500;
}
.members-search-result .mention-suggestion.selected {
  background-color: #302d2d;
}
.members-search-result .mention-suggestion {
  transition: 0.2s;
  gap: 5px;
  display: flex;
  align-items: center;
  padding: 12px 10px;
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: 4px;
  gap: 12px;
}
.members-search-result .mention-suggestion:last-child {
  margin: 0;
}
.members-search-result .mention-suggestion:hover {
  background-color: #302d2d;
}

.mention-suggestion {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  cursor: pointer;
}
.mention-suggestion.selected .suggession-name {
  font-weight: 600;
}
.mention-suggestion img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.mention-suggestion .profile-placeholder {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: #ccc;
}
.mention-suggestion .suggession-name {
  font-weight: 600;
}/*# sourceMappingURL=MembersSearchResult.css.map */
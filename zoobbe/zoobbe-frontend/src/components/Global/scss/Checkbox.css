.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  padding: 5px;
}
.checkbox-container input[type=checkbox] {
  display: none;
}

.checkbox {
  width: 14px;
  height: 14px;
  background: #222f39;
  border: 2px solid #222f39;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}
.checkbox svg {
  visibility: hidden;
  color: var(--white-text-color-alternative);
  width: 16px;
  height: 16px;
}
.checkbox span.material-symbols-outlined {
  color: var(--white-text-color-alternative);
  font-size: 16px;
}
.checkbox span.material-symbols-outlined {
  padding: 5px;
  background: rebeccapurple;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background: #222f39;
}
.checkbox.checked svg {
  visibility: visible;
}/*# sourceMappingURL=Checkbox.css.map */
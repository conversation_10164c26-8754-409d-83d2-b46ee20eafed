.member-role-container {
  background-color: var(--popover-background-color);
  border-radius: 8px;
  width: 280px;
  color: var(--white-text-color-alternative);
  font-family: Arial, sans-serif;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  padding: 5px 0;
}
.member-role-container .popover-member-role {
  padding: 10px 15px;
  cursor: pointer;
  color: var(--white-text-color-alternative);
}
.member-role-container .popover-member-role.selected {
  background-color: rgba(11, 102, 255, 0.0509803922);
  border-left: 2px solid var(--brand-color);
  color: var(--brand-color);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.member-role-container .popover-member-role.selected:hover {
  background-color: rgba(11, 102, 255, 0.0509803922);
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
  cursor: inherit;
}
.member-role-container .popover-member-role:hover {
  background-color: #302d2d;
}
.member-role-container .popover-member-role.disabled {
  color: #6e6e6e;
  cursor: not-allowed;
}
.member-role-container .popover-member-role.disabled .option-description {
  font-size: 12px;
  margin-top: 4px;
}
.member-role-container .popover-member-role .option-title {
  display: block;
  font-size: 15px;
}
.member-role-container .popover-member-role .option-description {
  font-size: 13px;
  color: #8c8c8c;
  margin-top: 5px;
  display: inline-block;
}
.member-role-container .popover-member-role .upgrade-badge {
  background-color: #5c2dd5;
  color: var(--white-text-color-alternative);
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  margin-left: 6px;
}
.member-role-container .popover-member-role.popover-option.remove-disabled {
  pointer-events: none;
}
.member-role-container .popover-member-role.popover-option.remove-disabled span {
  color: #5f6467;
}
.member-role-container .remove-member {
  font-size: 14px;
  color: var(--white-text-color-alternative);
  cursor: pointer;
}
.member-role-container .remove-member:hover {
  background-color: #302d2d;
}/*# sourceMappingURL=MemberRole.css.map */
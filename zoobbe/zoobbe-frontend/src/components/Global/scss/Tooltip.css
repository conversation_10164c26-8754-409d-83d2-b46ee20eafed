/* Trello-like tooltip styles */

.tooltip {
    position: absolute;
    background: var(--scrolbar-thumb-background-color);
    color: var(--primary-text-color);
    padding: 6px 12px;
    border-radius: 3px;
    font-size: 14px;
    z-index: 9999;
    white-space: nowrap;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24);
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    outline: none;

    /* Hidden by default */
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: opacity 0.1s ease-out,
                visibility 0.1s ease-out,
                transform 0.1s ease-out;
    will-change: opacity, transform;
}

.tooltip-visible {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Position-specific styles with directional animations */
.tooltip-top {
    transform: translateY(5px); /* Animate from bottom to top */
}

.tooltip-top.tooltip-visible {
    transform: translateY(0);
}

.tooltip-bottom {
    transform: translateY(-5px); /* Animate from top to bottom */
}

.tooltip-bottom.tooltip-visible {
    transform: translateY(0);
}

.tooltip-left {
    transform: translateX(5px); /* Animate from right to left */
}

.tooltip-left.tooltip-visible {
    transform: translateX(0);
}

.tooltip-right {
    transform: translateX(-5px); /* Animate from left to right */
}

.tooltip-right.tooltip-visible {
    transform: translateX(0);
}
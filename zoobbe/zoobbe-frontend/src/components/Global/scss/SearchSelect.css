.search-select {
  position: relative;
  width: 100%;
  cursor: pointer;
}
.search-select.focused .search-select-trigger {
  outline: 2px solid var(--brand-color);
}
.search-select .search-select-trigger {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #cfd2d6;
  font-family: system-ui;
  justify-content: space-between;
  position: relative;
  background-color: var(--popover-background-color);
  outline: 1px solid var(--secondary-outline-color);
  border-radius: var(--element-border-radius);
  cursor: pointer;
}
.search-select .search-select-trigger input {
  margin-bottom: 0;
  background: transparent;
  border: none;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  width: 100%;
  cursor: pointer;
  outline: none;
}
.search-select .search-select-trigger input::-moz-placeholder {
  color: var(--single-card-text-color);
  font-weight: 700;
}
.search-select .search-select-trigger input::placeholder {
  color: var(--single-card-text-color);
  font-weight: 700;
}
.search-select .search-select-trigger span.material-symbols-outlined {
  background: none;
  font-size: 20px;
  margin-right: 4px;
}
.search-select .search-select-options {
  position: absolute;
  top: calc(100% + 2px);
  left: 0;
  background-color: var(--popover-background-color);
  border-radius: var(--element-border-radius);
  width: 100%;
  z-index: 1000;
  padding: 5px 0;
  max-height: 500px;
  overflow: auto;
  margin-top: 0px;
  max-height: 300px;
  border: 1px solid #302d2d;
}
.search-select .search-select-options .search-select-option {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background 0.2s;
  padding: 8px 10px;
  color: var(--single-card-text-color);
  font-size: 14px;
  line-height: 1.4;
  font-weight: 600;
  border-left: 2px solid transparent;
}
.search-select .search-select-options .search-select-option:hover {
  color: #0b66ff;
  background: rgba(11, 102, 255, 0.0509803922);
  border-left: 2px solid var(--brand-color);
}
.search-select .search-select-options .search-select-option.selected {
  color: #0b66ff;
  border-left: 2px solid var(--brand-color);
}
.search-select .search-select-options .search-select-option .current-option {
  font-size: 12px;
}

li.search-select-group {
  margin-bottom: 10px;
}
li.search-select-group label.search-select-group-label {
  margin-left: 10px;
  text-transform: uppercase;
  font-size: 10px;
  font-weight: normal;
  color: var(--single-card-text-color);
  margin-bottom: 5px;
  display: block;
}
li.search-select-group ul.search-select-option-list {
  padding: 0;
}/*# sourceMappingURL=SearchSelect.css.map */
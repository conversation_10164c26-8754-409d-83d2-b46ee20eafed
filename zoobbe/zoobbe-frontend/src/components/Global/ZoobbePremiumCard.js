import React from 'react';
import './scss/ZoobbePremiumCard.scss';
import { Link } from 'react-router-dom';
import Payments from '../icons/Payments';

const ZoobbePremiumCard = ({ url }) => {
    return (
        <div className="zoobbe-premium-card">
            <h3 className="title">Try Zoobbe Premium</h3>
            <p className="description">
                Unlock advanced features, unlimited boards, and enhanced collaboration tools.
                Get unlimited storage and priority support to take your
                workspace to the next level.
            </p>
            <Link to={url} className="start-trial-link">Upgrade Now</Link>
            {/* <Link href={url} className="start-trial-link">Start free trial</Link> */}
            <div className="icon-container">
                <Payments />
            </div>
        </div>
    );
};

export default ZoobbePremiumCard;

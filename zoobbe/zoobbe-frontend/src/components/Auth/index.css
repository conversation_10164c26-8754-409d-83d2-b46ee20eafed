/* Container */
.login-container,
.reset-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f4f5f7;
  min-height: 100vh;
}

/* Login Box */
.login-box,
.reset-box {
  width: 300px;
  padding: 40px;
  background-color: var(--color-white);
  border-radius: 8px;
  text-align: center;
  margin: 40px 0;
}
.login-box .login-logo,
.login-box .reset-logo,
.reset-box .login-logo,
.reset-box .reset-logo {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  margin-top: 0px;
  color: var(--brand-color);
}
.login-box .login-heading,
.login-box .reset-heading,
.reset-box .login-heading,
.reset-box .reset-heading {
  font-size: 16px;
  margin-bottom: 20px;
  color: #5e6c84;
}
.login-box .login-form .login-input,
.login-box .login-form .reset-input,
.login-box .signup-form .login-input,
.login-box .signup-form .reset-input,
.login-box .reset-form .login-input,
.login-box .reset-form .reset-input,
.reset-box .login-form .login-input,
.reset-box .login-form .reset-input,
.reset-box .signup-form .login-input,
.reset-box .signup-form .reset-input,
.reset-box .reset-form .login-input,
.reset-box .reset-form .reset-input {
  width: calc(100% - 20px);
  padding: 0 10px;
  margin-bottom: 20px;
  border-radius: var(--element-border-radius);
  font-size: 14px;
  height: 35px;
  border: none;
  outline: 1px solid var(--input-border-color);
}
.login-box .login-form .login-input:focus,
.login-box .login-form .reset-input:focus,
.login-box .signup-form .login-input:focus,
.login-box .signup-form .reset-input:focus,
.login-box .reset-form .login-input:focus,
.login-box .reset-form .reset-input:focus,
.reset-box .login-form .login-input:focus,
.reset-box .login-form .reset-input:focus,
.reset-box .signup-form .login-input:focus,
.reset-box .signup-form .reset-input:focus,
.reset-box .reset-form .login-input:focus,
.reset-box .reset-form .reset-input:focus {
  width: calc(100% - 20px);
  padding: 0 10px;
  margin-bottom: 20px;
  outline: 2px solid var(--brand-color);
  border-radius: var(--element-border-radius);
  font-size: 14px;
  height: 35px;
  box-shadow: none;
  border: none;
}
.login-box .login-form .login-button,
.login-box .login-form .reset-button,
.login-box .signup-form .login-button,
.login-box .signup-form .reset-button,
.login-box .reset-form .login-button,
.login-box .reset-form .reset-button,
.reset-box .login-form .login-button,
.reset-box .login-form .reset-button,
.reset-box .signup-form .login-button,
.reset-box .signup-form .reset-button,
.reset-box .reset-form .login-button,
.reset-box .reset-form .reset-button {
  width: 100%;
  padding: 0 10px;
  background-color: var(--brand-color);
  color: var(--brand-btn-primary-color);
  border: none;
  border-radius: var(--element-border-radius);
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 20px;
  height: 35px;
}
.login-box .login-form .login-button:hover,
.login-box .login-form .reset-button:hover,
.login-box .signup-form .login-button:hover,
.login-box .signup-form .reset-button:hover,
.login-box .reset-form .login-button:hover,
.login-box .reset-form .reset-button:hover,
.reset-box .login-form .login-button:hover,
.reset-box .login-form .reset-button:hover,
.reset-box .signup-form .login-button:hover,
.reset-box .signup-form .reset-button:hover,
.reset-box .reset-form .login-button:hover,
.reset-box .reset-form .reset-button:hover {
  background-color: var(--hover-brand-color);
}
.login-box .login-or,
.reset-box .login-or {
  margin: 20px 0;
  color: #5e6c84;
}
.login-box .login-social-buttons,
.reset-box .login-social-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.login-box .login-social-buttons .social-button,
.reset-box .login-social-buttons .social-button {
  width: 100%;
  padding: 10px;
  border: 1px solid #dfe1e6;
  border-radius: var(--element-border-radius);
  font-size: 14px;
  cursor: pointer;
}
.login-box .login-social-buttons .social-button.google, .login-box .login-social-buttons .social-button.microsoft, .login-box .login-social-buttons .social-button.apple, .login-box .login-social-buttons .social-button.slack,
.reset-box .login-social-buttons .social-button.google,
.reset-box .login-social-buttons .social-button.microsoft,
.reset-box .login-social-buttons .social-button.apple,
.reset-box .login-social-buttons .social-button.slack {
  background-color: var(--color-white);
}
.login-box .login-links,
.reset-box .login-links {
  margin: 20px 0;
  color: #5e6c84;
}
.login-box .login-links .login-link,
.reset-box .login-links .login-link {
  color: #0052cc;
  text-decoration: none;
}
.login-box .login-links .login-link:hover,
.reset-box .login-links .login-link:hover {
  text-decoration: underline;
}
.login-box .login-footer,
.reset-box .login-footer {
  margin-top: 20px;
  font-size: 12px;
  color: #5e6c84;
}
.login-box .login-footer .login-atlassian,
.reset-box .login-footer .login-atlassian {
  font-weight: bold;
  margin-bottom: 10px;
}
.login-box .login-footer .login-footer-text,
.reset-box .login-footer .login-footer-text {
  margin-bottom: 10px;
}
.login-box .login-footer .login-privacy,
.reset-box .login-footer .login-privacy {
  margin-bottom: 10px;
}
.login-box .login-footer .login-privacy .login-privacy-link,
.reset-box .login-footer .login-privacy .login-privacy-link {
  color: #0052cc;
  text-decoration: none;
}
.login-box .login-footer .login-privacy .login-privacy-link:hover,
.reset-box .login-footer .login-privacy .login-privacy-link:hover {
  text-decoration: underline;
}
.login-box .login-footer .login-footer-note,
.reset-box .login-footer .login-footer-note {
  font-size: 10px;
}

p.legal-message {
  font-size: 12px;
  text-align: left;
  margin: 0 0 15px 0;
  padding: 0;
}

.success-message {
  color: green;
  margin-top: 0px;
  font-weight: 400;
  font-family: system-ui;
}

.error-message {
  color: red !important;
  margin-top: 10px;
  font-weight: 400;
  font-family: system-ui;
}

span.label {
  display: inline-block;
  text-align: left !important;
  font-weight: 600;
  font-size: 14px;
}

.error-message {
  color: red !important;
  font-size: 12px;
  text-align: left;
  margin: 0 0 15px 0;
  padding: 0;
}/*# sourceMappingURL=index.css.map */
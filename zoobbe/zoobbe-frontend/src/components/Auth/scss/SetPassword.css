.password-form-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f9f9f9;
}
.password-form-container h2 {
  font-size: 1.8rem;
  color: var(--scrolbar-thumb-background-color);
  margin-bottom: 20px;
}
.password-form-container form {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 400px;
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.password-form-container form input {
  font-size: 1rem;
  padding: 10px 15px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
  transition: border-color 0.3s ease;
}
.password-form-container form input:focus {
  outline: none;
  border-color: #007bff;
}
.password-form-container form input[disabled] {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}
.password-form-container form p {
  font-size: 0.9rem;
  color: #d9534f;
  margin-bottom: 10px;
}
.password-form-container form button {
  font-size: 1rem;
  padding: 10px 15px;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.password-form-container form button:hover {
  background-color: #0056b3;
}
.password-form-container form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}/*# sourceMappingURL=SetPassword.css.map */
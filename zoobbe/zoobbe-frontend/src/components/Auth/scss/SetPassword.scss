:root {
    --password-form-container-background-color: #f9f9f9;
    --password-form-heading-color: #6c757d;
    --password-form-background-color: var(--white);
    --password-form-box-shadow-color: rgba(0, 0, 0, 0.1);
    --password-form-input-border-color: #cccccc;
    --password-form-input-focus-border-color: var(--brand-color);
    --password-form-input-disabled-background-color: #f5f5f5;
    --password-form-input-disabled-color: #999999;
    --password-form-error-text-color: #d9534f;
    --password-form-button-text-color: var(--white);
    --password-form-button-background-color: var(--brand-color);
    --password-form-button-hover-background-color: var(--hover-brand-color);
    --password-form-button-disabled-background-color: #cccccc;
}

.password-form-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: var(--password-form-container-background-color);

    h2 {
        font-size: 1.8rem;
        color: var(--password-form-heading-color);
        margin-bottom: 20px;
    }

    form {
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 400px;
        background: var(--password-form-background-color);
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 4px 6px var(--password-form-box-shadow-color);

        input {
            font-size: 1rem;
            padding: 10px 15px;
            margin-bottom: 15px;
            border: 1px solid var(--password-form-input-border-color);
            border-radius: 5px;
            transition: border-color 0.3s ease;

            &:focus {
                outline: none;
                border-color: var(--password-form-input-focus-border-color);
            }

            &[disabled] {
                background-color: var(--password-form-input-disabled-background-color);
                color: var(--password-form-input-disabled-color);
                cursor: not-allowed;
            }
        }

        p {
            font-size: 0.9rem;
            color: var(--password-form-error-text-color);
            margin-bottom: 10px;
        }

        button {
            font-size: 1rem;
            padding: 10px 15px;
            color: var(--password-form-button-text-color);
            background-color: var(--password-form-button-background-color);
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;

            &:hover {
                background-color: var(--password-form-button-hover-background-color);
            }

            &:disabled {
                background-color: var(--password-form-button-disabled-background-color);
                cursor: not-allowed;
            }
        }
    }
}

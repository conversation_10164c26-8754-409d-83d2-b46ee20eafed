.banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 16px;
  margin: 16px auto;
  max-width: 600px;
  background-color: #171b20;
  border: 1px solid #171b20;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 45px;
  font-size: 14px;
}
.banner p {
  flex: 1 1;
  margin: 0;
  color: #f87168;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  display: flex;
  align-items: center;
  gap: 5px;
}
.banner p span {
  font-size: 20px;
}
.banner .banner-password-link {
  color: var(--brand-color);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

@media (max-width: 480px) {
  .banner {
    flex-direction: column;
    align-items: flex-start;
  }
  .banner p {
    margin-bottom: 12px;
    font-size: 14px;
  }
  .banner button {
    width: 100%;
    text-align: center;
  }
}/*# sourceMappingURL=SetPasswordBanner.css.map */
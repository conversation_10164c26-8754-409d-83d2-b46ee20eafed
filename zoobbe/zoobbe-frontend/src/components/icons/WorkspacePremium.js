const WorkspacePremium = ({ size = 24, color = "#0966ff", className="", tooltipContent="", tooltipPosition="" }) => {
    const tooltipProps = tooltipContent ? {
        'data-tooltip-content': tooltipContent,
        'data-tooltip-position': tooltipPosition
    } : {};

    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            height={`${size}px`}
            width={`${size}px`}
            viewBox="0 -960 960 960"
            fill={color}
            {...tooltipProps}
            className={className}
            
        >
            <path d="M387.5-414 422-528l-92-70h114l36-116 36 116h114l-92.5 70L572-414l-92-71.5-92.5 71.5ZM247-52v-301.5q-37-42-57.5-94.5T169-559q0-129.5 90.75-220.25T480-870q129.5 0 220.25 90.75T791-559q0 58.5-20.5 111T713-353.5V-52l-233-78.06L247-52Zm233.12-271q98.38 0 167.13-68.87T716-559.12q0-98.38-68.87-167.13T479.88-795q-98.38 0-167.13 68.87T244-558.88q0 98.38 68.87 167.13T480.12-323ZM322-164.5 480-205l158 40.5v-127q-35.5 20.5-75.19 32T480-248q-43.12 0-82.81-11.5-39.69-11.5-75.19-32v127ZM480-228Z" />
        </svg>
    )
}

export default WorkspacePremium;
const Diamond = ({ size = 24, color = "#0966ff", className="", tooltipContent="", tooltipPosition="" }) => {
    const tooltipProps = tooltipContent ? {
        'data-tooltip-content': tooltipContent,
        'data-tooltip-position': tooltipPosition
    } : {};

    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            height={`${size}px`}
            width={`${size}px`}
            viewBox="0 -960 960 960"
            fill={color}
            {...tooltipProps}
            className={className}
        >
            <path d="M480-124 83-600.5l118.5-237h557l118.5 237L480-124Zm-99-513.5h198l-62.5-125h-73l-62.5 125ZM442.5-286v-276.5h-230l230 276.5Zm75 0 230-276.5h-230V-286Zm145-351.5H774l-62.5-125H600l62.5 125Zm-476.5 0h111.5l62.5-125H248.5l-62.5 125Z" /></svg>
    )
}

export default Diamond;
const Workspaces = ({ size = 24, color = "#0966ff", className="", tooltipContent="", tooltipPosition="" }) => {
    const tooltipProps = tooltipContent ? {
        'data-tooltip-content': tooltipContent,
        'data-tooltip-position': tooltipPosition
    } : {};

    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            height={`${size}px`}
            width={`${size}px`}
            viewBox="0 -960 960 960"
            fill={color}
            {...tooltipProps}
            className={className}
        >
            <path d="M243.5-128q-64 0-109.25-45.25T89-282.5q0-64 45.25-109.25T243.5-437q64 0 109.25 45.25T398-282.5q0 64-45.25 109.25T243.5-128Zm473 0q-64 0-109.25-45.25T562-282.5q0-64 45.25-109.25T716.5-437q64 0 109.25 45.25T871-282.5q0 64-45.25 109.25T716.5-128Zm-473-75q32.79 0 56.15-23.35Q323-249.71 323-282.5t-23.35-56.15Q276.29-362 243.5-362t-56.15 23.35Q164-315.29 164-282.5t23.35 56.15Q210.71-203 243.5-203Zm473 0q32.79 0 56.15-23.35Q796-249.71 796-282.5t-23.35-56.15Q749.29-362 716.5-362t-56.15 23.35Q637-315.29 637-282.5t23.35 56.15Q683.71-203 716.5-203ZM480-522q-64.5 0-109.75-45.25T325-677q0-64.5 45.25-109.75T480-832q64.5 0 109.75 45.25T635-677q0 64.5-45.25 109.75T480-522Zm0-75q33 0 56.5-23.5T560-677q0-33-23.5-56.5T480-757q-33 0-56.5 23.5T400-677q0 33 23.5 56.5T480-597Zm0-80Zm236.5 394.5Zm-473 0Z" />
        </svg>
    )
}

export default Workspaces;
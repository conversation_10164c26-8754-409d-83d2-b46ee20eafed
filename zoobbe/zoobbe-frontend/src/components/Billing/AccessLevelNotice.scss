.access-level-notice-container {
  margin: 16px 0;
  
  .access-level-notice {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 16px 20px;
    border-radius: 8px;
    border: 1px solid;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    animation: slideIn 0.3s ease-out;

    &.view-only {
      background: #fee2e2;
      border-color: #ef4444;
    }

    &.limited {
      background: #fef3c7;
      border-color: #f59e0b;
    }

    .notice-content {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      flex: 1;

      .notice-icon {
        font-size: 20px;
        flex-shrink: 0;
        margin-top: 2px;
      }

      .notice-text {
        flex: 1;

        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          line-height: 1.2;
        }

        p {
          margin: 0;
          font-size: 14px;
          line-height: 1.5;
          opacity: 0.9;
        }
      }
    }

    .dismiss-button {
      background: none;
      border: none;
      font-size: 16px;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: background-color 0.2s;
      flex-shrink: 0;
      margin-left: 12px;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .access-level-notice-container {
    margin: 12px 0;

    .access-level-notice {
      padding: 14px 16px;

      .notice-content {
        gap: 10px;

        .notice-icon {
          font-size: 18px;
        }

        .notice-text {
          h4 {
            font-size: 15px;
            margin-bottom: 6px;
          }

          p {
            font-size: 13px;
          }
        }
      }

      .dismiss-button {
        font-size: 14px;
        margin-left: 8px;
      }
    }
  }
}

@media (max-width: 480px) {
  .access-level-notice-container {
    margin: 10px 0;

    .access-level-notice {
      padding: 12px 14px;
      flex-direction: column;
      gap: 12px;

      .notice-content {
        width: 100%;

        .notice-icon {
          font-size: 16px;
        }

        .notice-text {
          h4 {
            font-size: 14px;
            margin-bottom: 4px;
          }

          p {
            font-size: 12px;
          }
        }
      }

      .dismiss-button {
        align-self: flex-end;
        margin-left: 0;
      }
    }
  }
}

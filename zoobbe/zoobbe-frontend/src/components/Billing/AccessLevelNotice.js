import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { config } from '../../config';
import './AccessLevelNotice.scss';

/**
 * AccessLevelNotice component shows different messages based on user's access level
 * - Full access: No message
 * - Limited access: Info about limited features
 * - View-only access: Message to contact admin for full access
 */
const AccessLevelNotice = ({ workspaceId }) => {
    const [userAccessLevel, setUserAccessLevel] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isDismissed, setIsDismissed] = useState(false);
    
    const user = useSelector(state => state.user.user);
    const workspaces = useSelector(state => state.workspaces.workspaces);

    useEffect(() => {
        if (workspaceId && user) {
            checkUserAccessLevel();
        }
    }, [workspaceId, user]);

    const checkUserAccessLevel = async () => {
        try {
            setIsLoading(true);
            
            // First check from Redux store
            const workspace = workspaces.find(w => w.shortId === workspaceId);
            if (workspace) {
                const member = workspace.members?.find(m => m.user === user._id);
                if (member) {
                    setUserAccessLevel(member.accessLevel);
                    setIsLoading(false);
                    return;
                }
            }

            // If not found in Redux, fetch from API
            const response = await fetch(`${config.API_URI}/api/billing/workspace/${workspaceId}/status`, {
                credentials: 'include'
            });
            
            if (response.ok) {
                const data = await response.json();
                // This would need to be enhanced to return user-specific access level
                // For now, we'll use the workspace member data
                setUserAccessLevel('full'); // Default fallback
            }
        } catch (error) {
            console.error('Error checking user access level:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleDismiss = () => {
        setIsDismissed(true);
        // Store dismissal in localStorage for 24 hours
        localStorage.setItem(`access-notice-dismissed-${workspaceId}`, Date.now().toString());
    };

    // Check if notice was recently dismissed
    useEffect(() => {
        const dismissedTime = localStorage.getItem(`access-notice-dismissed-${workspaceId}`);
        if (dismissedTime) {
            const dismissedAt = parseInt(dismissedTime);
            const now = Date.now();
            // If it's been less than 24 hours since dismissal, don't show
            if (now - dismissedAt < 24 * 60 * 60 * 1000) {
                setIsDismissed(true);
            }
        }
    }, [workspaceId]);

    if (isLoading || isDismissed || !userAccessLevel || userAccessLevel === 'full') {
        return null;
    }

    const getNoticeContent = () => {
        switch (userAccessLevel) {
            case 'view-only':
                return {
                    type: 'view-only',
                    icon: '👁️',
                    title: 'View-Only Access',
                    message: 'You currently have view-only access to this workspace. You can view boards and cards but cannot make changes. Contact the workspace admin to upgrade your access.',
                    bgColor: '#fee2e2',
                    borderColor: '#ef4444',
                    textColor: '#dc2626'
                };
            case 'limited':
                return {
                    type: 'limited',
                    icon: '⚡',
                    title: 'Limited Access',
                    message: 'You have limited access to this workspace. Some premium features may not be available. The workspace admin can upgrade your access for full features.',
                    bgColor: '#fef3c7',
                    borderColor: '#f59e0b',
                    textColor: '#d97706'
                };
            default:
                return null;
        }
    };

    const noticeContent = getNoticeContent();
    if (!noticeContent) return null;

    return (
        <div className="access-level-notice-container">
            <div 
                className={`access-level-notice ${noticeContent.type}`}
                style={{
                    background: noticeContent.bgColor,
                    borderColor: noticeContent.borderColor
                }}
            >
                <div className="notice-content">
                    <div className="notice-icon">
                        {noticeContent.icon}
                    </div>
                    <div className="notice-text">
                        <h4 style={{ color: noticeContent.textColor }}>
                            {noticeContent.title}
                        </h4>
                        <p style={{ color: noticeContent.textColor }}>
                            {noticeContent.message}
                        </p>
                    </div>
                </div>
                <button 
                    className="dismiss-button"
                    onClick={handleDismiss}
                    style={{ color: noticeContent.textColor }}
                >
                    ✕
                </button>
            </div>
        </div>
    );
};

export default AccessLevelNotice;

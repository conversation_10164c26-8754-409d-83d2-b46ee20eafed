import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from "react-redux";

import { useNavigate, Link } from "react-router-dom";
import Spinner from '../Global/Spinner';
import './SearchResults.scss';
import { find } from "../../utils/helpers";
import { config } from '../../config';
import { fetchRecentBoards } from '../../redux/Slices/boardsSlice';
import { setLoading } from '../../redux/Slices/boardSlice';
import { togglePopover } from '../../redux/Slices/popoverSlice';

const SearchResults = ({ boardId, query, setQuery, setSearchResult }) => {

    const dispatch = useDispatch();

    const [results, setResults] = useState({ boards: [], cards: [] }); // Search results
    const [loading, setSearchLoading] = useState(false); // Loading state
    const [debouncedQuery, setDebouncedQuery] = useState(query); // Debounced query
    const [showResults, setShowResults] = useState(true); // Manage visibility of results

    const { workspaces } = useSelector(state => state.workspaces);
    const recentlyViewed = find.get(workspaces, { name: 'recentViewedBoards' });


    const { recentBoards } = useSelector((state) => state.boards) || [];

    useEffect(() => {
        if (recentBoards.length < 1) {
            dispatch(fetchRecentBoards({ limit: 10 }));
        }
    }, [dispatch]);


    const navigate = useNavigate(); // Initialize the navigate function

    // Debounce the query input
    useEffect(() => {
        setSearchLoading(false);
        if (query) {
            setSearchLoading(true); // Show loading when typing starts
        }

        const timer = setTimeout(() => {
            setDebouncedQuery(query); // Set the debounced query after typing stops
        }, 200); // Delay of 200ms after the user stops typing

        return () => clearTimeout(timer); // Clean up the timer
    }, [query]);

    // Fetch search results
    const fetchResults = async () => {
        if (!debouncedQuery) return;

        try {
            const token = localStorage.getItem('accessToken');

            let response = await fetch(`${config.API_URI}/api/global/search?query=${debouncedQuery}`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                credentials: 'include',
            });

            response = await response.json();

            if (response.success) {
                setResults(response.data);
                setSearchLoading(false); // Hide loading after receiving results
            }

        } catch (error) {
            console.log(error);
            setSearchLoading(false); // Hide loading on error
        }
    };

    useEffect(() => {
        if (debouncedQuery) {
            fetchResults(); // Fetch results only when debouncedQuery changes
        }
    }, [debouncedQuery]);

    const handleItemClick = (permalink) => {
        setShowResults(false); // Hide results on click
        setSearchResult(false);
        setQuery('');
        navigate(permalink); // Navigate to the target route

        // window.location.href = permalink;
    };

    const handleBoardLink = () => {
        dispatch(setLoading(true));
        dispatch(togglePopover({ contentId: null, position: { top: 0, left: 0 }, targetId: null }));
    }


    return (

        <>
            {
                showResults && (results.cards.length > 0 || recentBoards?.length > 0 || results.boards.length > 0) && (
                    <div className="search-results">
                        {loading && (
                            <div className='spinner-container'>
                                <Spinner size={30} speed={1.5} strokeWidth={5} />
                            </div>
                        )}

                        {!debouncedQuery && !loading && recentBoards?.length > 0 && (
                            <section className="category">
                                <h4>Recent</h4>
                                {!loading && recentBoards?.map(board => {
                                    return (
                                        <div
                                            className={`result-item${boardId == board?.shortId ? ' active' : ''}`}
                                            key={board._id}
                                            onClick={() => handleItemClick(board.permalink)}
                                        >

                                            {
                                                board.cover?.sizes?.thumbnail ? (
                                                    <div
                                                        className="icon board-icon"
                                                        style={{ backgroundImage: `url(${board.cover?.sizes?.medium})` }}
                                                    ></div>
                                                ) : (
                                                    <div className="icon card-icon"></div>
                                                )
                                            }
                                            <div className="content">
                                                <p className="title">{board.title}</p>
                                                <p className="subtitle">{board.workspace.name}</p>
                                            </div>

                                            {
                                                boardId !== board?.shortId && (
                                                    <Link onClick={handleBoardLink} className="card-permalink" to={board.permalink}></Link>

                                                )
                                            }

                                        </div>
                                    )
                                })}
                            </section>
                        )}

                        {/* Results */}
                        {debouncedQuery && !loading && (
                            <>
                                {results.cards.length > 0 && (
                                    <section className="category">
                                        <h4>CARDS</h4>
                                        {results.cards.map((card) => (
                                            <div
                                                className="result-item"
                                                key={card._id}
                                                onClick={() => handleItemClick(card.permalink)}
                                            >
                                                <div className="icon card-icon">
                                                    <span className="material-symbols-outlined">video_label</span>
                                                </div>
                                                <div className="content">
                                                    <p className="title">{card.title}</p>
                                                    <p className="subtitle">{card.boardTitle}: {card.actionList.title}</p>
                                                </div>
                                                <Link className="card-permalink" to={card.permalink}></Link>

                                            </div>
                                        ))}
                                    </section>
                                )}

                                {results.boards.length > 0 && (
                                    <section className="category">
                                        <h4>BOARDS</h4>
                                        {results.boards.map((board) => {
                                            console.log(board)
                                            return (
                                                <div
                                                    className="result-item"
                                                    key={board._id}
                                                    onClick={() => handleItemClick(board.permalink)}
                                                >
                                                    {
                                                        board.cover?.sizes?.thumbnail && (
                                                            <div
                                                                className="icon board-icon"
                                                                style={{ backgroundImage: `url(${board.cover?.sizes?.thumbnail})` }}
                                                            ></div>
                                                        )
                                                    }
                                                    <div className="content">
                                                        <p className="title">{board.title}</p>
                                                        <p className="subtitle">{board.workspace.name}</p>
                                                    </div>

                                                    {
                                                        boardId !== board?.shortId && (
                                                            <Link className="board-permalink" to={board.permalink}></Link>
                                                        )
                                                    }

                                                </div>
                                            )
                                        })}
                                    </section>
                                )}

                                {results.boards.length < 1 && results.cards.length < 1 && (
                                    <section className="category noting-found">
                                        <span className="material-icons">
                                            search_off
                                        </span>
                                        <div className="notging-found-content">
                                            We couldn't find anything matching your search.
                                            Try again with a different term, or refine your results with Advanced Search
                                        </div>
                                    </section>
                                )}




                            </>
                        )}

                    </div>
                )
            }
        </>

    );
};

export default SearchResults;

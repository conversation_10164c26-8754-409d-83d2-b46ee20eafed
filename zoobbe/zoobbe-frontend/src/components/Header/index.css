:root {
  --zoobbe-brand-color: purple;
}

.zoobbe-header {
  display: flex;
  align-items: center;
  background-color: #1e1e1e;
  padding: 8px 20px;
  color: var(--color-white);
  justify-content: space-between;
}
.zoobbe-header .header-right {
  display: flex;
  align-items: center;
}
.zoobbe-header .header-left {
  display: flex;
  align-items: center;
}
.zoobbe-header__logo {
  font-size: 24px;
  margin-right: 20px;
}
.zoobbe-header__logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--primary-text-color);
  font-weight: 500;
  font-size: 21px;
}
.zoobbe-header__menu {
  display: flex;
  align-items: center;
  flex-grow: 1;
  max-width: 900px;
  margin-right: auto;
}
.zoobbe-header__menu-item {
  background: none;
  border: none;
  color: var(--white-text-color-alternative);
  font-size: 16px;
  margin-right: 20px;
  cursor: pointer;
  text-decoration: none;
}
.zoobbe-header__menu-item--create {
  background-color: var(--brand-color);
  padding: 6px 12px;
  border-radius: 3px;
  color: var(--brand-btn-primary-color);
}
.zoobbe-header__search-container {
  display: flex;
  align-items: center;
  background-color: var(--scrolbar-thumb-background-color);
  padding: 0px 10px;
  border-radius: 3px;
  margin-right: 20px;
  height: 32px;
  position: relative;
  padding-right: 0;
}
.zoobbe-header__search-container__search-input {
  border: none;
  background: none;
  color: var(--white-text-color-alternative);
  outline: none;
  margin-left: 10px;
  height: 35px;
}
.zoobbe-header__search-container .search-content {
  display: flex;
  align-items: center;
  width: 100%;
  height: 32px;
}
.zoobbe-header__search-container.focused {
  outline: 2px solid var(--brand-color);
}
.zoobbe-header__search-container.isOpen {
  width: 500px;
}
.zoobbe-header__icons {
  display: flex;
  align-items: center;
  position: relative;
  gap: 10px;
}
.zoobbe-header__icons-notification {
  cursor: pointer;
  transition: 0.03s;
}
.zoobbe-header__icons-avatar {
  border-radius: 50%;
  width: 22px;
  height: 22px;
  cursor: pointer;
  border: 5px solid transparent;
  transition: 0.03s;
  border: 5px solid #3a3737;
}
.zoobbe-header__icons-avatar.active {
  border: 5px solid #233b5b;
}
.zoobbe-header .zoobbe-header__search-input {
  border: none;
  background: none;
  color: var(--white-text-color-alternative);
  outline: none;
  margin-left: 10px;
  width: 100%;
  height: 35px;
}/*# sourceMappingURL=index.css.map */
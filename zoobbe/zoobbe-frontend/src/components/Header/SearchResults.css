.search-results {
  background-color: #1e1e1e;
  color: #d0d0d0;
  padding: 15px 10px;
  width: 490px;
  position: absolute;
  z-index: 3;
  left: 0;
  top: 40px;
  max-height: 400px;
  overflow-x: hidden;
  overflow-y: auto;
}
.search-results .category {
  margin-bottom: 15px;
}
.search-results .category:last-child {
  margin-bottom: 0;
}
.search-results .category h4 {
  font-size: 12px;
  color: #888;
  margin-bottom: 10px;
  text-transform: uppercase;
  margin-top: 0;
  padding: 0 5px;
}
.search-results .category .result-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 5px 20px;
  width: 100%;
  cursor: pointer;
  margin-bottom: 0;
  margin-left: -15px;
  position: relative;
  gap: 12px;
}
.search-results .category .result-item a.card-permalink,
.search-results .category .result-item a.board-permalink {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  border: 0;
  width: 100%;
  display: block;
  height: 100%;
}
.search-results .category .result-item:hover {
  background: #242628;
}
.search-results .category .result-item .icon {
  width: 40px;
  height: 40px;
  background-color: #444;
  border-radius: 8px;
  background-size: cover;
  background-position: center;
  overflow: hidden;
}
.search-results .category .result-item .icon.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--primary-text-color);
  background: #242728;
}
.search-results .category .result-item .icon.card-icon img {
  width: 40px;
  height: 40px;
}
.search-results .category .result-item .content {
  width: calc(100% - 55px);
}
.search-results .category .result-item .content .title {
  font-size: 14px;
  color: var(--primary-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
  margin-bottom: 5px;
  font-weight: 500;
  max-width: 450px;
  word-wrap: break-word;
}
.search-results .category .result-item .content .subtitle {
  font-size: 12px;
  color: #888;
  margin: 0;
}
.search-results section.category.noting-found {
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  gap: 10px;
  font-size: 15px;
  line-height: 1.4rem;
  font-weight: 400;
}
.search-results section.category.noting-found span.material-icons {
  font-size: 70px;
  color: #686666;
}
.search-results .view-all {
  text-align: center;
}
.search-results .view-all a {
  font-size: 14px;
  color: #1a73e8;
  text-decoration: none;
}
.search-results .view-all a:hover {
  text-decoration: underline;
}/*# sourceMappingURL=SearchResults.css.map */
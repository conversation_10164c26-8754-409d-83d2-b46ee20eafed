import React, { useState, useEffect } from "react";
import "./scss/ImportSource.scss";
import StepActions from "./StepActions";
import { useDispatch } from "react-redux";
import { fetchWorkspaces } from "../../redux/Slices/workspaceSlice";
import { config } from "../../config";

const importSources = [
  { name: "Trell<PERSON>", width: "80", icon: "https://cloud.zoobbe.com/backgrounds/import/trello.svg", disabled: false },
  { name: "Asana", width: "130", icon: "https://cloud.zoobbe.com/backgrounds/import/asana.png", disabled: true },
  { name: "Basecamp", width: "130", icon: "https://cloud.zoobbe.com/backgrounds/import/basecamp.svg", disabled: true },
  { name: "Confluence", width: "150", icon: "https://cloud.zoobbe.com/backgrounds/import/confluence.svg", disabled: true },
  { name: "<PERSON>ra Software", width: "130", icon: "https://cloud.zoobbe.com/backgrounds/import/jira.svg", disabled: true },
  { name: "Monday.com", width: "155", icon: "https://cloud.zoobbe.com/backgrounds/import/monday.svg", disabled: true },
  { name: "Notion", width: "130", icon: "https://cloud.zoobbe.com/backgrounds/import/notion.svg", disabled: true },
  { name: "Slack", width: "110", icon: "https://cloud.zoobbe.com/backgrounds/import/slack.svg", disabled: true },
  { name: "Todoist", width: "110", icon: "https://cloud.zoobbe.com/backgrounds/import/todoist.png", disabled: true },
  { name: "Wrike", width: "110", icon: "https://cloud.zoobbe.com/backgrounds/import/wrike.svg", disabled: true },
];

const ImportSource = ({ goNext, step, selected, setSelected }) => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchWorkspaces());

    // Retrieve the selected source from localStorage on mount
    const storedSource = localStorage.getItem("selectedSource");
    if (storedSource) {
      setSelected(storedSource);
    }
  }, [dispatch, setSelected]);

  const handleSourceClick = (sourceName, isDisabled) => {
    if (!isDisabled) {
      if (selected === sourceName) {
        // Deselect if the same source is clicked
        setSelected(null);
        localStorage.removeItem("selectedSource"); // Remove from localStorage
      } else {
        // Select the new source
        setSelected(sourceName);
        localStorage.setItem("selectedSource", sourceName); // Save to localStorage
      }
    }
  };

  return (
    <div className="import-source">
      <h3>STEP {step}</h3>
      <h2>Select Source of Import</h2>

      <div className="container">
        <div className="import-grid">
          {importSources.map((source, index) => (
            <button
              key={index}
              className={`import-option ${source.disabled ? "full-deactivated" : ""} ${selected === source.name ? "selected" : ""
                }`}
              onClick={() => handleSourceClick(source.name, source.disabled)}
              disabled={source.disabled}
            >
              <span className="icon"><img width={source.width} src={source.icon} /></span>
              <div>
                {source.description && <p>{source.description}</p>}
              </div>
            </button>
          ))}
        </div>
        <StepActions
          showBack={false}
          onNext={goNext}
          isNextDisabled={!selected}
          nextLabel="Continue"
        />
      </div>
    </div>
  );
};

export default ImportSource;
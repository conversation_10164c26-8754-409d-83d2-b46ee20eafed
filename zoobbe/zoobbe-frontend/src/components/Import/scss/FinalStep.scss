// FinalStep.scss
.final-step {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;

    .progress-header h3{
        font-size: 14px;
        color: #777;
        text-transform: uppercase;
        text-align: center;
    }

    .options-section {
        margin-bottom: 30px;

        .question {
            font-size: 16px;
            color: var(--text-color);
            margin-bottom: 15px;
            font-weight: normal;
        }

        .radio-options {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .radio-option {
                display: flex;
                align-items: center;
                position: relative;
                padding-left: 15px;
                cursor: pointer;
                font-size: 14px;
                color: #333;
                line-height: 1.3;

                input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;

                    &:checked ~ .radio-checkmark {
                        background-color: white;
                        border-color: var(--secondary-brand-color);

                        &:after {
                            display: block;
                        }
                    }
                }

                .radio-checkmark {
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: 15px;
                    width: 15px;
                    background-color: white;
                    border: 1px solid #ccc;
                    border-radius: 50%;

                    &:after {
                        content: "";
                        position: absolute;
                        display: none;
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        background: var(--secondary-brand-color);
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                }

                .option-label {
                    margin-left: 8px;
                    color: var(--text-color);
                }
            }
        }

        .checkbox-options {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .checkbox-option {
                display: flex;
                align-items: center;
                position: relative;
                padding-left: 28px;
                cursor: pointer;
                font-size: 14px;
                color: #333;

                input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;

                    &:checked ~ .checkbox-checkmark {
                        background-color: var(--secondary-brand-color);
                        border-color: var(--secondary-brand-color);

                        &:after {
                            display: block;
                        }
                    }
                }

                .checkbox-checkmark {
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: 18px;
                    width: 18px;
                    background-color: white;
                    border: 1px solid #ccc;
                    border-radius: 3px;

                    &:after {
                        content: "";
                        position: absolute;
                        display: none;
                        left: 6px;
                        top: 2px;
                        width: 5px;
                        height: 10px;
                        border: solid white;
                        border-width: 0 2px 2px 0;
                        transform: rotate(45deg);
                    }
                }

                .option-label {
                    margin-left: 8px;
                }
            }
        }
    }

    .divider {
        height: 1px;
        background-color: #eee;
        margin: 30px 0;
    }

    .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 15px;

        button {
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid #ddd;
            background-color: #fff;

            &.primary-button {
                background-color: var(--secondary-brand-color);
                color: white;
                border-color: var(--secondary-brand-color);

                &:hover {
                    background-color: #45a049;
                }
            }

            &.secondary-button {
                color: #333;

                &:hover {
                    background-color: #f5f5f5;
                }
            }
        }
    }
}

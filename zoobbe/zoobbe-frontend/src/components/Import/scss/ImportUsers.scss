// ImportTasks.scss
.import-tasks {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;

    .progress-header {
        margin-bottom: 15px;

        .step-indicator {
            font-size: 14px;
            color: #666;
            font-weight: bold;
            text-transform: uppercase;
            display: block;
            margin-bottom: 5px;
        }

        .step-title {
            font-size: 20px;
            color: #333;
            margin: 0;
        }
    }

    .step-description {
        font-size: 14px;
        color: #666;
        margin-bottom: 30px;
        line-height: 1.5;
    }

    .container {
        max-width: 600px;
        margin: auto;
    }

    .mapping-container {
        display: flex;
        gap: 30px;
        margin-bottom: 40px;

        .users-section {
            flex: 1;

            .section-title {
                font-size: 14px;
                color: #333;
                margin-bottom: 15px;
                text-transform: uppercase;
                font-weight: bold;
            }

            .users-table {
                border: 1px solid #eee;
                border-radius: 4px;
                padding: 10px;

                .user-mapping {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    border-bottom: 1px solid #eee;

                    &:last-child {
                        border-bottom: none;
                    }

                    .imported-user {
                        flex: 1;
                        font-size: 14px;
                        color: #333;
                    }

                    .mapping-arrow {
                        margin: 0 15px;
                        color: #999;
                    }

                    .clickup-user {
                        flex: 1;
                        font-size: 14px;
                        color: #666;
                        font-style: italic;
                    }
                }
            }

            .note-box {
                border: 1px solid #eee;
                border-radius: 4px;
                padding: 15px;
                background-color: #f9f9f9;

                p {
                    font-size: 13px;
                    color: #666;
                    margin: 0;
                    line-height: 1.5;

                    strong {
                        color: #333;
                        font-weight: bold;
                    }
                }
            }
        }
    }

    .action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 15px;

        button {
            padding: 10px 20px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid #ddd;
            background-color: #fff;

            &.primary-button {
                background-color: var(--secondary-brand-color);
                color: white;
                border-color: var(--secondary-brand-color);

                &:hover {
                    background-color: #45a049;
                }
            }

            &.secondary-button {
                color: #333;

                &:hover {
                    background-color: #f5f5f5;
                }
            }
        }
    }
}

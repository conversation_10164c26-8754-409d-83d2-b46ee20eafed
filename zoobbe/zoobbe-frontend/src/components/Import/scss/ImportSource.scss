.import-source {
    text-align: center;
    padding: 20px;

    h3 {
        font-size: 14px;
        color: #777;
        text-transform: uppercase;
    }

    h2 {
        font-size: 24px;
        margin-bottom: 40px;
        color: var(--primary-text-color);
    }
    .container {
        max-width: 600px;
        margin: auto;
    }

    .import-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;

        .import-option {
            background: var(--import-option-bg-color);
            border: none;
            border-radius: 8px;
            gap: 10px;
            cursor: pointer;
            font-size: 14px;
            color: #555;
            height: 80px;
            align-items: center;
            justify-content: center;

            .icon {
                font-size: 20px;
                img {
                    max-width: 180px;
                }
            }

            &:hover {
                outline: 1px solid var(--brand-color);
            }

            &.disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            &.selected {
                outline: 2px solid var(--brand-color);
            }
        }
        button.import-option.full-deactivated {
            filter: grayscale(1);

            img {
                filter: var(--import-source-color);
            }
        }
    }

    .import-actions {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        max-width: 600px;
        margin: 20px auto 0;

        button {
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: 0.3s;

            &.back-btn {
                background: #f0f0f0;
                color: #555;

                &:hover {
                    background: #ddd;
                }
            }

            &.continue-btn {
                background: #007bff;
                color: var(--primary-text-color);

                &:disabled {
                    background: #ccc;
                    cursor: not-allowed;
                }

                &:hover:not(:disabled) {
                    background: #0056b3;
                }
            }
        }
    }
}

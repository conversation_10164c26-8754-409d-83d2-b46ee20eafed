.trello-import {
    text-align: center;
    padding: 20px;

    h3 {
        font-size: 14px;
        color: #777;
        text-transform: uppercase;
    }

    h2 {
        font-size: 22px;
        margin-bottom: 40px;
        color: var(--primary-text-color);
    }
    .container {
        max-width: 600px;
        margin: auto;

        .customize-mode {
            text-align: left;
            width: 100%;
            display: inline-block;
            margin-top: 10px;

            .customize-link {
                cursor: pointer;
                color: var(--brand-color);
                text-decoration: underline;
                font-size: 15px;
            }
        }
    }
    .import-container {
        display: flex;
        justify-content: center;
        gap: 20px;
        max-width: 700px;
        margin: 0 auto;

        .loading-spinner {
            color: var(--primary-text-color);
        }
    }

    .board-row {
        display: flex;
        // align-items: self-end;
        width: 100%;
        gap: 20px;
        justify-content: space-between;
        margin-bottom: 10px;
        align-items: center;

        span.workspace-label {
            display: flex;
            align-items: center;
            width: calc(100% - 300px);
            color: var(--primary-text-color);
        }
    }

    .boards-list {
        display: flex;
        flex-direction: column;
        align-items: start;
        width: 100%;

        .all-btn {
            background: var(--single-card-side-action-bg-color);
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: none;
            transition: 0.3s;

            &.selected {
                background: #008060;
                color: var(--white);
            }
        }

        .board-item {
            display: flex;
            align-items: center;
            background: var(--board-option-button-background-color);
            border-radius: 5px;
            padding: 0 12px;
            width: calc(100% - 340px);
            cursor: pointer;
            transition: 0.3s;
            height: 35px;
            user-select: none;

            input {
                border: none;
                background: transparent;
                width: 100%;
                font-size: 14px;
                pointer-events: none;
                color: var(--primary-text-color);
            }

            .checkmark {
                margin-right: 10px;
                margin-right: 0.5rem;
                display: inline-flex;
                align-items: center;
                height: 18px;
                width: 18px;
                border-radius: 50px;
                justify-content: center;

                svg {
                    height: 18px;
                    fill: var(--brand-color);
                }
            }

            &.selected {
                background: #e6f9f0;
                border: 1px solid #008060;
            }
        }

        .zoobbe-select {
            width: calc(100% - 300px);
        }
    }

    .import-mapping {
        text-align: left;
        display: flex;
        flex-direction: column;
        width: 100%;

        span.workspace-label {
            margin-bottom: 18px;
        }

        .board-mapping {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 5px;
        }

        .arrow {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .space-select {
            padding: 10px;
            font-size: 14px;
            border-radius: 5px;
            border: 1px solid #ddd;
            width: 200px;
        }

        .customize-link {
            color: #008060;
            font-size: 14px;
            margin-top: 10px;
            text-decoration: underline;
            cursor: pointer;
        }
    }

    .import-actions {
        display: flex;
        justify-content: space-between;
        max-width: 700px;
        margin: 20px auto 0;

        button {
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: 0.3s;

            &.back-btn {
                background: var(--card-background-color);
                color: #555;

                &:hover {
                    background: #ddd;
                }
            }

            &.next-btn {
                background: #008060;
                color: var(--primary-text-color);

                &:hover {
                    background: #006644;
                }
            }
        }
    }
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;

    button {
        padding: 10px 20px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        border: 1px solid var(--zoobbe-select-border-color);
        background-color: var(--single-card-side-action-bg-color);
        color: var(--white);


        &.primary-button {
            background-color: var(--secondary-brand-color);
            border-color: var(--secondary-brand-color);

            &:hover {
                background-color: #45a049;
            }
        }

        &.secondary-button {
            color: var(--filter-option-text-color);

            &:hover {
                background-color: var(--single-card-action-button-hover-color);
            }
        }
    }
}

.export-import-container {
    margin: 2rem;
    max-width: 500px;
    margin: auto;
    margin-top: 60px;

    .progress-bar-container {
        display: flex;
        flex-direction: column;
        margin-top: 30px;

        .progress-bar-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            justify-content: space-between;

            .import-titles {
                display: flex;
                align-items: center;
                gap: 10px;
                color: var(--primary-text-color);
                font-size: 15px;
                font-weight: 600;
                width: calc(100% - 100px);
                font-family:
                    system-ui,
                    -apple-system,
                    BlinkMacSystemFont,
                    "Segoe UI",
                    Roboto,
                    Oxygen,
                    Ubuntu,
                    Cantarell,
                    "Open Sans",
                    "Helvetica Neue",
                    sans-serif;

                .icon-bar {
                    position: relative;
                    display: flex;
                    color: green;

                    .material-symbols-outlined.import-icon {
                        position: absolute;
                        left: 8px;
                        top: 8px;
                        color: #3498dbb3;
                    }
                }

                .title {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }

            .import-titles.import-success {
                margin-bottom: 10px;
            }

            .items-imported {
                color: var(--primary-text-color);
                font-size: 15px;
                font-weight: 600;
                font-family:
                    system-ui,
                    -apple-system,
                    BlinkMacSystemFont,
                    "Segoe UI",
                    Roboto,
                    Oxygen,
                    Ubuntu,
                    Cantarell,
                    "Open Sans",
                    "Helvetica Neue",
                    sans-serif;
            }
        }

        .progress-bar {
            position: relative;
            width: 100%;
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;

            .progress-bar-fill {
                height: 100%;
                background-color: #6a0dad;
                transition: width 0.4s ease;
            }
        }

        .progress-bar-percentage {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
            text-align: right;
            font-weight: 900;
        }
    }

    .no-progress-message {
        display: flex;
        align-items: center;
        gap: 10px;
        text-align: center;
        margin-top: 20px;
        color: #888;
        font-size: 16px;

        .info-icon {
            font-size: 24px;
            color: #3498db;
        }

        .title {
            font-weight: 600;
        }
    }
}

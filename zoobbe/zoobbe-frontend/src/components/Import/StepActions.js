import React from "react";

import "./scss/StepActions.scss";

const StepActions = ({
    showBack = true,
    showNext = true,
    nextLabel = "Next",
    onBack,
    onNext,
    isNextDisabled = false,
}) => {
    return (
        <div className="action-buttons">
            {showBack && (
                <button className="secondary-button" onClick={onBack}>
                    Back
                </button>
            )}
            {showNext && (
                <button
                    className={`primary-button ${isNextDisabled ? "full-deactivated" : ""}`}
                    onClick={onNext}
                    disabled={isNextDisabled}
                >
                    {nextLabel}
                </button>
            )}
        </div>
    );
};

export default StepActions;

import React, { useState } from 'react';
import './scss/ImportUsers.scss';
import StepActions from './StepActions';

const ImportUsers = ({ goNext, goBack }) => {

    const [selected, setSelected] = useState(null);

    return (
        <div className="import-tasks">
            <div className="progress-header">
                <span className="step-indicator">STEP 3 OF 4</span>
                <h2 className="step-title">Import Tasks</h2>
            </div>

            <p className="step-description">
                Select which Boards from Trello you want to import (left) and map them into ClickUp (right).
            </p>

            <div className="container">
                <div className="mapping-container">
                    <div className="users-section">
                        <h3 className="section-title">IMPORTED USERS:</h3>
                        <div className="users-table">
                            <div className="user-mapping">
                                <span className="imported-user">Akash</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                            <div className="user-mapping">
                                <span className="imported-user">embedpress</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                            <div className="user-mapping">
                                <span className="imported-user">seakashdiu</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                            <div className="user-mapping">
                                <span className="imported-user">Akash Mia</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                            <div className="user-mapping">
                                <span className="imported-user">aassdsadffww</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                            <div className="user-mapping">
                                <span className="imported-user">sdsvsdvsd</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                            <div className="user-mapping">
                                <span className="imported-user">weuygfuwe</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                            <div className="user-mapping">
                                <span className="imported-user">themeixllc</span>
                                <span className="mapping-arrow">→</span>
                                <span className="clickup-user">Inactive user</span>
                            </div>
                        </div>
                    </div>

                    <div className="users-section">
                        <h3 className="section-title">CLICKUP USERS:</h3>
                        <div className="note-box">
                            <p>
                                <strong>Back</strong><br />
                                If you haven't already added users into ClickUp, simply select 'invite by email' to invite and map automatically.
                            </p>
                        </div>
                    </div>
                </div>

                <StepActions
                    showBack={true}
                    onBack={goBack}
                    onNext={goNext}
                    isNextDisabled={false}
                    nextLabel="Next"
                />
            </div>
        </div>
    );
};

export default ImportUsers;
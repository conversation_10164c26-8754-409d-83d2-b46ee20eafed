import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import "./scss/TrelloImport.scss";
import StepActions from "./StepActions";
import ZoobbeSelect from "../Global/ZoobbeSelect";
import { fetchLiteWorkspaces, fetchWorkspaces } from "../../redux/Slices/workspaceSlice";

const TrelloImport = ({
  goNext,
  goBack,
  step,
  trelloBoards,
  selectedBoards,
  setSelectedBoards,
  selectedWorkspace,
  setSelectedWorkspace,
  boardWorkspaces,
  setBoardWorkspaces,
  customizeMode,
  setCustomizeMode,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showBoardList, setShowBoardList] = useState(false);
  const { liteWorkspaces, status } = useSelector((state) => state.workspaces);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchLiteWorkspaces());
  }, []);

  console.log({ liteWorkspaces });

  const workspaceOptions =
    liteWorkspaces?.map((workspace) => ({
      value: workspace._id.toString(),
      label: workspace.name,
    })) || [];

  useEffect(() => {
    if (workspaceOptions.length && !selectedWorkspace) {
      setSelectedWorkspace(workspaceOptions[0]);
    }
  }, [workspaceOptions]);

  useEffect(() => {
    if (trelloBoards.length > 0) {
      setSelectedBoards(trelloBoards);
      setIsLoading(false);
    }
  }, [trelloBoards]);

  const toggleBoardSelection = (board) => {
    setSelectedBoards((prev) => {
      const isSelected = prev.find((b) => b.id === board.id); // match by _id

      if (isSelected) {
        return prev.filter((b) => b.id !== board.id);
      } else {
        return [...prev, board];
      }
    });
  };



  const handleWorkspaceSelect = (workspace) => {
    if (!customizeMode) {
      setSelectedWorkspace(workspace);
    }
  };


  const handleCustomizeToggle = () => {
    if (!customizeMode) {
      const defaultWorkspace = selectedWorkspace || workspaceOptions[0];
      const mappings = {};
      selectedBoards.forEach((board) => {
        mappings[board.id] = boardWorkspaces[board.id] || defaultWorkspace;
      });
      setBoardWorkspaces(mappings);
      setCustomizeMode(true);
    } else {
      setCustomizeMode(false);
    }
  };

  const handleBoardWorkspaceChange = (board, selected) => {

    setBoardWorkspaces((prev) => ({
      ...prev,
      [board]: selected,
    }));

  };

  const handleNext = () => {
    const payload = {
      source: null,
      includeArchived: true,
      boards: selectedBoards.map((board) => ({
        board: board.id,
        workspaceId: customizeMode
          ? boardWorkspaces[board.id]?.value || boardWorkspaces[board.id]?._id
          : selectedWorkspace?.value || selectedWorkspace?._id,
      })),
    };

    console.log("Payload to submit:", payload);
    goNext();
  };



  if (isLoading || status == 'loading') {

    return (
      <div className="trello-import">
        <div className="container">
          <div className="import-container">
            <div className="loading-spinner">Loading Trello boards...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="trello-import">
      <h3>STEP {step} OF 3</h3>
      <h2>Select Trello Boards to Import</h2>

      <div className="container">
        <div className="import-container">
          {isLoading || status == 'loading' ? (
            <div className="loading-spinner">Loading Trello boards...</div>
          ) : (
            <>
              {!showBoardList && !customizeMode ? (
                <div className="boards-list">
                  <div className="board-row">
                    <button
                      className={`all-btn ${selectedBoards.length === trelloBoards.length ? "selected" : ""
                        }`}
                      onClick={() =>
                        setSelectedBoards(
                          selectedBoards.length === trelloBoards.length
                            ? []
                            : trelloBoards
                        )
                      }
                    >
                      ALL
                    </button>
                    <span className="workspace-label">Select Workspace</span>
                  </div>

                  <div className="board-row single-summary-row">

                    <div
                      className="board-item"
                      onClick={() =>
                        setSelectedBoards(
                          selectedBoards.length === trelloBoards.length ? [] : trelloBoards
                        )}
                    >
                      {
                        selectedBoards.length ? (
                          <span className="checkmark">
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#a855f7"><path d="M382-240 154-468l57-57 171 171 367-367 57 57-424 424Z"></path></svg>
                          </span>

                        ) : ""}
                      <input type="text" value={`Import all ${trelloBoards.length} board${trelloBoards.length > 1 ? "s" : ""}`} readOnly />
                    </div>

                    {!customizeMode && (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#e8eaed"><path d="M0 0h24v24H0z" fill="none" /><path d="M16.01 11H4v2h12.01v3L20 12l-3.99-4z" /></svg>
                        <ZoobbeSelect
                          options={[...workspaceOptions]}
                          defaultSelectedOption={workspaceOptions.findIndex(
                            (opt) => opt.value === selectedWorkspace?.value
                          )}
                          onSelect={handleWorkspaceSelect}
                        />
                      </>
                    )}
                  </div>
                </div>

              ) : (
                <div className="boards-list">
                  <div className="board-row">
                    <button
                      className={`all-btn ${selectedBoards.length === trelloBoards.length ? "selected" : ""
                        }`}
                      onClick={() =>
                        setSelectedBoards(
                          selectedBoards.length === trelloBoards.length
                            ? []
                            : trelloBoards
                        )
                      }
                    >
                      ALL
                    </button>
                    <span className="workspace-label">Select Workspace</span>
                  </div>

                  {trelloBoards.map((board, index) => {
                    const isSelected = selectedBoards.some((b) => b.id === board.id);

                    return (
                      <div key={index} className={`board-row ${isSelected ? "selected" : ""}`}>
                        <div
                          className="board-item"
                          onClick={() => toggleBoardSelection(board)}
                        >
                          {isSelected ? (
                            <span className="checkmark">
                              <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#a855f7"><path d="M382-240 154-468l57-57 171 171 367-367 57 57-424 424Z"></path></svg>
                            </span>
                          ) : ""}
                          <input type="text" value={board.name} readOnly />
                        </div>

                        {customizeMode && (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#e8eaed"><path d="M0 0h24v24H0z" fill="none" /><path d="M16.01 11H4v2h12.01v3L20 12l-3.99-4z" /></svg>
                            <ZoobbeSelect
                              options={workspaceOptions}
                              defaultSelectedOption={workspaceOptions.findIndex(
                                (opt) => opt.value === boardWorkspaces[board.id]?.value
                              )}
                              onSelect={(selected) => handleBoardWorkspaceChange(board.id, selected)}
                              className={`${!isSelected ? "full-deactivated" : ""}`}
                            />

                          </>
                        )}

                        {!customizeMode && index === 0 && (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#e8eaed"><path d="M0 0h24v24H0z" fill="none" /><path d="M16.01 11H4v2h12.01v3L20 12l-3.99-4z" /></svg>
                            <ZoobbeSelect
                              options={workspaceOptions}
                              defaultSelectedOption={workspaceOptions.findIndex(
                                (opt) => opt.value === boardWorkspaces[board.id]?.value
                              )}
                              onSelect={(selected) => handleBoardWorkspaceChange(board.id, selected)}
                            />
                          </>

                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </>
          )}
        </div>

        {
          (!isLoading && status !== 'loading') && (
            <div className="customize-mode">
              <span
                className="customize-link"
                onClick={(e) => {
                  e.preventDefault();
                  handleCustomizeToggle();
                }}
              >
                {customizeMode
                  ? "Use same space for all boards"
                  : "Customize Spaces individually"}
              </span>
            </div>

          )
        }

        <StepActions
          showBack={true}
          onBack={goBack}
          onNext={handleNext}
          isNextDisabled={(isLoading || status === 'loading') || !selectedBoards.length}
          nextLabel="Next"
        />
      </div>
    </div>
  );
};

export default TrelloImport;

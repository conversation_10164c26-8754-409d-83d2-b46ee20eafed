import React, { useEffect, useState } from "react";
import ImportSource from "./ImportSource";
import TrelloImport from "./TrelloImport";
import ImportUsers from "./ImportUsers";
import FinalStep from "./FinalStep";
import { config } from "../../config";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { io } from 'socket.io-client'; // Import socket.io-client


const OnboardingFlow = () => {


    const API_KEY = config.TRELLO_AUTH_API_KEY;
    const APP_NAME = "ZoobbeImport";
    const SCOPE = "read";
    const EXPIRATION = "never";
    const CALLBACK_URL = window.location.origin + "/import";

    const [token, setToken] = useState();
    const [trelloBoards, setTrelloBoards] = useState([]);

    const [step, setStep] = useState(parseInt(localStorage.getItem("onboardingStep")));
    const [importSource, setImportSource] = useState(localStorage.getItem("selectedSource"));
    const [selectedBoards, setSelectedBoards] = useState([]);
    const [selectedWorkspace, setSelectedWorkspace] = useState(null);
    const [customizeMode, setCustomizeMode] = useState(false);
    const [boardWorkspaces, setBoardWorkspaces] = useState({});
    const [isIncludeArchived, setIsIncludeArchived] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false); 


    const { user } = useSelector(state => state.user);

    const navigate = useNavigate();


    const goNext = () => {
        setStep((prev) => {
            const next = Math.min(prev + 1, 3);
            localStorage.setItem("onboardingStep", next);
            return next;
        });
    };

    const goBack = () => {
        setStep((prev) => {
            const back = Math.max(prev - 1, 1);
            localStorage.setItem("onboardingStep", back);
            return back;
        });
    };

    // Socket initialization
    useEffect(() => {
        const socket = io(config.API_URI); // Update with your socket server URI
        if (user) {
            const userId = user?.user?._id;
            socket.emit('join', userId);

            socket.on('importProgress', (data) => {
                navigate("/import/progress"); // Use navigate function here
            });
        }

        return () => {
            socket.disconnect();
        };
    }, [user]);


    const onSubmit = async () => {
        setIsSubmitting(true); // Set loading state to true
        const payload = {
            source: importSource,
            includeArchived: isIncludeArchived,
            token,
            boards: selectedBoards.map((board) => ({
                id: board.id,
                workspaceId: (customizeMode ? boardWorkspaces[board.id]?.value : selectedWorkspace?.value) || null,
            })),
        };

        try {
            const res = await fetch(`${config.API_URI}/api/trello/import`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                credentials: 'include',
                body: JSON.stringify(payload),
            });

            if (!res.ok) throw new Error("Import failed");

            const result = await res.json();
            console.log("✅ Import started:", result);
            // Redirect to progress page or dashboard
        } catch (err) {
            console.error("❌ Import error:", err);
            alert("Failed to start import");
        } finally {
            setIsSubmitting(false); 
        }
    };


    useEffect(() => {
        // Extract Trello token from URL fragment
        const urlParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = urlParams.get("token");

        if (accessToken) {
            setToken(accessToken);
            setStep(2);
            localStorage.setItem("onboardingStep", "2");
            window.history.replaceState({}, document.title, window.location.pathname);
        } else {
            localStorage.removeItem("onboardingStep");
            localStorage.removeItem("selectedSource");
            setImportSource(null);
            setStep(1);

        }

    }, []);



    useEffect(() => {
        if (token) {
            fetchBoards().then(() => {
                setStep(2); // Go to board selection
            });
        }
    }, [token]);


    const onAuthTrello = () => {
        const authUrl = `https://trello.com/1/authorize?expiration=${EXPIRATION}&name=${APP_NAME}&scope=${SCOPE}&response_type=token&key=${API_KEY}&return_url=${CALLBACK_URL}`;
        window.location.href = authUrl;
    };

    const fetchBoards = async () => {
        try {
            const response = await fetch(`https://api.trello.com/1/members/me/boards?key=${API_KEY}&token=${token}`);
            const data = await response.json();
            setSelectedBoards(data); // preselect all
            setTrelloBoards(data);
        } catch (error) {
            console.error("Error fetching boards:", error);
        }
    };


    return (
        <div id="zoobbe-import-onboarding-flow">
            {step === 1 && (
                <ImportSource
                    step={step}
                    goNext={onAuthTrello}
                    selected={importSource}
                    setSelected={setImportSource}
                />
            )}
            {step === 2 && (
                <TrelloImport
                    step={step}
                    goNext={goNext}
                    goBack={goBack}
                    trelloBoards={trelloBoards}
                    selectedBoards={selectedBoards}
                    setSelectedBoards={setSelectedBoards}
                    selectedWorkspace={selectedWorkspace}
                    setSelectedWorkspace={setSelectedWorkspace}
                    boardWorkspaces={boardWorkspaces}
                    setBoardWorkspaces={setBoardWorkspaces}
                    customizeMode={customizeMode}
                    setCustomizeMode={setCustomizeMode}
                />
            )}
            {step === 3 && (
                <FinalStep
                    step={step}
                    goBack={goBack}
                    isIncludeArchived={isIncludeArchived}
                    setIsIncludeArchived={setIsIncludeArchived}
                    onSubmit={onSubmit}
                    isSubmitting={isSubmitting} 
                />
            )}
        </div>
    );
};


export default OnboardingFlow;

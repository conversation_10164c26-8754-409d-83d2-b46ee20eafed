import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { io } from 'socket.io-client'; // Import socket.io-client
import "./scss/ExportImport.scss";
import { config } from '../../config';
import Spinner from '../Global/Spinner';

import { fetchUser } from '../../redux/Slices/thunks';
import { hideSnackBar, showSnackBar } from '../../redux/Slices/snackbarSlice';


const ImportProgress = () => {
    const [log, setLog] = useState([]); // Store multiple logs
    const [progress, setProgress] = useState(0); // State for progress message
    const [importSuccess, setImportSuccess] = useState(false); // New state for import success
    const [isImportStart, setImportStart] = useState(false); // New state for import success

    const dispatch = useDispatch();

    const { user } = useSelector(state => state.user);


    useEffect(() => {
        if (!user) {
            dispatch(fetchUser());
        }
    }, [dispatch, user]);


    // Socket initialization
    useEffect(() => {
        const socket = io(config.API_URI); // Update with your socket server URI
        if (user) {
            const userId = user?.user?._id;
            socket.emit('join', userId);


            socket.on('importProgress', (data) => {
                setImportStart(true);
                dispatch(hideSnackBar());
                setProgress(data.progress);
                setLog(data);

                console.log({ data });

                // Mark as successful when import reaches 100%
                if (data.progress >= 100) {
                    setImportSuccess(true);
                }

            });


            console.log('Socket initialized for user:', userId);
        }

        return () => {
            socket.disconnect();
        };
    }, [user]);


    return (
        <div>
            <div className="export-import-container">


                {/* Progress bar and status */}
                {progress !== '0' ? (
                    <div className="progress-bar-container">
                        <div className="progress-bar-header">
                            <div className={`import-titles${importSuccess ? ' import-success' : ''}`}>
                                <div className="icon-bar">
                                    {importSuccess ? (
                                        <span className="material-symbols-outlined success-icon">
                                            check_circle
                                        </span>
                                    ) : (
                                        <>
                                            <span className="material-symbols-outlined import-icon">import_export</span>
                                            {!importSuccess && <Spinner size={40} color="#3498db" speed={1.5} />}
                                        </>
                                    )}
                                </div>

                                <span className='title'>
                                    {importSuccess ? 'Successfully Imported' : log.title || 'Importing...'}
                                </span>
                            </div>
                            <div className="items-imported">{log.index} / {log.length}</div>
                        </div>
                        <div className="progress-bar">
                            <div
                                className="progress-bar-fill"
                                style={{ width: `${progress}%` }}
                            ></div>
                        </div>
                        <div className="progress-bar-percentage">{progress.toFixed(0)}%</div>
                    </div>
                ) : (
                    <div className="no-progress-message">
                        <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24" fill="#888"><path d="M440-280h80v-240h-80zm40-320q17 0 28.5-11.5T520-640t-11.5-28.5T480-680t-28.5 11.5T440-640t11.5 28.5T480-600m0 520q-83 0-156-31.5T197-197t-85.5-127T80-480t31.5-156T197-763t127-85.5T480-880t156 31.5T763-763t85.5 127T880-480t-31.5 156T763-197t-127 85.5T480-80m0-80q134 0 227-93t93-227-93-227-227-93-227 93-93 227 93 227 227 93m0-320" /></svg>
                        <span className="title">No import progress yet. Please wait or start an import.</span>
                    </div>
                )}
            </div>

        </div>
    );
}

export default ImportProgress;
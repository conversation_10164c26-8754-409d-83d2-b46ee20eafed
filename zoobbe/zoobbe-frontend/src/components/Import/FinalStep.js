import React from 'react';
import './scss/FinalStep.scss';
import StepActions from './StepActions';

const FinalStep = ({ goBack, step, isIncludeArchived, setIsIncludeArchived, onSubmit, isSubmitting }) => {
    const handleChange = (e) => {
        setIsIncludeArchived(e.target.value === "yes");
    };

    return (
        <div className="final-step">
            <div className="progress-header">
                <h3>STEP {step} OF 3</h3>
            </div>

            <div className="import-container">
                <div className="options-section">
                    <h3 className="question">Do you want to include archived cards?</h3>
                    <div className="radio-options">
                        {/* yes */}
                        <label className="radio-option">
                            <input
                                type="radio"
                                name="archivedCards"
                                value="yes"
                                checked={isIncludeArchived === true}
                                onChange={handleChange}
                            />
                            <span className="radio-checkmark"></span>
                            <span className="option-label">Yes, include archived cards</span>
                        </label>
                        {/* no */}
                        <label className="radio-option">
                            <input
                                type="radio"
                                name="archivedCards"
                                value="no"
                                checked={isIncludeArchived === false}
                                onChange={handleChange}
                            />
                            <span className="radio-checkmark"></span>
                            <span className="option-label">No, do not include archived cards</span>
                        </label>
                    </div>
                </div>

                <StepActions
                    onBack={goBack}
                    onNext={onSubmit}
                    nextLabel={isSubmitting ? "Import starting..." : "Complete"}
                    isNextDisabled={isSubmitting}
                />
            </div>
        </div>
    );
};

export default FinalStep;

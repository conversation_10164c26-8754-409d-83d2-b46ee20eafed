.activity-container {
  width: 100%;
  border-collapse: collapse;
  font-family: system-ui;
  color: #d9d9d9;
  max-width: 750px;
  margin: auto;
}
.activity-container .header-item {
  display: flex;
  align-items: center;
  gap: 6px;
}
.activity-container .activities {
  margin-left: -65px;
}

.profile-page.card-loading {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}
.profile-page.card-loading .top-profile {
  flex-shrink: 0;
}
.profile-page.card-loading .activity-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.profile-page.card-loading .activity-container .table-body {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}/*# sourceMappingURL=activity.css.map */
.card-table {
  width: calc(100% - 100px);
  border-collapse: collapse;
  font-family: system-ui;
  color: #d9d9d9;
  max-width: 1200px;
  margin: auto;
}
.card-table .table-header {
  padding: 10px 0;
  font-weight: bold;
  display: grid;
  grid-template-columns: minmax(0, 500px) minmax(0, 220px) minmax(0, 200px) minmax(0, 160px) minmax(0, 200px);
  grid-template-rows: auto;
  -moz-column-gap: 8px;
       column-gap: 8px;
  border-bottom: 1px solid var(--ds-background-neutral, rgba(9, 30, 66, 0.0588235294));
  text-decoration: none;
}
.card-table .table-header .header-item {
  flex: 1;
  text-align: left;
  color: var(--primary-text-color);
}
.card-table .table-body .table-row {
  border-top: 1px solid #3a3a3a;
  display: grid;
  grid-template-columns: minmax(0, 500px) minmax(0, 220px) minmax(0, 200px) minmax(0, 160px) minmax(0, 200px);
  grid-template-rows: auto;
  -moz-column-gap: 8px;
       column-gap: 8px;
  border-bottom: 1px solid var(--ds-background-neutral, rgba(9, 30, 66, 0.0588235294));
  text-decoration: none;
}
.card-table .table-body .table-row a {
  padding: 12px 4px;
  color: #d9d9d9;
  text-decoration: none;
  font-weight: 400;
  line-height: 1.6;
  font-size: 15px;
}
.card-table .table-body .table-row a:hover {
  background-color: #2a2a2a;
  color: var(--brand-color);
}
.card-table .table-body .table-row .table-cell {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 15px;
  color: var(--primary-text-color);
  word-break: break-word;
}
.card-table .table-body .table-row .table-cell.board-info {
  display: flex;
  align-items: center;
}
.card-table .table-body .table-row .table-cell.board-info .board-image {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 3px;
}
.card-table .table-body .table-row .labels {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}
.card-table .table-body .table-row .labels .label {
  width: 20px;
  height: 5px;
  border-radius: 3px;
}
.card-table .table-body .table-row .labels .label.green {
  background-color: #4caf50;
}
.card-table .table-body .table-row .labels .label.purple {
  background-color: #9c27b0;
}
.card-table .table-body .table-row .labels .label.blue {
  background-color: #2196f3;
}
.card-table .table-body .table-row .labels .label.orange {
  background-color: #ff9800;
}
.card-table .table-body .table-row .labels .label.red {
  background-color: #f44336;
}
.card-table .table-body .table-row .due-date {
  display: flex;
  align-items: center;
}
.card-table .table-body .table-row .due-date.completed {
  background-color: #004085;
  padding: 2px 5px;
  border-radius: 3px;
  color: var(--white-text-color-alternative);
  font-size: 12px;
}

.profile-page.card-loading {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}
.profile-page.card-loading .top-profile {
  flex-shrink: 0;
}
.profile-page.card-loading .card-table {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.profile-page.card-loading .card-table .table-body {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}/*# sourceMappingURL=CardTable.css.map */
.top-profile .account-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  border-bottom: 1px solid var(--scrolbar-thumb-background-color);
  width: calc(100% - 300px);
  margin: 0 auto;
  margin-top: 40px;
}
.top-profile .account-section .profile-picture {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 10px;
}
.top-profile .account-section .account-info {
  flex-grow: 1;
}
.top-profile .account-section .account-info .username {
  font-weight: bold;
  margin: 0;
  color: var(--primary-text-color);
}
.top-profile .account-section .account-info .email {
  margin: 0;
  color: var(--primary-text-color);
}
.top-profile nav.navbar {
  margin-bottom: 20px;
}
.top-profile nav.navbar .active {
  font-weight: bold;
  color: #1a73e8; /* Example color for the active link */
  position: relative;
}
.top-profile nav.navbar .active::before {
  position: absolute;
  height: 2px;
  width: 100%;
  background: #1a73e8;
  content: "";
  top: -1.5px;
}
.top-profile nav.navbar ul {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  display: flex;
  gap: 20px;
}
.top-profile nav.navbar ul li {
  list-style: none;
}
.top-profile nav.navbar ul li a {
  text-decoration: none;
  border: none;
  box-sizing: border-box;
  color: var(--ds-text-subtle, #44546f);
  display: inline-block;
  font-weight: 700;
  padding: 15px 0;
  text-decoration: none;
}

@media only screen and (max-width: 991px) {
  nav.navbar ul {
    justify-content: center;
  }
}/*# sourceMappingURL=ProfileNavbar.css.map */
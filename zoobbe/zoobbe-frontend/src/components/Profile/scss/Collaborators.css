.zoobbe-main-content {
  margin: 0 auto;
}
.zoobbe-main-content .collaborators {
  color: var(--white-text-color-alternative);
  display: flex;
  gap: 20px;
  max-width: 1180px;
  margin: auto;
}
.zoobbe-main-content .collaborators .collaborators-content {
  width: 100%;
}
.zoobbe-main-content .collaborators h2 {
  margin-top: 0;
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 20px;
}
.zoobbe-main-content .collaborators .tabs {
  display: flex;
  margin-bottom: 20px;
  flex-direction: column;
  gap: 10px;
  width: 280px;
}
.zoobbe-main-content .collaborators .tabs .tab {
  padding: 10px 15px;
  border: 1px solid #2b2626;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
}
.zoobbe-main-content .collaborators .tabs .tab.selected {
  background-color: #3a3f47;
  color: var(--white-text-color-alternative);
}
.zoobbe-main-content .collaborators .tabs .tab:not(.selected) {
  background-color: #1e1e1e;
  color: #aaa;
}
.zoobbe-main-content .collaborators .workspace-members h3 {
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  margin-bottom: 10px;
}
.zoobbe-main-content .collaborators .workspace-members h3 .icon-copy {
  margin-left: 10px;
  width: 16px;
  height: 16px;
}
.zoobbe-main-content .collaborators .workspace-members p {
  margin-bottom: 20px;
  color: #bbb;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.6;
}
.zoobbe-main-content .collaborators .workspace-members .invite {
  margin-bottom: 20px;
}
.zoobbe-main-content .collaborators .workspace-members .invite h4 {
  font-size: 1rem;
  margin-bottom: 10px;
}
.zoobbe-main-content .collaborators .workspace-members .invite p {
  margin-bottom: 10px;
}
.zoobbe-main-content .collaborators .workspace-members .invite .invite-link-button {
  background-color: var(--brand-color);
  color: var(--brand-btn-primary-color);
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-family: system-ui;
}
.zoobbe-main-content .collaborators .workspace-members .invite .invite-link-button span {
  font-size: 20px;
}
.zoobbe-main-content .collaborators .workspace-members .invite .invite-link-button:hover {
  background-color: #357ab8;
}
.zoobbe-main-content .collaborators .workspace-members .members-list input {
  width: 100%;
  padding: 0 10px;
  border-radius: 5px;
  border: 1px solid #2b2626;
  background-color: #121212;
  color: var(--white-text-color-alternative);
  margin-bottom: 20px;
  width: calc(100% - 20px);
  height: 35px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list input:focus {
  outline: 2px solid var(--brand-color);
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member {
  display: flex;
  align-items: center;
  padding: 15px 0;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member:last-child {
  border: none;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .profile-pic {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .image-placeholder {
  margin-right: 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .member-info {
  flex-grow: 1;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .member-info .member-name {
  display: block;
  margin-bottom: 2px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .member-info .member-bottom {
  display: flex;
  align-items: center;
  gap: 8px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .member-info .member-bottom .last-active,
.zoobbe-main-content .collaborators .workspace-members .members-list .member .member-info .member-bottom .username {
  font-size: 0.8rem;
  color: #888;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .view-boards-button {
  background-color: transparent;
  color: var(--single-card-text-color);
  padding: 0px 10px;
  border: none;
  border-radius: var(--element-border-radius);
  margin-right: 10px;
  cursor: pointer;
  transition: background 0.3s;
  height: 35px;
  font-size: 14px;
  display: flex;
  align-items: center;
  font-family: system-ui;
  outline: 1px solid var(--outline-color);
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .view-boards-button:hover {
  color: var(--brand-color);
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions {
  display: flex;
  align-items: center;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .admin-role {
  margin-right: 10px;
  font-weight: bold;
  margin-bottom: 0;
  width: 120px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .admin-role .zoobbe-select-trigger {
  padding: 0px 12px;
  background-color: transparent;
  font-weight: 400;
  border: none;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .admin-role .zoobbe-select-trigger span.arrow-down {
  top: 6px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .admin-role .zoobbe-select-trigger span {
  right: 10px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .admin-role .zoobbe-select-trigger.active {
  color: var(--brand-color);
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .admin-role .zoobbe-select-option {
  padding: 0px 12px;
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  font-family: system-ui;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .leave-button {
  background-color: transparent;
  color: var(--single-card-text-color);
  padding: 0px 8px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  transition: background 0.3s;
  height: 35px;
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 5px;
  padding-right: 10px;
  border-radius: var(--element-border-radius);
  outline: 1px solid var(--outline-color);
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .leave-button span {
  font-size: 16px;
}
.zoobbe-main-content .collaborators .workspace-members .members-list .member .admin-actions .leave-button:hover {
  background-color: #c4352c;
}
.zoobbe-main-content .collaborators .guests p,
.zoobbe-main-content .collaborators .join-requests p {
  margin-bottom: 20px;
  color: #bbb;
}

@media only screen and (max-width: 991px) {
  .zoobbe-main-content .collaborators {
    flex-direction: column;
  }
  .zoobbe-main-content .collaborators .tabs {
    flex-direction: row;
    width: 100%;
    margin: 0;
  }
}/*# sourceMappingURL=Collaborators.css.map */
.export-import-container {
  margin: 2rem;
  max-width: 500px;
  margin: auto;
  margin-top: 60px;
}
.export-import-container .zoobbe-select {
  margin-bottom: 20px;
}
.export-import-container .zoobbe-select .zoobbe-select-option {
  height: 40px;
}
.export-import-container .buttons {
  display: flex;
  gap: 1rem;
}
.export-import-container .buttons span.material-symbols-outlined {
  font-size: 20px;
}
.export-import-container .buttons button {
  padding: 2px 18px;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.export-import-container .buttons button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.export-import-container .buttons button.export-btn {
  background-color: #3498db;
  color: var(--color-white);
}
.export-import-container .buttons button.export-btn:hover:not(:disabled) {
  background-color: #2980b9;
}
.export-import-container .buttons button.import-btn {
  background-color: #2ecc71;
  color: var(--color-white);
}
.export-import-container .buttons button.import-btn:hover:not(:disabled) {
  background-color: #27ae60;
}
.export-import-container .buttons .import-label {
  position: relative;
  display: inline-block;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  background-color: #f39c12;
  color: var(--color-white);
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.export-import-container .buttons .import-label:hover {
  background-color: #e67e22;
}
.export-import-container .buttons .import-label input {
  display: none;
}
.export-import-container .uploaded-file-name {
  color: var(--white-text-color-alternative);
  text-align: left;
  width: 100%;
  margin-bottom: 20px;
  color: var(--primary-text-color);
}
.export-import-container .create-user-checkbox label {
  display: inline-flex;
  align-items: center;
  margin-bottom: 25px;
  gap: 5px;
  font-family: system-ui;
  font-weight: 600;
  color: #797a7c;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.select-workspace-to-import {
  width: 100%;
  color: #3498db;
}
.select-workspace-to-import label {
  align-self: flex-start;
  margin-bottom: 0.8rem;
  font-family: system-ui;
  font-weight: 600;
  color: #797a7c;
  margin-bottom: 10px;
  display: inline-block;
}
.select-workspace-to-import .zoobbe-select-trigger {
  height: 40px;
}
.select-workspace-to-import .zoobbe-select .zoobbe-select-trigger span.arrow-down {
  top: 8px;
}

.export-import-container .logs-container {
  margin-top: 20px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.export-import-container .logs-container .log-entry {
  margin-bottom: 20px;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}
.export-import-container .logs-container .log-entry:last-child {
  border-bottom: none;
}
.export-import-container .logs-container .log-entry .log-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--color-white);
}
.export-import-container .logs-container .log-entry .progress-bar {
  height: 20px;
  border-radius: 4px;
  background-color: #007bff;
  margin: 10px 0;
  color: white;
  line-height: 20px;
  text-align: center;
  transition: width 0.3s;
}
.export-import-container .logs-container .log-entry .log-details {
  font-size: 14px;
  color: #555;
}
.export-import-container .logs-container .log-entry .log-details strong {
  color: var(--scrolbar-thumb-background-color);
}

.progress-bar-container {
  display: flex;
  flex-direction: column;
  margin-top: 30px;
}
.progress-bar-container .progress-bar-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  justify-content: space-between;
}
.progress-bar-container .progress-bar-header .import-titles {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--primary-text-color);
  font-size: 15px;
  font-weight: 600;
  width: calc(100% - 100px);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
.progress-bar-container .progress-bar-header .import-titles .icon-bar {
  position: relative;
  /* width: 40px; */
  display: flex;
  color: green;
}
.progress-bar-container .progress-bar-header .import-titles .icon-bar .material-symbols-outlined.import-icon {
  position: absolute;
  left: 8px;
  top: 8px;
  color: rgba(52, 152, 219, 0.7019607843);
}
.progress-bar-container .progress-bar-header .import-titles .title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.progress-bar-container .progress-bar-header .import-titles.import-success {
  margin-bottom: 10px;
}
.progress-bar-container .progress-bar-header .items-imported {
  color: var(--primary-text-color);
  font-size: 15px;
  font-weight: 600;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
.progress-bar-container .progress-bar-header .file-icon {
  width: 24px;
  height: 24px;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin-right: 8px;
}
.progress-bar-container .progress-bar-header .file-size {
  margin-left: auto;
  font-size: 12px;
  color: #666;
}
.progress-bar-container .progress-bar {
  position: relative;
  width: 100%;
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
}
.progress-bar-container .progress-bar .progress-bar-fill {
  height: 100%;
  background-color: #6a0dad;
  transition: width 0.4s ease;
}
.progress-bar-container .progress-bar-percentage {
  margin-top: 4px;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  text-align: right;
  font-weight: 900;
}/*# sourceMappingURL=ExportImport.css.map */
.zppbbe-workspace-wrapper {
  background-color: var(--workspace-background-color);
  max-height: -moz-fit-content;
  max-height: fit-content;
}
.zppbbe-workspace-wrapper .zoobbe-workspace-container {
  display: flex;
  color: var(--primary-text-color);
  max-width: 1180px;
}

.zoobbe-sidebar {
  width: 250px;
  padding: 20px;
  max-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .quick-links {
  border-bottom: 1px solid var(--scrolbar-thumb-background-color);
}
.zoobbe-sidebar .zoobbe-sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  padding-bottom: 15px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav ul .zoobbe-nav-item a {
  padding: 10px;
  margin: 0;
  color: var(--white-text-color-alternative);
  display: block;
  text-decoration: none;
  border-radius: 12px;
  transition: 0.2s;
  display: flex;
  align-items: center;
  gap: 10px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav ul .zoobbe-nav-item a span {
  font-size: 20px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav ul .zoobbe-nav-item a:hover {
  background-color: #242728;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces {
  margin-top: 20px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces h3 {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
  padding: 12px;
  border-radius: 12px;
  transition: 0.2s;
  padding-top: 0;
  font-weight: 500;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces h3 span.material-symbols-outlined {
  font-size: 22px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace {
  margin-bottom: 10px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 12px;
  transition: 0.2s;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  margin-bottom: 5px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-header svg {
  height: 19px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-header .workspace-name {
  display: flex;
  align-items: center;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-header .zoobbe-workspace-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: var(--primary-text-color);
  font-weight: 700;
  font-size: 16px;
  background: #242628;
  color: var(--brand-color);
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-header .zoobbe-workspace-title {
  font-weight: bold;
  font-size: 14px;
  font-family: system-ui;
  color: var(--single-card-text-color);
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-header:hover {
  background: #242628;
  border-radius: 10px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-options {
  list-style: none;
  margin: 0;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-options li a {
  padding: 10px 52px;
  color: var(--single-card-text-color);
  display: flex;
  text-decoration: none;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
       user-select: none;
  align-items: center;
  gap: 10px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-options li a span {
  font-size: 16px;
}
.zoobbe-sidebar .zoobbe-sidebar-nav .zoobbe-workspaces .zoobbe-workspace .zoobbe-workspace-options li a:hover {
  background: #242728;
  border-radius: 10px;
}

.spinner-container {
  display: flex;
  justify-content: center;
  height: 200px;
  align-items: center;
}

.zoobbe-main-content {
  flex: 1;
  padding: 20px;
}
.zoobbe-main-content .zoobbe-recently-viewed {
  margin-bottom: 40px;
}
.zoobbe-main-content .zoobbe-recently-viewed h2 {
  font-size: 18px;
  margin-bottom: 20px;
  text-transform: uppercase;
}
.zoobbe-main-content .zoobbe-recently-viewed .zoobbe-board-card {
  padding: 10px 40px;
  display: inline-flex;
  background: var(--scrolbar-thumb-background-color);
  text-decoration: none;
  color: #ddd;
  height: 50px;
  border-radius: 7px;
  align-items: center;
}
.zoobbe-main-content .zoobbe-your-workspaces,
.zoobbe-main-content .zoobbe-guest-workspaces {
  margin-top: 20px;
}
.zoobbe-main-content .zoobbe-your-workspaces h2,
.zoobbe-main-content .zoobbe-guest-workspaces h2 {
  font-size: 18px;
  margin-bottom: 20px;
  text-transform: uppercase;
  color: var(--single-card-text-color);
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px 0;
  border-radius: 5px;
  margin-bottom: 30px;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-list,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-list {
  margin-left: 30px;
  display: grid;
  gap: 15px;
  grid-template-columns: repeat(3, 1fr);
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-list .board-tile,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-list .board-tile {
  padding: 10px 40px;
  display: flex;
  background: var(--scrolbar-thumb-background-color);
  text-decoration: none;
  color: #ddd;
  height: 50px;
  border-radius: 7px;
  align-items: center;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-list .create-board,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-list .create-board {
  padding: 10px 20px;
  display: flex;
  background: #171b1e;
  border: 2px solid #1c2023;
  text-decoration: none;
  color: #ddd;
  height: 50px;
  border-radius: 7px;
  align-items: center;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  text-align: center;
  text-align: center;
  min-height: 100px;
  /* width: 80px; */
  justify-content: center;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-list .create-board span.material-symbols-outlined,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-list .create-board span.material-symbols-outlined {
  font-size: 30px;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-header,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-header .zoobbe-workspace-actions,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-header .zoobbe-workspace-actions {
  display: flex;
  gap: 8px;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-header .zoobbe-workspace-actions a,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-header .zoobbe-workspace-actions a {
  background-color: #171b1f;
  border-radius: 3px;
  color: var(--single-card-text-color);
  display: inline-block;
  font-weight: 500;
  padding: 8px 12px;
  position: relative;
  text-decoration: none;
  font-family: system-ui;
  font-size: 14px;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .zoobbe-workspace-card,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .zoobbe-workspace-card {
  display: flex;
  align-items: center;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .zoobbe-workspace-card .zoobbe-workspace-icon,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .zoobbe-workspace-card .zoobbe-workspace-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: var(--primary-text-color);
  font-weight: 700;
  font-size: 16px;
  background: #242628;
  color: var(--brand-color);
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .zoobbe-workspace-card .zoobbe-workspace-title,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .zoobbe-workspace-card .zoobbe-workspace-title {
  font-weight: bold;
  color: var(--single-card-text-color);
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .zoobbe-workspace-actions button,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .zoobbe-workspace-actions button {
  margin-left: 10px;
  background: none;
  border: none;
  color: var(--single-card-text-color);
  padding: 5px 10px;
  font-family: system-ui;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .zoobbe-workspace-actions button:hover,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .zoobbe-workspace-actions button:hover {
  background-color: #5a5a5a;
  border-radius: 5px;
}
.zoobbe-main-content .zoobbe-your-workspaces .zoobbe-guest-workspaces .zoobbe-workspace-card,
.zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-guest-workspaces .zoobbe-workspace-card {
  background-color: #3a3a3a;
  padding: 20px;
  border-radius: 5px;
}

@media screen and (max-width: 1120px) {
  .zoobbe-main-content .zoobbe-your-workspaces .zoobbe-workspace-section .boards-page-board-section-list,
  .zoobbe-main-content .zoobbe-guest-workspaces .zoobbe-workspace-section .boards-page-board-section-list {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}/*# sourceMappingURL=workspace.css.map */
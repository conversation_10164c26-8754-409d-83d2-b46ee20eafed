.boards-page-board-section-list-item {
  background-color: #171b1e;
  border: 2px solid #1c2023;
  border-radius: 10px;
  min-height: 100px;
  padding: 10px;
  position: relative;
  -o-object-fit: cover;
     object-fit: cover;
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden;
}
.boards-page-board-section-list-item .board-gradient {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1; /* Ensure it's above the background but below content */
  transition: 0.2s;
}
.boards-page-board-section-list-item:hover .board-gradient {
  opacity: 0.5;
}
.boards-page-board-section-list-item .board-tile-link {
  position: relative; /* Ensures child elements appear above the overlay */
  z-index: 2;
}
.boards-page-board-section-list-item .board-title {
  z-index: 1;
}
.boards-page-board-section-list-item .board-tile-link {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.boards-page-board-section-list-item .board-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100px;
}
.boards-page-board-section-list-item .board-item .board-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 30px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: var(--single-card-text-color);
}
.boards-page-board-section-list-item .board-item .board-details {
  display: flex;
  justify-content: space-between;
  align-items: end;
  margin-bottom: -50px;
  transition: 0.2s;
}
.boards-page-board-section-list-item .board-item .board-details .board-stats span.material-symbols-outlined {
  font-size: 16px;
  margin-right: 5px;
  color: #fff;
}
.boards-page-board-section-list-item .board-item .board-details .board-members {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 3;
}
.boards-page-board-section-list-item .board-item .board-details .board-members .avatar {
  transition: 0.2s;
}
.boards-page-board-section-list-item .board-item .board-details .board-members .avatar .image-placeholder {
  color: var(--color-white) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
}
.boards-page-board-section-list-item .board-item .board-details .board-members .members-info {
  gap: 3;
}
.boards-page-board-section-list-item .board-item .board-details .board-members .member-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: -10px;
}
.boards-page-board-section-list-item .board-item .board-details .board-members .member-avatar img {
  width: 100%;
  height: 100%;
}
.boards-page-board-section-list-item .board-item .board-details .board-members .member-count {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  z-index: 2;
  cursor: pointer;
  color: var(--color-white);
}
.boards-page-board-section-list-item .board-item .board-details .board-stats {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  z-index: 1;
}
.boards-page-board-section-list-item .board-item .board-details .board-stats .stats-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  fill: #666;
}

.boards-page-board-section-list-item:hover .board-details {
  margin: 0;
}/*# sourceMappingURL=BaordSection.css.map */
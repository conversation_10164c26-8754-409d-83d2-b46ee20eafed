.zoobbe-profile {
  width: 300px;
  background-color: var(--popover-background-color);
  border-radius: 8px;
  position: absolute;
  right: -20px;
  top: 40px;
  z-index: 4;
  box-shadow: var(--popover-box-shadow);
  border: 1px solid var(--popover-border-color);
}
.zoobbe-profile .account-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px 0 0px 20px;
  gap: 5px;
}
.zoobbe-profile .account-section .profile-picture {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 10px;
}
.zoobbe-profile .account-section .account-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.zoobbe-profile .account-section .account-info .username {
  font-weight: bold;
  margin: 0;
  color: #cfd2d6;
}
.zoobbe-profile .account-section .account-info .email {
  margin: 0;
  color: #aaaaaa;
}
.zoobbe-profile .manage-account-section {
  border-bottom: 1px solid var(--popover-border-color);
  margin-bottom: 20px;
  padding: 0 20px 20px;
}
.zoobbe-profile .manage-account-section .manage-account {
  display: block;
  margin-top: 5px;
  color: #0094ff;
  text-decoration: none;
}
.zoobbe-profile .menu-section,
.zoobbe-profile .other-section {
  border-bottom: 1px solid var(--popover-border-color);
}
.zoobbe-profile .menu-section .menu-title,
.zoobbe-profile .other-section .menu-title {
  font-weight: bold;
  margin-bottom: 10px;
  padding: 0 20px;
  color: #cfd2d6;
}
.zoobbe-profile .menu-section ul,
.zoobbe-profile .other-section ul {
  list-style: none;
  padding: 10px 0;
  margin: 0;
}
.zoobbe-profile .menu-section ul li a,
.zoobbe-profile .other-section ul li a {
  color: var(--single-card-text-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  font-family: system-ui;
  display: block;
  padding: 10px 20px;
}
.zoobbe-profile .menu-section ul li a:hover,
.zoobbe-profile .other-section ul li a:hover {
  background-color: #272727;
}
.zoobbe-profile .other-section {
  margin-bottom: 0px;
}
.zoobbe-profile .logout-section .menu-title {
  font-weight: bold;
  margin-bottom: 10px;
  padding: 0 20px;
}
.zoobbe-profile .logout-section ul {
  list-style: none;
  padding: 10px 0;
  margin: 0;
}
.zoobbe-profile .logout-section ul li a {
  color: var(--single-card-text-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  font-family: system-ui;
  display: block;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.zoobbe-profile .logout-section ul li a span.material-symbols-outlined {
  font-size: 18px;
}
.zoobbe-profile .logout-section ul li a:hover {
  color: #0094ff;
  background-color: var(--scrolbar-thumb-background-color);
}
.zoobbe-profile .workspace-section .create-workspace,
.zoobbe-profile .workspace-section .export-import-board {
  color: #0094ff;
  text-decoration: none;
  background: none;
  box-shadow: none;
  border: none;
  font-size: 15px;
  font-weight: 600;
  width: 100%;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-left: 0;
  padding: 20px;
}
.zoobbe-profile .workspace-section .create-workspace span.material-symbols-outlined,
.zoobbe-profile .workspace-section .export-import-board span.material-symbols-outlined {
  font-size: 16px;
  font-weight: 600;
}
.zoobbe-profile .workspace-section a.export-import-board {
  padding: 15px 20px;
}
.zoobbe-profile .other-section ul {
  border-top: 1px solid var(--popover-background-color);
  padding: 5px 0;
  margin: 0;
}
.zoobbe-profile .other-section ul li a {
  color: var(--single-card-text-color);
  text-decoration: none;
}
.zoobbe-profile .other-section ul li a:hover {
  color: #0094ff;
}

.profile-page {
  display: flex;
  color: var(--white-text-color-alternative);
  background-color: var(--workspace-background-color);
  flex-direction: column;
}
.profile-page .bottom-profile-content {
  display: flex;
  width: 600px;
  margin: 30px auto;
}
.profile-page .sidebar {
  width: 220px;
  background-color: #2c2c2e;
  padding: 20px 0;
  border-radius: 8px;
}
.profile-page .sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.profile-page .sidebar ul li a {
  color: var(--white-text-color-alternative);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  font-family: system-ui;
  display: block;
  padding: 12px 20px;
  text-decoration: none;
}
.profile-page .sidebar ul li a:hover {
  color: #0094ff;
  background-color: var(--scrolbar-thumb-background-color);
}
.profile-page .content {
  flex-grow: 1;
  padding: 20px;
  width: calc(100% - 220px);
}
.profile-page .content h1 {
  font-size: 24px;
  margin-bottom: 10px;
  margin-top: 0;
  color: var(--primary-text-color);
}
.profile-page .content p {
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.6;
  color: var(--primary-text-color);
}
.profile-page .content a {
  color: #1a73e8;
  text-decoration: none;
}
.profile-page .content a:hover {
  text-decoration: underline;
}
.profile-page .content .about-section h2 {
  font-size: 18px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--scrolbar-thumb-background-color);
  padding-bottom: 20px;
  color: var(--primary-text-color);
}
.profile-page .content .about-section .form-group {
  margin-bottom: 15px;
}
.profile-page .content .about-section .form-group label {
  display: block;
  font-size: 14px;
  margin-bottom: 12px;
  color: var(--primary-text-color);
  font-weight: 700;
}
.profile-page .content .about-section .form-group input,
.profile-page .content .about-section .form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #222;
  border-radius: var(--element-border-radius);
  background-color: var(--workspace-background-color);
  color: var(--primary-text-color);
  height: 20px;
  font-family: system-ui;
}
.profile-page .content .about-section .form-group input:focus,
.profile-page .content .about-section .form-group textarea:focus {
  outline: 2px solid var(--brand-color);
}
.profile-page .content .about-section .form-group textarea {
  height: 100px;
}
.profile-page .content .about-section .form-group .profile-picture {
  padding: 20px 0;
}
.profile-page .content .about-section .form-group .profile-picture img {
  width: 180px;
  height: 180px;
  -o-object-fit: cover;
     object-fit: cover;
}
.profile-page .content .about-section button {
  padding: 0px 20px;
  border: none;
  border-radius: var(--element-border-radius);
  background-color: var(--brand-color);
  color: var(--color-white);
  cursor: pointer;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  font-weight: 600;
}
.profile-page .content .about-section button:hover {
  background-color: var(--hover-brand-color);
}
.profile-page .content .about-section .choose-file-button {
  gap: 5px;
  font-weight: 500;
  background-color: transparent;
  padding: 0;
  justify-content: start;
  margin-top: 20px;
  color: var(--primary-text-color);
  max-width: 135px;
  align-items: center;
  display: flex;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}
.profile-page .content .about-section .choose-file-button:hover {
  background-color: transparent;
}
.profile-page .content .about-section .choose-file-button span {
  font-size: 16px;
}/*# sourceMappingURL=index.css.map */
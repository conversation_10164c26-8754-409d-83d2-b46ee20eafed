.zoobbe-settings-content {
  max-width: 900px;
  margin: 0 auto;
}
.zoobbe-settings-content .board-header {
  display: flex;
  align-items: center;
  background-color: #181818;
  padding: 10px;
  border-radius: 8px;
  color: var(--single-card-text-color);
  margin: 30px 0;
}
.zoobbe-settings-content .board-header .board-icon {
  background-color: #f05a28;
  border-radius: var(--element-border-radius);
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.zoobbe-settings-content .board-header .board-icon .icon-text {
  color: var(--single-card-text-color);
  font-size: 24px;
  font-weight: bold;
}
.zoobbe-settings-content .board-header .board-details {
  margin-left: 10px;
}
.zoobbe-settings-content .board-header .board-details .board-name {
  display: flex;
  align-items: center;
}
.zoobbe-settings-content .board-header .board-details .board-name span {
  font-size: 20px;
  font-weight: 600;
}
.zoobbe-settings-content .board-header .board-details .board-name span.material-symbols-outlined {
  margin-left: 5px;
  cursor: pointer;
  color: #bbbbbb;
  transform: rotate(-15deg);
}
.zoobbe-settings-content .board-header .board-details .board-visibility {
  display: flex;
  align-items: center;
  margin-top: 5px;
}
.zoobbe-settings-content .board-header .board-details .board-visibility span.material-symbols-outlined {
  font-size: 14px !important;
  margin-right: 4px;
}
.zoobbe-settings-content .board-header .board-details .board-visibility span {
  font-size: 14px;
  color: #bbbbbb;
}
.zoobbe-settings-content .settings-panel {
  padding: 20px;
  background-color: #1e1e1e;
  color: var(--white-text-color-alternative);
}
.zoobbe-settings-content .settings-panel .section {
  margin-bottom: 20px;
}
.zoobbe-settings-content .settings-panel .section h2 {
  font-size: 18px;
  margin-bottom: 10px;
  color: var(--single-card-text-color);
}
.zoobbe-settings-content .settings-panel .section .restriction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #2c2c2c;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 400;
}
.zoobbe-settings-content .settings-panel .section .restriction p {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: var(--single-card-text-color);
}
.zoobbe-settings-content .settings-panel .section .restriction button {
  background-color: #4a4a4a;
  border: none;
  color: var(--single-card-text-color);
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
}
.zoobbe-settings-content .settings-panel .section .restriction button:hover {
  background-color: #6a6a6a;
}
.zoobbe-settings-content .settings-panel .section .link-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #2c2c2c;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 10px;
}
.zoobbe-settings-content .settings-panel .section .link-section p {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: var(--single-card-text-color);
}
.zoobbe-settings-content .settings-panel .section .link-section .add-to-slack {
  background-color: #4a4a4a;
  border: none;
  color: var(--white-text-color-alternative);
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.zoobbe-settings-content .settings-panel .section .link-section .add-to-slack:hover {
  background-color: #6a6a6a;
}
.zoobbe-settings-content .settings-panel .section .link-section .add-to-slack::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  background-size: contain;
  margin-right: 5px;
}
.zoobbe-settings-content .settings-panel .delete-section a {
  color: #ff4c4c;
  text-decoration: none;
}
.zoobbe-settings-content .settings-panel .delete-section a:hover {
  text-decoration: underline;
}/*# sourceMappingURL=Settings.css.map */
.create-workspace-form,
.create-board-form {
  background-color: var(--popover-background-color);
  color: var(--white-text-color-alternative);
  padding: 20px;
  border-radius: 8px;
  width: 350px;
  margin: 0 auto;
  text-align: center;
}
.create-workspace-form .background-selector,
.create-board-form .background-selector {
  display: flex;
  gap: 7px;
}
.create-workspace-form .background-selector img,
.create-board-form .background-selector img {
  width: 70px;
  height: 45px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 5px;
}
.create-workspace-form h1,
.create-board-form h1 {
  font-size: 12px;
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-text-color);
}
.create-workspace-form .group,
.create-board-form .group {
  width: 100%;
  text-align: left;
  margin-bottom: 20px;
}
.create-workspace-form .group input#board-title,
.create-workspace-form .group input#workspace-name,
.create-board-form .group input#board-title,
.create-board-form .group input#workspace-name {
  margin-bottom: 10px;
}
.create-workspace-form .group input,
.create-workspace-form .group select,
.create-workspace-form .group textarea,
.create-board-form .group input,
.create-board-form .group select,
.create-board-form .group textarea {
  margin: 0;
}
.create-workspace-form .group p,
.create-board-form .group p {
  margin: 0;
}
.create-workspace-form .group .board-preview,
.create-board-form .group .board-preview {
  height: 160px;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  text-align: center;
  align-content: center;
  position: relative;
  background-position: center;
}
.create-workspace-form .group .board-preview img,
.create-board-form .group .board-preview img {
  width: calc(100% - 70px);
  height: auto;
  z-index: 1;
  position: relative;
}
.create-workspace-form .group .board-preview::after,
.create-board-form .group .board-preview::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  z-index: 0;
}
.create-workspace-form p,
.create-board-form p {
  margin-bottom: 2rem;
  font-size: 0.9rem;
  font-family: system-ui;
  font-weight: 400;
}
.create-workspace-form p.error-message,
.create-board-form p.error-message {
  margin-bottom: 0;
}
.create-workspace-form form,
.create-board-form form {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.create-workspace-form form .group h3,
.create-board-form form .group h3 {
  font-size: 12px;
  margin-top: 0;
}
.create-workspace-form form label,
.create-board-form form label {
  align-self: flex-start;
  margin-bottom: 0.8rem;
  font-family: system-ui;
  font-weight: 600;
  color: #797a7c;
  font-size: 0.8rem;
}
.create-workspace-form form input,
.create-workspace-form form select,
.create-workspace-form form textarea,
.create-board-form form input,
.create-board-form form select,
.create-board-form form textarea {
  width: calc(100% - 30px);
  padding: 0 15px;
  margin-bottom: 1.4rem;
  border: 1px solid var(--scrolbar-thumb-background-color);
  border-radius: var(--element-border-radius);
  background-color: #111;
  color: var(--white-text-color-alternative);
  height: 35px;
  font-family: system-ui;
}
.create-workspace-form form input::-moz-placeholder, .create-workspace-form form select::-moz-placeholder, .create-workspace-form form textarea::-moz-placeholder, .create-board-form form input::-moz-placeholder, .create-board-form form select::-moz-placeholder, .create-board-form form textarea::-moz-placeholder {
  color: #666;
}
.create-workspace-form form input::placeholder,
.create-workspace-form form select::placeholder,
.create-workspace-form form textarea::placeholder,
.create-board-form form input::placeholder,
.create-board-form form select::placeholder,
.create-board-form form textarea::placeholder {
  color: #666;
}
.create-workspace-form form input:focus-visible, .create-workspace-form form input:focus,
.create-workspace-form form select:focus-visible,
.create-workspace-form form select:focus,
.create-workspace-form form textarea:focus-visible,
.create-workspace-form form textarea:focus,
.create-board-form form input:focus-visible,
.create-board-form form input:focus,
.create-board-form form select:focus-visible,
.create-board-form form select:focus,
.create-board-form form textarea:focus-visible,
.create-board-form form textarea:focus {
  outline: 2px solid var(--brand-color);
}
.create-workspace-form form select#workspace-type,
.create-board-form form select#workspace-type {
  width: 100%;
  cursor: pointer;
}
.create-workspace-form form textarea,
.create-board-form form textarea {
  height: 80px;
  padding: 12px;
}
.create-workspace-form form button,
.create-board-form form button {
  background-color: var(--brand-color);
  color: var(--brand-btn-primary-color);
  border: none;
  border-radius: var(--element-border-radius);
  cursor: pointer;
  width: 100%;
  padding: 0;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.create-workspace-form form button:hover,
.create-board-form form button:hover {
  background-color: #135bb7;
}

.create-board-form,
.create-workspace-form {
  width: 300px;
}

.background-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}
.background-selector .background-option {
  position: relative;
  width: 100%;
  height: 40px;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.2s ease;
  background-repeat: no-repeat;
}
.background-selector .background-option:hover {
  outline: 2px solid var(--brand-color);
}
.background-selector .background-option.selected {
  outline: 2px solid var(--brand-color);
}
.background-selector .background-option.selected .checkmark {
  display: block;
}
.background-selector .background-option .checkmark {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  background: var(--brand-color);
  color: #fff;
  font-size: 14px;
  line-height: 20px;
  border-radius: 50%;
  text-align: center;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.background-selector.colors {
  margin-bottom: 0;
}
.background-selector.colors .background-option {
  height: 30px;
  border-radius: 4px;
}

.create-workspace-form {
  border-radius: 8px 0 0 8px;
  padding: 35px;
  text-align: left;
}
.create-workspace-form h1 {
  font-size: 20px;
  margin-bottom: 10px;
}
.create-workspace-form p {
  font-size: 1rem;
  margin-top: 0;
  line-height: 1.4em;
  margin-bottom: 20px;
}
.create-workspace-form p.error-message {
  font-size: 14px;
}
.create-workspace-form input,
.create-workspace-form textarea {
  font-size: 15px;
  font-weight: 500;
  color: rgba(190, 199, 210, 0.631372549);
}
.create-workspace-form .zoobbe-select .zoobbe-select-trigger {
  color: rgba(190, 199, 210, 0.631372549);
}

.workspace-container {
  display: flex;
}
.workspace-container .workspace-right-content {
  width: 320px;
  max-height: 100%;
  background: url(https://trello.com/assets/df0d81969c6394b61c0d.svg);
  background-color: #181717;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 8px 8px 0;
}
.workspace-container .workspace-right-content img {
  width: calc(100% - 50px);
  height: auto;
  margin-top: -200px;
}/*# sourceMappingURL=CreateWorkspace.css.map */
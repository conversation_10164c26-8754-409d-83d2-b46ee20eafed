import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { find, getTextColor, toSlug } from "../../utils/helpers";
import { useDispatch, useSelector } from "react-redux";
import { openModal } from "../../redux/Slices/modalSlice";

import './scss/BaordSection.scss'
import MemberImage from "../Global/MemberImage";
import StarFill from "../icons/StarFill";
import { fetchStarredBoards } from "../../redux/Slices/boardsSlice";
import { config } from "../../config";
import { fetchUser } from "../../redux/Slices/thunks";
import MoreMembersCount from "../Global/MoreMembersCount";

const TopBoards = ({ type, boards }) => {

    const dispatch = useDispatch();

    const { workspaces } = useSelector(state => state.workspaces);


    const addTransparency = (rgbColor, alpha = 1) => {
        // Ensure the RGB color is in the correct format (e.g., "rgb(255, 0, 0)")
        const match = rgbColor?.match(/\d+/g);
        if (!match || match.length < 3) {
            return rgbColor; // Return original if format is invalid
        }

        const [r, g, b] = match;
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    const { user } = useSelector((state) => state.user);
    const isUserLoaded = user && user.user;

    // Toggle star status
    const toggleStar = async (boardId, shortId) => {
        if (!isUserLoaded) {
            console.warn('User data not loaded yet');
            return;
        }

        try {
            const token = localStorage.getItem('accessToken');
            const isStarred = user.user.starredBoards?.includes(boardId);
            const action = isStarred ? 'remove' : 'add';

            const response = await fetch(`${config.API_URI}/api/users/me/stared`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                credentials: 'include',
                body: JSON.stringify({ boardId: shortId, action }),
            });

            if (response.ok) {
                dispatch(fetchStarredBoards()); // Refresh starred boards in the redux store
                dispatch(fetchUser()); // Refresh starred boards in the redux store
            }
        } catch (error) {
            console.error('Error toggling star status:', error);
        }
    };

    return (
        <div className="boards-page-board-section-list">
            {boards?.map(board => {
                const members = (board.members).filter(member => member._id);

                const coverColor =
                    Array.isArray(board?.cover?.coverColor) && board.cover.coverColor.length > 2
                        ? board.cover.coverColor[2]
                        : 'transparent';

                const opacity = '0.6';


                return (
                    <div
                        className="boards-page-board-section-list-item"
                        key={board._id}
                        style={{
                            backgroundImage: `url(${board?.cover?.url})`,
                            backgroundSize: 'cover', // Ensures the image covers the full container
                            backgroundPosition: 'center center', // Centers the image
                            // borderColor: `${coverColor ? addTransparency(coverColor, opacity) : 'transparent'}`

                        }}
                    >
                        {
                            coverColor && (
                                <div
                                    className="board-gradient"
                                    style={{
                                        background: `${coverColor ? addTransparency(coverColor, opacity) : 'transparent'}`
                                    }}
                                ></div>
                            )
                        }

                        <div className="board-item">

                            <div className="top-stats">
                                <div className="board-title" style={{ color: `#fff` }}> {board.title}</div>

                                <div className="zoobbe-star" onClick={() => { toggleStar(board._id, board.shortId) }}>
                                    {isUserLoaded && (
                                        <>
                                            {!user.user.starredBoards.includes(board._id) ? (
                                                <span className="material-symbols-outlined">
                                                    kid_star
                                                </span>
                                            ) : (
                                                <StarFill
                                                    color={user.user.starredBoards.includes(board._id) ? '#FFD700' : '#fff'}
                                                />
                                            )}
                                        </>
                                    )}
                                </div>
                            </div>


                            <div className="board-details">
                                <div className="board-members">
                                    <MemberImage members={members} type={'board'} size={30} />
                                    <MoreMembersCount members={members} boardId={board.shortId} uniqid={board.shortId + '-starred'} />

                                </div>
                                <div className="board-stats" style={{ color: `#fff` }}>
                                    <span className="material-symbols-outlined" style={{ color: `#fff` }}>
                                        credit_card
                                    </span>
                                    <span>{board.cardCount}</span>
                                </div>
                            </div>
                        </div>
                        <Link className="board-tile-link" to={board.permalink}></Link>
                    </div>
                )
            })}
        </div>
    )
}

export default TopBoards;
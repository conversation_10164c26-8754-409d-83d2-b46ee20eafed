import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { find, getTextColor, toSlug, uniqid } from "../../utils/helpers";
import { useDispatch, useSelector } from "react-redux";
import { openModal } from "../../redux/Slices/modalSlice";

import './scss/BaordSection.scss'
import MemberImage from "../Global/MemberImage";
import StarFill from "../icons/StarFill";
import { config } from "../../config";
import { fetchStarredBoards } from "../../redux/Slices/boardsSlice";
import { fetchUser } from "../../redux/Slices/thunks";
import MoreMembersCount from "../Global/MoreMembersCount";
import { api } from "../../utils/apiUtils";

const BoardSection = ({ workspace, defaultSelectedOption, isGuests }) => {
    const { boards } = workspace;
    const [displayedBoards, setDisplayedBoards] = useState(boards || []);

    const dispatch = useDispatch();
    const { user } = useSelector((state) => state.user);
    const isUserLoaded = user && user.user;

    const handleCreateBoard = (defaultSelectedOption) => {
        dispatch(openModal({ modalType: 'CREATE_BOARD', modalData: { defaultSelectedOption } }));
    };

    // Function to add transparency to RGB colors
    const addTransparency = (rgbColor, alpha = 1) => {
        // Ensure the RGB color is in the correct format (e.g., "rgb(255, 0, 0)")
        const match = rgbColor?.match(/\d+/g);
        if (!match || match.length < 3) {
            return rgbColor; // Return original if format is invalid
        }

        const [r, g, b] = match;
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    };

    // Optimized toggle star function using our API utility
    const toggleStar = async (boardId, shortId) => {
        if (!isUserLoaded) {
            console.warn('User data not loaded yet');
            return;
        }

        try {
            const isStarred = user.user.starredBoards?.includes(boardId);
            const action = isStarred ? 'remove' : 'add';

            // Use our optimized API utility
            await api.put(`${config.API_URI}/api/users/me/stared`,
                { boardId: shortId, action }
            );

            // Refresh data in Redux store
            dispatch(fetchStarredBoards());
            dispatch(fetchUser());
        } catch (error) {
            console.error('Error toggling star status:', error);
        }
    };


    // Initialize with the boards from props
    useEffect(() => {
        setDisplayedBoards(boards || []);
    }, [boards]);

    // Render board items
    const renderBoardItem = (board) => {
        const members = (board.members || []).filter(member => member._id);
        const cardsCount = board.cardsCount || 0;
        const wCardsCount = workspace.cardsCount || 0;
        const coverColor =
            Array.isArray(board?.cover?.coverColor) && board.cover.coverColor.length > 2
                ? board.cover.coverColor[2]
                : 'transparent';
        const opacity = '0.6';
        const displayMembersLimit = 3;

        return (
            <div
                className="boards-page-board-section-list-item"
                key={board._id}
                style={{
                    backgroundImage: `url(${board?.cover?.sizes?.medium || board?.cover?.url})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center center',
                }}
            >
                {coverColor && (
                    <div
                        className="board-gradient"
                        style={{
                            background: `${coverColor ? addTransparency(coverColor, opacity) : 'transparent'}`
                        }}
                    ></div>
                )}

                <div className="board-item">
                    <div className="top-stats">
                        <div className="board-title" style={{ color: `#fff` }}>{board.title}</div>

                        <div
                            className="zoobbe-star"
                            onClick={() => { toggleStar(board._id, board.shortId) }}
                        >
                            {isUserLoaded && (
                                <>
                                    {!user.user.starredBoards.includes(board._id) ? (
                                        <span className="material-symbols-outlined">
                                            kid_star
                                        </span>
                                    ) : (
                                        <StarFill
                                            color={user.user.starredBoards.includes(board._id) ? '#FFD700' : '#fff'}
                                        />
                                    )}
                                </>
                            )}
                        </div>
                    </div>

                    <div className="board-details">
                        <div className="board-members">
                            <MemberImage members={members} type={'board'} size={30} displayMembersLimit={displayMembersLimit} />
                            <MoreMembersCount members={members} boardId={board.shortId} uniqid={board.shortId} />
                        </div>
                        <div className="board-stats" style={{ color: `#fff` }}>
                            <span className="material-symbols-outlined" style={{ color: `#fff` }}>
                                credit_card
                            </span>
                            {!isGuests ? (
                                <span>{cardsCount} / {wCardsCount}</span>
                            ) : (
                                <span>{cardsCount}</span>
                            )}
                        </div>
                    </div>
                </div>
                <Link className="board-tile-link" to={board.permalink}></Link>
            </div>
        );
    };

    return (
        <div className="boards-page-board-section-list">
            {/* Render boards */}
            {displayedBoards.map(renderBoardItem)}

            {/* Create board button */}
            {!isGuests && (
                <div className="create-board" onClick={() => { handleCreateBoard(defaultSelectedOption) }}>
                    <span className="material-symbols-outlined">
                        add
                    </span>
                </div>
            )}
        </div>
    )
}

export default BoardSection;
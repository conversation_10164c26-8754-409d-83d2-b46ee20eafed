import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { io } from 'socket.io-client'; // Import socket.io-client
import "./scss/ExportImport.scss";
import ZoobbeSelect from '../Global/ZoobbeSelect';
import { config } from '../../config';
import Checkbox from '../Global/Checkbox';
import Spinner from '../Global/Spinner';

import { fetchWorkspaces } from '../../redux/Slices/workspaceSlice';
import { fetchUser } from '../../redux/Slices/thunks';
import { hideSnackBar, showSnackBar } from '../../redux/Slices/snackbarSlice';


const API_KEY = config.TRELLO_AUTH_API_KEY;
const APP_NAME = "ZoobbeImport";
const SCOPE = "read";
const EXPIRATION = "never";
const CALLBACK_URL = window.location.origin + "/import-export/";

export default function TrelloImport() {
    const [token, setToken] = useState();
    const [boards, setBoards] = useState([]);
    const [selectedBoards, setSelectedBoards] = useState({});
    const [inviteMembers, setInviteMembers] = useState(false);

    const [importFile, setImportFile] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isChecked, setIsChecked] = useState(false);
    const [log, setLog] = useState([]); // Store multiple logs
    const [progress, setProgress] = useState(0); // State for progress message
    const [importSuccess, setImportSuccess] = useState(false); // New state for import success
    const [isImportStart, setImportStart] = useState(false); // New state for import success

    const dispatch = useDispatch();

    const { workspaces } = useSelector(state => state.workspaces);
    const { user } = useSelector(state => state.user);

    useEffect(() => {
        // Extract Trello token from URL fragment
        const urlParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = urlParams.get("token");

        console.log({ accessToken });

        if (accessToken) {
            setToken(accessToken);
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }, []);

    useEffect(() => {
        // Automatically fetch boards if token is available
        if (token) {
            fetchBoards();
        }
    }, [token]);

    const authenticateTrello = () => {
        const authUrl = `https://trello.com/1/authorize?expiration=${EXPIRATION}&name=${APP_NAME}&scope=${SCOPE}&response_type=token&key=${API_KEY}&return_url=${CALLBACK_URL}`;
        window.location.href = authUrl;
    };

    const fetchBoards = async () => {
        try {
            const response = await fetch(`https://api.trello.com/1/members/me/boards?key=${API_KEY}&token=${token}`);
            const data = await response.json();
            setBoards(data);
        } catch (error) {
            console.error("Error fetching boards:", error);
        }
    };

    const toggleBoardSelection = (boardId) => {
        setSelectedBoards((prev) => ({
            ...prev,
            [boardId]: !prev[boardId],
        }));
    };


    const importBoards = async () => {
        const selectedBoardIds = Object.keys(selectedBoards).filter((id) => selectedBoards[id]);

        console.log({ selectedBoardIds });

        if (selectedBoardIds.length === 0) {
            alert("Please select at least one board to import.");
            return;
        }

        try {
            const response = await fetch(`${config.API_URI}/api/trello/import`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                credentials: 'include',
                body: JSON.stringify({
                    boards: selectedBoardIds,
                    inviteMembers,
                    workspaceId: selectedWorkspace?.value,
                    token
                }),
            });
            const result = await response.json();
            alert(result.message || "Import completed");
        } catch (error) {
            console.error("Error importing boards:", error);
        }
    };

    useEffect(() => {
        if (!user) {
            dispatch(fetchUser());
        }
    }, [dispatch, user]);

    const workspaceOptions = workspaces?.map(workspace => ({
        value: workspace._id.toString(),
        label: workspace.name,
    }));

    const visibilityOptions = [
        { value: 'workspace', label: 'Workspace' },
        { value: 'public', label: 'Public' },
        { value: 'private', label: 'Private' },
    ];
    const importFromOptions = [
        { value: 'trello', label: 'Trello' },
        // { value: 'fluent-boards', label: 'Fluent Boards' },
    ];

    const [selectedWorkspace, setSelectedWorkspace] = useState(workspaceOptions[0]);
    const [selectedVisibility, setSelectedVisibility] = useState(visibilityOptions[0]);
    const [importFromSelect, setImportFromSelect] = useState(importFromOptions[0]);

    // Socket initialization
    useEffect(() => {
        const socket = io(config.API_URI); // Update with your socket server URI
        if (user) {
            const userId = user?.user?._id;
            socket.emit('join', userId);


            socket.on('importProgress', (data) => {
                setImportStart(true);
                dispatch(hideSnackBar());
                setProgress(data.progress);
                setLog(data);

                // Mark as successful when import reaches 100%
                if (data.progress >= 100) {
                    setImportSuccess(true);
                }

                console.log('New log data:', data);
            });


            console.log('Socket initialized for user:', userId);
        }

        return () => {
            socket.disconnect();
        };
    }, [user]);

    const handleWorkspaceSelect = (workspace) => {
        setSelectedWorkspace(workspace);
    };

    const handleVisibilitySelect = (visibility) => {
        setSelectedVisibility(visibility);
    };

    const handleImportFromSelect = (importFrom) => {
        setImportFromSelect(importFrom);
    };



    useEffect(() => {
        dispatch(fetchWorkspaces());
    }, [dispatch]);

    return (
        <div>
            <div className="export-import-container">
                <h2>Select Source of Import</h2>


                <div className="select-workspace-to-import">


                    <label htmlFor="import-from">Import From</label>
                    <ZoobbeSelect
                        options={importFromOptions}
                        defaultSelectedOption={0}
                        onSelect={handleImportFromSelect}
                    />

                    <label htmlFor="visibility-type">Visibility</label>
                    <ZoobbeSelect
                        options={visibilityOptions}
                        defaultSelectedOption={0}
                        onSelect={handleVisibilitySelect}
                    />


                    <label htmlFor="workspace-type">Workspace*</label>
                    <ZoobbeSelect
                        options={workspaceOptions}
                        defaultSelectedOption={0}
                        onSelect={handleWorkspaceSelect}
                    />

                </div>

                <div className='import-board-list'>
                    {boards.map((board) => (

                        <div className='board-checkbox' key={board.id}>
                            <Checkbox
                                checked={selectedBoards[board.id] || false}
                                onChange={() => toggleBoardSelection(board.id)}
                                label={board.name}
                            />
                        </div>
                    ))}
                </div>

                {
                    (importFromSelect.value == 'trello' && !token) ?
                        <button className='auth-button' onClick={authenticateTrello}>Continiue</button>
                        : <button className='auth-button' onClick={importBoards}>Import Selected Boards</button>
                }


                {/* Progress bar and status */}
                {progress !== '0' && (
                    <div className="progress-bar-container">
                        <div className="progress-bar-header">
                            <div className={`import-titles${importSuccess ? ' import-success' : ''}`}>
                                <div className="icon-bar">
                                    {importSuccess ? (
                                        <span className="material-symbols-outlined success-icon">
                                            check_circle
                                        </span>
                                    ) : (
                                        <>
                                            <span className="material-symbols-outlined import-icon">import_export</span>
                                            {!importSuccess && <Spinner size={40} color="#3498db" speed={1.5} />}
                                        </>
                                    )}
                                </div>

                                <span className='title'>
                                    {importSuccess ? 'Successfully Imported' : log.title || 'Importing...'}
                                </span>
                            </div>
                            <div className="items-imported">{log.index} / {log.length}</div>
                        </div>
                        <div className="progress-bar">
                            <div
                                className="progress-bar-fill"
                                style={{ width: `${progress}%` }}
                            ></div>
                        </div>
                        <div className="progress-bar-percentage">{progress.toFixed(0)}%</div>
                    </div>
                )}
            </div>

        </div>
    );
}

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { showToast } from '../../redux/Slices/toastSlice';
import { config } from '../../config';

const JoinBoard = () => {
    const { boardId } = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { loggedIn } = useSelector((state) => state.user);
    const [joining, setJoining] = useState(false);

    // Extract token from query parameters
    const queryParams = new URLSearchParams(location.search);
    const token = queryParams.get('token');

    useEffect(() => {
        if (!token) {
            dispatch(showToast({ message: 'Invalid link: Missing token', type: 'error' }));
            navigate('/'); // Redirect to home or another page
            return;
        }

        const joinBoard = async () => {
            setJoining(true);
            try {
                const response = await fetch(`${config.API_URI}/api/boards/join-board`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
                    },
                    credentials: 'include',
                    body: JSON.stringify({ boardId, token }),
                });

                const data = await response.json();

                if (response.ok) {
                    dispatch(showToast({ message: 'Successfully joined the board', type: 'success' }));
                    navigate(`/b/${boardId}`);
                } else {
                    dispatch(showToast({ message: data.message || 'Failed to join board', type: 'error' }));
                    // navigate('/');
                }
            } catch (error) {
                dispatch(showToast({ message: 'Error joining board', type: 'error' }));
                // navigate('/');
            } finally {
                setJoining(false);
            }
        };

        joinBoard();

    }, [token, boardId, loggedIn, dispatch, navigate]);

    if (joining) {
        return <div>Joining board...</div>;
    }

    return null; // or a loading component while joining
};

export default JoinBoard;

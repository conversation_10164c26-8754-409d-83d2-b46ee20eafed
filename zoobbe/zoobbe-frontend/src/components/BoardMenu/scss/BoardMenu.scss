.menu-container {
    color: var(--white-text-color-alternative);
    padding: 20px 0px;
    width: 300px;
    border-radius: 8px;
    background-color: var(--popover-background-color);
    border: 1px solid var(--popover-border-color);
    box-shadow: var(--popover-box-shadow);
    position: fixed;
    right: 0;
    top: 52px;
    z-index: 3;
    overflow: hidden;
    height: calc(100vh - 140px);
    max-height: 100%;
    border-top-right-radius: 0;

    .menu-inner-content {
        transition: transform 0.3s ease-in-out; /* Animation for sliding */
    }

    &.slide-in {
        transform: translateX(0);
    }

    &.slide-out {
        transform: translateX(-100%);
    }

    .menu-title {
        font-size: 15px;
        font-weight: 500;
        margin: 0;
        text-align: center;
    }

    .menu-section {
        padding: 0;
        margin: 0;

        .menu-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 8px;
            font-size: 14px;

            &:hover {
                background: var(--board-menu-item-background-color);
            }

            .menu-icon {
                width: 25px;
                height: 25px;
                font-size: 20px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: var(--brand-color);
                background: var(--menu-item-icon-background-color);
            }
        }
        li.menu-item.deactivated span {
            color: #5d5d5d;
            background: none;
        }
    }

    .menu-divider {
        border-top: 1px solid var(--menu-divider-color);
        margin: 15px 0;
    }

    .menu-screen {
        display: flex;
        flex-direction: column;
        height: 100%;

        .back-button {
            background: none;
            border: none;
            color: var(--brand-color);
            font-size: 16px;
            cursor: pointer;
            display: flex;
            position: absolute;
            left: 10px;
        }
        .close-button {
            background: none;
            border: none;
            color: var(--brand-color);
            font-size: 16px;
            cursor: pointer;
            display: flex;
            position: absolute;
            right: 10px;

            span.material-symbols-outlined {
                font-size: 22px;
            }
        }

        .screen {
            text-align: center;
            font-size: 18px;
        }

        .add-attachment {
            padding: 0px;
            width: 100%;

            h3.background-group-title {
                padding: 0 20px;
            }
        }
    }

    .menu-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
    }

    h3.background-group-title a {
        color: var(--brand-color);
    }
}

.unsplash-images {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
    padding: 0 20px;
    margin-top: 5px;

    img {
        width: 100%;
        height: 100px;
        object-fit: cover;
    }
}

.save-unsplash-background {
    position: absolute;
    top: 85px;
    width: 100%;
    z-index: 1;
    left: 0;
    button {
        padding: 0px 20px;
        border: none;
        border-radius: var(--element-border-radius);
        color: var(--primary-text-color);
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        height: 35px;
        width: 100%;
        background: var(--single-card-side-action-bg-color);
        margin: 0 20px;
        margin-top: 0;
        font-weight: 600;

        &:hover {
            background: var(--single-card-action-button-hover-color);
        }
    }
}

.unsplash-background-images {
    overflow-y: auto;
    height: calc(100vh - 200px);
    margin-top: 55px;
}
.unsplash-background-images.custom {
    height: calc(100vh - 315px);
}

.board-background-select {
    position: relative;
    width: 100%;
    height: 100px; /* Adjust height as needed */
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Optional shadow effect */

    .background-box {
        display: block;
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        position: relative;
        cursor: pointer;
        background-color: var(--single-card-action-button-hover-color);

        .checkmark {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 30px;
            height: 30px;
            background: var(--brand-color);
            color: var(--background-box-checkmark-color);
            font-size: 14px;
            line-height: 20px;
            border-radius: 50%;
            text-align: center;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            text-align: center;
            justify-content: center;
        }

        .large {
            position: absolute;
            bottom: 5px;
            left: 0px;
            background-color: var(--background-box-large-text-background-color);
            padding: 5px 12px;
            border-radius: 5px;
            left: 50%;
            transform: translate(-50%, 0%);
            text-align: center;
            width: 100%;
            margin-bottom: -100px;
            transition: 0.2s;
            font-size: 12px;
            font-weight: 400;

            a {
                color: var(--white);
                font-size: 12px;
                font-weight: 400;
                text-decoration: none;
                transition: color 0.3s ease;

                &:hover {
                    color: var(--background-box-large-text-hover-color); /* Highlight color on hover */
                    text-decoration: underline;
                }
            }
        }

        &:hover {
            .large {
                margin: 0;
            }
        }
    }
}

.board-background-select.selected {
    outline: 2px solid var(--brand-color);

    span.background-box:after {
        position: absolute;
        left: 0;
        right: 0;
        content: "";
        bottom: 0;
        top: 0;
        background: var(--background-box-hover-overlay-color);
        z-index: 1;
    }
}

.unsplash-images.background-image-screens span {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--single-card-side-action-bg-color);

    &:hover {
        background-color: var(--single-card-action-button-hover-color);
    }
}

.choose-backgrounds {
    padding: 0 20px;
}

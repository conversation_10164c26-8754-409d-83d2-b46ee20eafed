import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { uploadBackground, resetStatus } from '../../../redux/Slices/attachmentSlice';
import { hideSnackBar, showSnackBar } from '../../../redux/Slices/snackbarSlice';
import { config } from '../../../config';
import { fetchBoardById } from '../../../redux/Slices/boardSlice';

import Spinner from '../../Global/Spinner';

const Background = () => {
    const [attachmentUrl, setAttachmentUrl] = useState('');
    const [isUrlValid, setIsUrlValid] = useState(true);
    const [isTextareaFocused, setIsTextareaFocused] = useState(false);
    const [unsplashImages, setUnsplashImages] = useState([]);
    const [isLoadingUnsplash, setIsLoadingUnsplash] = useState(false);
    const [page, setPage] = useState(1); // Current page for Unsplash API
    const [hasMoreImages, setHasMoreImages] = useState(true);
    const { status } = useSelector((state) => state.attachments);
    const { board } = useSelector((state) => state.board);

    const urlRef = useRef(null);
    const observer = useRef();
    const fileInputRef = useRef(null);

    const boardId = board.shortId;
    const dispatch = useDispatch();

    useEffect(() => {
        if (urlRef.current) {
            urlRef.current.style.height = '22px';
            urlRef.current.style.height = `${urlRef.current.scrollHeight}px`;
        }
    }, [attachmentUrl]);

    useEffect(() => {
        if (status === 'uploadSucceeded') {
            dispatch(hideSnackBar());
        } else if (status === 'uploadFailed') {
            dispatch(showSnackBar({
                message: 'File upload failed',
                type: 'error',
            }));
        }
    }, [status, dispatch, boardId]);

    // Fetch Unsplash images
    useEffect(() => {
        fetchUnsplashImages(page); // Fetch initial images
    }, [page]);

    const fetchUnsplashImages = async (page) => {
        if (!hasMoreImages) return;

        setIsLoadingUnsplash(true);
        try {
            const token = localStorage.getItem('accessToken');
            const response = await fetch(`${config.API_URI}/api/unsplash?page=${page}&limit=30`, {
                method: 'GET',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch images: ${response.statusText}`);
            }

            const data = await response.json();
            if (!data.success || !Array.isArray(data.photos)) {
                throw new Error('Unexpected response format from the server');
            }

            const formattedImages = data.photos;

            setUnsplashImages((prevImages) => [...prevImages, ...formattedImages]);

            if (data.photos.length === 0) {
                setHasMoreImages(false); // Stop fetching if no more images
            }
        } catch (error) {
            console.error('Error fetching Unsplash images:', error);
        } finally {
            setIsLoadingUnsplash(false);
        }
    };

    const lastImageRef = useCallback((node) => {
        if (isLoadingUnsplash) return;
        if (observer.current) observer.current.disconnect();

        observer.current = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting && hasMoreImages) {
                setPage((prevPage) => prevPage + 1);
            }
        });

        if (node) observer.current.observe(node);
    }, [isLoadingUnsplash, hasMoreImages]);


    const validateImageUrl = (url) => {
        const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i;
        return imageExtensions.test(url);
    };

    const handleUrlChange = (e) => {
        const url = e.target.value;
        setAttachmentUrl(url);
        setIsUrlValid(validateImageUrl(url));
    };


    const handleUnsplashImageSelect = async (url) => {
        dispatch(showSnackBar({ message: 'Setting background...', type: 'uploading' }));

        try {
            const token = localStorage.getItem('accessToken');

            if (!token) {
                throw new Error('User is not authenticated. Access token is missing.');
            }

            const response = await fetch(`${config.API_URI}/api/boards/boards-background`, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                credentials: 'include',
                body: JSON.stringify({ url, boardId }),
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to set background: ${response.status} ${errorText}`);
            }

            // Refresh the board data after successful upload
            dispatch(fetchBoardById(boardId));
            dispatch(hideSnackBar());
        } catch (error) {
            console.error('Error setting background:', error);
            dispatch(showSnackBar({ message: 'Failed to set background', type: 'error' }));
        }
    };


    const handleFileUpload = async (e) => {
        console.log('File input changed');
        const file = e.target.files[0];

        if (!file) {
            console.log('No file selected');
            return;
        }

        console.log('File selected:', file);

        dispatch(showSnackBar({ message: 'Uploading files...', type: 'uploading' }));

        const response = await dispatch(uploadBackground({ file, id: boardId, apiPath: 'boards' }));

        if (response.meta.requestStatus === 'fulfilled') {
            console.log('File upload successful');
            dispatch(fetchBoardById(boardId));
            dispatch(hideSnackBar());
        } else {
            console.log('File upload failed');
            dispatch(showSnackBar({ message: 'File upload failed', type: 'error' }));
        }
    };

    const attachmentAcceptType = 'file';

    return (
        <div className="add-attachment">
            <div className="group">
                <h3>Attach URL</h3>
                <div className={`textarea-wrapper ${isTextareaFocused ? 'focused' : ''}`}>
                    <textarea
                        ref={urlRef}
                        type="url"
                        placeholder="Enter attachment URL"
                        value={attachmentUrl}
                        onChange={handleUrlChange}
                        rows={1}
                        style={{ overflow: 'hidden' }}
                        spellCheck={false}
                        onFocus={() => setIsTextareaFocused(true)}
                        onBlur={() => setIsTextareaFocused(false)}
                    />
                </div>
                {!isUrlValid && <p className="error-message">Please enter a valid URL.</p>}
                <button disabled={!isUrlValid}>Submit</button>
            </div>

            <div className="group">
                <h3>or Upload a file from your computer</h3>
                <p>Click the 'Choose a file' button to browse and select a file from your computer.</p>
                <button onClick={() => fileInputRef.current.click()} className="attach-file-btn">
                    {status === 'loading' ? 'Uploading...' :
                        status === 'uploadSucceeded' ? 'Upload Successful' :
                            'Choose a file'
                    }
                </button>

                <input
                    type="file"
                    accept="image/*"
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    onChange={handleFileUpload}
                />
            </div>

            <div className="group">
                <h3 className='background-group-title'>or Choose an image from <a target='_blank' href='https://unsplash.com'>Unsplash</a></h3>

                <div className="unsplash-images unsplash-background-images">
                    {unsplashImages.map((img, index) => {
                        const isLastImage = index === unsplashImages.length - 1;

                        return (
                            <div
                                className="board-background-select"
                                key={img.id}
                                ref={isLastImage ? lastImageRef : null}
                            >
                                <span
                                    className="background-box"
                                    data-testid={`board-background-select-photo-${index}`}
                                    style={{ backgroundImage: `url(${img.urls.thumb})` }}
                                    onClick={() => handleUnsplashImageSelect(img.urls.regular)}
                                >
                                    <div className="large">
                                        <a
                                            href={img.user.links.html}
                                            target="_blank"
                                            title={img.user.name}
                                        >
                                            {img.user.name}
                                        </a>
                                    </div>
                                </span>
                            </div>
                        );
                    })}
                </div>
                {isLoadingUnsplash && (
                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80px' }}>
                        <Spinner size={30} color="#3498db" speed={1.5} />
                    </div>
                )}

            </div>
        </div>
    );
};

export default Background;

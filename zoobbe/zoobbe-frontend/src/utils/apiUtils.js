import { config } from '../config';
import { refreshAccessToken } from './helpers';

// Cache for storing API responses
const apiCache = new Map();

/**
 * Enhanced fetch function with authentication, caching, and error handling
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @param {boolean} useCache - Whether to use cache for this request
 * @param {number} cacheDuration - How long to cache the response in milliseconds
 * @returns {Promise<any>} - The response data
 */
export const apiFetch = async (url, options = {}, useCache = false, cacheDuration = 60000) => {
  // Add authentication token to headers if available
  const token = localStorage.getItem('accessToken');
  
  // Prepare headers with authentication
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers,
  };

  // Prepare the complete options
  const fetchOptions = {
    credentials: 'include',
    ...options,
    headers,
  };

  // Create a cache key based on the URL and options
  const cacheKey = useCache ? `${url}-${JSON.stringify(fetchOptions)}` : null;

  // Check if we have a cached response and it's not expired
  if (useCache && cacheKey && apiCache.has(cacheKey)) {
    const cachedData = apiCache.get(cacheKey);
    if (cachedData.expiry > Date.now()) {
      return cachedData.data;
    }
    // Remove expired cache entry
    apiCache.delete(cacheKey);
  }

  try {
    const response = await fetch(url, fetchOptions);

    // Handle authentication errors
    if (response.status === 401) {
      // Try to refresh the token
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        // Retry the request with the new token
        const newToken = localStorage.getItem('accessToken');
        const newHeaders = {
          ...headers,
          'Authorization': `Bearer ${newToken}`,
        };
        
        const retryOptions = {
          ...fetchOptions,
          headers: newHeaders,
        };
        
        const retryResponse = await fetch(url, retryOptions);
        if (!retryResponse.ok) {
          throw new Error(`API error: ${retryResponse.status}`);
        }
        
        const data = await retryResponse.json();
        
        // Cache the successful response if caching is enabled
        if (useCache && cacheKey) {
          apiCache.set(cacheKey, {
            data,
            expiry: Date.now() + cacheDuration,
          });
        }
        
        return data;
      } else {
        // If token refresh failed, redirect to login
        window.location.href = '/login';
        throw new Error('Authentication failed');
      }
    }

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Cache the successful response if caching is enabled
    if (useCache && cacheKey) {
      apiCache.set(cacheKey, {
        data,
        expiry: Date.now() + cacheDuration,
      });
    }
    
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

/**
 * Clear all cached API responses
 */
export const clearApiCache = () => {
  apiCache.clear();
};

/**
 * Clear specific cached API response
 * @param {string} url - The URL to clear from cache
 */
export const clearApiCacheForUrl = (url) => {
  for (const key of apiCache.keys()) {
    if (key.startsWith(url)) {
      apiCache.delete(key);
    }
  }
};

/**
 * API methods for common operations
 */
export const api = {
  get: (url, options = {}, useCache = false, cacheDuration = 60000) => 
    apiFetch(url, { ...options, method: 'GET' }, useCache, cacheDuration),
  
  post: (url, data, options = {}) => 
    apiFetch(url, { ...options, method: 'POST', body: JSON.stringify(data) }),
  
  put: (url, data, options = {}) => 
    apiFetch(url, { ...options, method: 'PUT', body: JSON.stringify(data) }),
  
  delete: (url, options = {}) => 
    apiFetch(url, { ...options, method: 'DELETE' }),
};

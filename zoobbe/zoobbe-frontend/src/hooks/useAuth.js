import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { fetchUser } from '../redux/Slices/thunks';
import { config } from '../config';
import { api } from '../utils/apiUtils';

/**
 * Custom hook for authentication management
 * Provides functions for login, logout, token refresh, and auth state
 */
const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [authError, setAuthError] = useState(null);
  const dispatch = useDispatch();

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Check if user is authenticated
  const checkAuthStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        setIsAuthenticated(false);
        setIsLoading(false);
        return false;
      }

      // Verify token with backend
      const response = await api.get(`${config.API_URI}/api/users/me`, {}, false);
      
      if (response && response.valid) {
        setIsAuthenticated(true);
        // Fetch user data to keep Redux store updated
        dispatch(fetchUser());
        setIsLoading(false);
        return true;
      } else {
        // Try to refresh the token
        const refreshed = await refreshToken();
        if (refreshed) {
          setIsAuthenticated(true);
          dispatch(fetchUser());
          setIsLoading(false);
          return true;
        } else {
          setIsAuthenticated(false);
          setIsLoading(false);
          return false;
        }
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setAuthError(error.message);
      setIsAuthenticated(false);
      setIsLoading(false);
      return false;
    }
  }, [dispatch]);

  // Login function
  const login = useCallback(async (credentials) => {
    setIsLoading(true);
    setAuthError(null);
    
    try {
      const response = await api.post(`${config.API_URI}/api/users/login`, credentials);
      
      if (response && response.accessToken) {
        localStorage.setItem('accessToken', response.accessToken);
        setIsAuthenticated(true);
        dispatch(fetchUser());
        setIsLoading(false);
        return { success: true, user: response.user };
      } else {
        throw new Error('Login failed: No token received');
      }
    } catch (error) {
      console.error('Login error:', error);
      setAuthError(error.message || 'Login failed');
      setIsAuthenticated(false);
      setIsLoading(false);
      return { success: false, error: error.message || 'Login failed' };
    }
  }, [dispatch]);

  // Logout function
  const logout = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Call logout endpoint
      await api.post(`${config.API_URI}/api/users/logout`);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API response
      localStorage.removeItem('accessToken');
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  }, []);

  // Refresh token function
  const refreshToken = useCallback(async () => {
    try {
      const response = await api.get(`${config.API_URI}/api/users/refresh-token`, {}, false);
      
      if (response && response.accessToken) {
        localStorage.setItem('accessToken', response.accessToken);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  }, []);

  return {
    isAuthenticated,
    isLoading,
    authError,
    login,
    logout,
    refreshToken,
    checkAuthStatus
  };
};

export default useAuth;

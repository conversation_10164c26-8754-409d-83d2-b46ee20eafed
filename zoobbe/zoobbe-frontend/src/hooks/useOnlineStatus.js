import { useState, useEffect } from "react";
import { io } from "socket.io-client";
import { config } from "../config";
import { useSelector } from "react-redux";

const socket = io(config.API_URI);

const useOnlineStatus = (memberId) => {
    const [isOnline, setIsOnline] = useState(false);
    const { user } = useSelector((state) => state.user);

    const userId = user?.user._id;


    useEffect(() => {
        if (!userId) return;

        // Notify the server that the user is online after connecting
        socket.emit("join", userId);

        const handleStatusChange = (data) => {

            console.log({ data });

            if (data.userId === userId) {
                setIsOnline(data.online);
            }
        };

        socket.on("user-online-status", handleStatusChange);

        return () => {
            socket.off("user-online-status", handleStatusChange);
        };
    }, [user]); // ✅ Updates when `userId` changes

    return isOnline;
};

export default useOnlineStatus;
